# 📊 Charts Screen

## 🎯 Vision
Экран графиков в Project T является уникальным и передовым продуктом, который отбирает только важные и достоверные рыночные данные без бесполезного потока визуальной информации. Он открывает перед пользователями доступ к криптовалютным данным, собранным из проверенных источников, и обогащает детальным анализом. Каждая криптовалюта словно проходит через призму, разделяясь на растущие, падающие и стабильные активы, чтобы пользователи могли мгновенно уловить рыночные тренды используя проверенные источники. AI агент интерпретирует рыночные данные, создаёт уникальные индикаторы настроения, убирает лишнее и оптимизирует отображение портфолио.

## 🎯 Mission
Улучшение алгоритмов поиска, фильтрации, анализа рыночной информации для ещё большего криптовалютного охвата и полной независимости от внешних источников данных.

---

## 📊 Основные компоненты экрана

### 1. **Horizontal Crypto Carousel** - Горизонтальная карусель топ криптовалют
```
🪙 Bitcoin (BTC)     📈 $108,450  +2.3%
🪙 Ethereum (ETH)    📈 $3,890    +1.8%
🪙 Solana (SOL)      📈 $185      +4.2%
🪙 Cardano (ADA)     📉 $0.89     -0.5%
🪙 Polygon (MATIC)   📈 $1.12     +3.1%
```

### 2. **Market Sentiment Indicators** - Индикаторы настроения рынка
```
😨 Fear & Greed Index: 73/100 (Greed)
📊 Crypto Sentiment: 68.5/100 (Bullish)
🔄 Altcoin Season: 45.2/100 (Neutral)
📈 Market Trend: 67% UP, 33% DOWN
```

### 3. **Crypto Grid List** - Сетка криптовалют с мини-графиками
```
#1  🪙 BTC  $108,450  📈 +2.3%  [mini chart]
#2  🪙 ETH  $3,890    📈 +1.8%  [mini chart]
#3  🪙 SOL  $185      📈 +4.2%  [mini chart]
#4  🪙 ADA  $0.89     📉 -0.5%  [mini chart]
```

### 4. **Search & Filter System** - Поиск и фильтрация
```
🔍 Search: "bitcoin"
🏷️ Filters: All | Top 10 | Top 50 | Top 100
📊 Sort: Market Cap | Price | 24h Change
🔄 Layout: 2 columns | 3 columns
```

### 5. **Pagination Controls** - Управление страницами
```
⬅️ Previous | Page 1 / 20 | Next ➡️
📄 50 items per page
🔝 Back to top button
```

---

## 🔧 Техническая архитектура

### **Charts Screen Architecture**
```
📱 ChartsScreen ←→ 🔄 CryptoProvider ←→ 📊 CoinGecko API ←→ 💾 Local Cache
                ←→ 📈 MiniChart Widgets ←→ 🎨 CustomPainter ←→ 🖼️ UI Rendering
```

### **Core Components**
- **ChartsScreen**: Главный экран с каруселью и списком криптовалют
- **MiniChart**: Компактные графики для каждой криптовалюты в списке
- **CryptoProvider**: State management для данных криптовалют
- **FearGreedGauge**: Виджет индикатора страха и жадности
- **CosmicBackground**: Анимированный фон с градиентами
- **AppBottomNavigation**: Нижняя навигация между экранами

### **Real-time Data Pipeline**
```dart
// CryptoProvider data management
class CryptoProvider extends ChangeNotifier {
  List<CryptoCurrency> _allCryptos = [];
  bool _isLoading = false;

  Future<void> loadAllCryptos() async {
    _isLoading = true;
    notifyListeners();

    final cryptos = await _cryptoService.fetchTopCryptos(250);
    _allCryptos = cryptos;
    _isLoading = false;
    notifyListeners();
  }
}
```

---

## 🎮 Интерактивные возможности

### **Search & Navigation System**
```dart
// Real-time search with debounce
class _ChartsScreenState extends State<ChartsScreen> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _searchDebounceTimer;

  void _onSearchChanged() {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(Duration(milliseconds: 300), () {
      _performSearch(_searchController.text);
    });
  }
}
```

### **Interactive Features**
- 🔍 **Real-time Search**: Поиск криптовалют с debounce (300ms)
- 🎯 **Tap Navigation**: Переход к детальному экрану криптовалюты
- 📊 **Dynamic Filtering**: Фильтры Top 10, Top 50, Top 100, All
- 🔄 **Auto-refresh**: Автоматическое обновление данных каждые 30 секунд
- 📱 **Responsive Layout**: Адаптивное количество колонок (2-3)
- 🎨 **Sentiment Colors**: Динамические цвета на основе рыночного настроения

---

## 📊 Компоненты UI

### 1. **Crypto Carousel Cards** - Карточки в горизонтальной карусели
```dart
class CryptoCarouselCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: cardWidth,
      margin: EdgeInsets.symmetric(horizontal: 10.0, vertical: 14.0),
      padding: EdgeInsets.all(18.0),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.04),
        borderRadius: BorderRadius.circular(16.0),
        border: Border.all(color: Colors.white.withOpacity(0.18)),
      ),
      child: Column(
        children: [
          Row(children: [
            CircleAvatar(radius: 12, backgroundImage: NetworkImage(crypto.imageUrl)),
            SizedBox(width: 6),
            Text(crypto.symbol, style: TextStyle(color: Colors.white, fontSize: 12)),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
              decoration: BoxDecoration(
                color: trendColor.withOpacity(0.25),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text('${crypto.priceChangePercentage24h.toStringAsFixed(1)}%'),
            ),
          ]),
          Spacer(),
          Text('\$${_formatPrice(crypto.price)}',
               style: TextStyle(color: Colors.white, fontSize: 15.0)),
        ],
      ),
    );
  }
}
```

### 2. **Crypto List Items** - Элементы основного списка
```dart
class CryptoListItem extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.0),
      padding: EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Row(
        children: [
          Text('#${rank}', style: TextStyle(color: Colors.grey[400], fontSize: 12)),
          SizedBox(width: 8),
          CircleAvatar(radius: 16, backgroundImage: NetworkImage(crypto.imageUrl)),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(crypto.name, style: TextStyle(color: Colors.white, fontSize: 14)),
                Text(crypto.symbol, style: TextStyle(color: Colors.grey[400], fontSize: 12)),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text('\$${_formatPrice(crypto.price)}',
                   style: TextStyle(color: Colors.white, fontSize: 14)),
              Text('${crypto.priceChangePercentage24h.toStringAsFixed(2)}%',
                   style: TextStyle(color: trendColor, fontSize: 12)),
            ],
          ),
          SizedBox(width: 8),
          Container(
            width: 60,
            height: 30,
            child: MiniChart(crypto: crypto),
          ),
        ],
      ),
    );
  }
}
```

---

## ⚡ Производительность и оптимизация

### **Performance Metrics**
```
📊 Рендеринг: 60 FPS для списка криптовалют
⚡ Загрузка: <2 секунды для 250 криптовалют
💾 Память: <30MB для полного списка
🔄 Обновления: Каждые 30 секунд автоматически
📱 Поддержка: iOS, Android, Web, Desktop
```

### **Pagination System**
```dart
class PaginationController {
  static const int _itemsPerPage = 50;
  int _currentPage = 0;

  List<CryptoCurrency> getCurrentPageItems(List<CryptoCurrency> allCryptos) {
    final pageStart = _currentPage * _itemsPerPage;
    final pageEnd = math.min(pageStart + _itemsPerPage, allCryptos.length);
    return allCryptos.sublist(pageStart, pageEnd);
  }
}
```

### **Search Optimization**
```dart
// Debounced search для оптимизации производительности
class SearchController {
  Timer? _searchDebounceTimer;

  void onSearchChanged(String query) {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(Duration(milliseconds: 300), () {
      _performSearch(query);
    });
  }
}
```

### **Memory Management**
```dart
// Автоматическая очистка ресурсов
@override
void dispose() {
  _scrollController.dispose();
  _searchController.dispose();
  _searchFocusNode.dispose();
  _refreshTimer?.cancel();
  _scrollTimer?.cancel();
  _searchDebounceTimer?.cancel();
  super.dispose();
}
```

---

## 🎨 UI/UX Design

### **Dynamic Dark Theme**
```dart
final chartsTheme = ChartsTheme(
  backgroundColor: Color(0xFF0A0A0A),
  cardColor: Colors.white.withOpacity(0.05),
  borderColor: Colors.white.withOpacity(0.1),
  textColor: Colors.white,
  secondaryTextColor: Colors.grey[400],
  bullishColor: Color(0xFF34C759),
  bearishColor: Color(0xFFFF3B30),
  neutralColor: Color(0xFF4A90E2),
);
```

### **Responsive Grid Layout**
```dart
class ResponsiveCryptoGrid extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final columnsCount = screenWidth > 600 ? 3 : 2;

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columnsCount,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
        childAspectRatio: 1.2,
      ),
      itemBuilder: (context, index) => CryptoListItem(crypto: cryptos[index]),
    );
  }
}
```

### **Sentiment-Based Color System**
```dart
class SentimentColorCalculator {
  static Color calculateSentimentColor(List<CryptoCurrency> cryptos) {
    int upCount = cryptos.where((c) => c.priceChangePercentage24h > 0).length;
    int downCount = cryptos.where((c) => c.priceChangePercentage24h < 0).length;
    double upPercent = upCount / cryptos.length;

    if (upPercent > 0.6) return Color(0xFF34C759); // Зеленый (бычий)
    else if (upPercent < 0.4) return Color(0xFFFF3B30); // Красный (медвежий)
    else return Color(0xFF4A90E2); // Нейтральный
  }
}
```

### **Smooth Scroll Animations**
```dart
class AutoScrollCarousel extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scrollController,
      builder: (context, child) {
        return ListView.builder(
          controller: _scrollController,
          scrollDirection: Axis.horizontal,
          physics: BouncingScrollPhysics(),
          itemBuilder: (context, index) => CryptoCarouselCard(crypto: cryptos[index]),
        );
      },
    );
  }
}
```

---

## 🚀 Конкурентные преимущества

### **1. Comprehensive Market Overview**
- 📊 **250+ Cryptocurrencies**: Полный охват топ криптовалют
- 🎨 **Real-time Data**: Актуальные данные с CoinGecko API
- ⚡ **Fast Performance**: Оптимизированный рендеринг списков

### **2. Advanced Search & Filter System**
- 🔍 **Instant Search**: Поиск с debounce для быстрого отклика
- 📱 **Smart Filters**: Top 10, Top 50, Top 100, All categories
- 🔄 **Dynamic Sorting**: По рыночной капитализации, цене, изменению

### **3. Market Sentiment Integration**
- 📡 **Fear & Greed Index**: Индикатор страха и жадности рынка
- 💾 **Crypto Sentiment**: Собственный индекс настроения
- 🎮 **Altcoin Season**: Индикатор сезона альткоинов

### **4. User Experience Features**
- 🔮 **Pagination System**: Эффективная загрузка больших списков
- 🎯 **Responsive Design**: Адаптивная сетка 2-3 колонки
- 📊 **Mini Charts**: Компактные графики в каждой карточке
- 🎨 **Dynamic Colors**: Цвета интерфейса зависят от рыночного настроения

---

## 📈 Technical Capabilities

```
🎨 UI Components: Carousel, Grid List, Search, Filters, Pagination
📊 Data Sources: CoinGecko API, Fear & Greed Index, Sentiment APIs
⏱️ Update Frequency: Auto-refresh every 30 seconds
🎮 Interactivity: Search, Filter, Sort, Navigate, Refresh
🌐 Platforms: iOS, Android, Web, Desktop (Flutter)
🔄 State Management: Provider pattern with ChangeNotifier
💾 Performance: Pagination (50 items), Debounced search (300ms)
🎯 Data Volume: 250+ cryptocurrencies with real-time prices
```

---

## 🎯 Результат

**Charts Screen** предоставляет пользователям **комплексный обзор криптовалютного рынка** с **250+ активами**, **интеллектуальным поиском**, **рыночными индикаторами** и **высокой производительностью** на всех платформах.

Пользователи получают **мгновенный доступ** к **актуальным рыночным данным** с **интуитивной навигацией**, **динамическими фильтрами** и **адаптивным интерфейсом**, который меняется в зависимости от настроения рынка.
