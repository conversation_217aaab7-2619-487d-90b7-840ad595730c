# 🚀 Продакшн Развертывание News AI System

## 📋 Обзор

Полноценная продакшн система с:
- **Docker контейнеризация** всех сервисов
- **Nginx reverse proxy** с SSL поддержкой
- **Real-time SSE** для мгновенных обновлений
- **Redis кэширование** для производительности
- **Prometheus мониторинг** для отслеживания метрик
- **Rate limiting** и безопасность
- **Auto-scaling** готовность

## 🏗️ Архитектура

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Flutter Web   │────│    Nginx     │────│  News Backend   │
│   (Static)      │    │ (Proxy/SSL)  │    │   (Node.js)     │
└─────────────────┘    └──────────────┘    └─────────────────┘
                              │                       │
                              │                       │
                       ┌──────────────┐    ┌─────────────────┐
                       │    Redis     │    │   Prometheus    │
                       │  (Caching)   │    │ (Monitoring)    │
                       └──────────────┘    └─────────────────┘
```

## 🛠️ Быстрый Старт

### 1. Подготовка

```bash
# Клонируйте репозиторий
git clone <your-repo>
cd flutter

# Создайте продакшн конфигурацию
cp news-backend/.env.production.example news-backend/.env.production
```

### 2. Настройка API ключей

Отредактируйте `news-backend/.env.production`:

```env
DEEPSEEK_API_KEY=your_deepseek_api_key_here
NEWS_API_KEY=your_news_api_key_here
CRYPTO_COMPARE_API_KEY=your_crypto_compare_api_key_here
CORS_ORIGIN=https://your-domain.com
```

### 3. Развертывание

```bash
# Сделайте скрипт исполняемым
chmod +x deploy.sh

# Запустите развертывание
./deploy.sh
```

## 🔧 Ручное Развертывание

### 1. Сборка Flutter приложения

```bash
flutter build web --release --web-renderer html
```

### 2. Установка зависимостей бэкенда

```bash
cd news-backend
npm ci --only=production
```

### 3. Запуск Docker Compose

```bash
docker-compose -f docker-compose.prod.yml up --build -d
```

## 📊 Мониторинг и Управление

### Доступные Сервисы

- **Web App**: http://your-domain.com
- **API**: http://your-domain.com/api
- **Prometheus**: http://your-domain.com:9090
- **Health Check**: http://your-domain.com/health

### Полезные Команды

```bash
# Просмотр логов
docker-compose -f docker-compose.prod.yml logs -f news-backend

# Перезапуск сервиса
docker-compose -f docker-compose.prod.yml restart news-backend

# Масштабирование
docker-compose -f docker-compose.prod.yml up --scale news-backend=3 -d

# Остановка всех сервисов
docker-compose -f docker-compose.prod.yml down

# Обновление без даунтайма
docker-compose -f docker-compose.prod.yml up --build --no-deps -d news-backend
```

### Мониторинг Метрик

```bash
# Статус всех сервисов
curl http://localhost/api/status

# Количество подключенных SSE клиентов
curl http://localhost/api/status | jq '.connectedClients'

# Общее количество новостей
curl http://localhost/api/status | jq '.totalNews'
```

## 🔒 Безопасность

### Настроенные Меры Безопасности

1. **Helmet.js** - защита заголовков HTTP
2. **Rate Limiting** - ограничение запросов
3. **CORS** - контроль доступа
4. **SSL/TLS** - шифрование трафика
5. **Non-root пользователь** в Docker
6. **Валидация входных данных**

### SSL Сертификаты

```bash
# Для Let's Encrypt
certbot certonly --webroot -w /var/www/html -d your-domain.com

# Копируйте сертификаты
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/cert.pem
cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/key.pem

# Перезапустите nginx
docker-compose -f docker-compose.prod.yml restart nginx
```

## 📈 Масштабирование

### Горизонтальное Масштабирование

```bash
# Запуск нескольких инстансов бэкенда
docker-compose -f docker-compose.prod.yml up --scale news-backend=3 -d
```

### Вертикальное Масштабирование

Отредактируйте `docker-compose.prod.yml`:

```yaml
news-backend:
  deploy:
    resources:
      limits:
        cpus: '2.0'
        memory: 4G
      reservations:
        cpus: '1.0'
        memory: 2G
```

## 🚨 Troubleshooting

### Частые Проблемы

1. **SSE не работает**
   ```bash
   # Проверьте nginx конфигурацию
   docker exec news-nginx-prod nginx -t
   
   # Проверьте логи
   docker-compose -f docker-compose.prod.yml logs nginx
   ```

2. **API ключи не работают**
   ```bash
   # Проверьте переменные окружения
   docker exec news-backend-prod env | grep API
   ```

3. **Высокое потребление памяти**
   ```bash
   # Мониторинг ресурсов
   docker stats
   
   # Ограничьте кэш новостей
   # В .env.production: MAX_NEWS_CACHE_SIZE=500
   ```

### Логи и Отладка

```bash
# Все логи
docker-compose -f docker-compose.prod.yml logs -f

# Только бэкенд
docker-compose -f docker-compose.prod.yml logs -f news-backend

# Только nginx
docker-compose -f docker-compose.prod.yml logs -f nginx

# Логи с временными метками
docker-compose -f docker-compose.prod.yml logs -f -t
```

## 🔄 Обновления

### Обновление Кода

```bash
# Получите последние изменения
git pull origin main

# Пересоберите и перезапустите
docker-compose -f docker-compose.prod.yml up --build -d
```

### Обновление Зависимостей

```bash
cd news-backend
npm update
docker-compose -f docker-compose.prod.yml build --no-cache news-backend
docker-compose -f docker-compose.prod.yml up -d news-backend
```

## 📝 Backup и Восстановление

### Backup Данных

```bash
# Backup новостей
docker exec news-backend-prod cp /app/data/newsFeed.json /tmp/
docker cp news-backend-prod:/tmp/newsFeed.json ./backup/

# Backup Redis
docker exec news-redis-prod redis-cli BGSAVE
docker cp news-redis-prod:/data/dump.rdb ./backup/
```

### Восстановление

```bash
# Восстановление новостей
docker cp ./backup/newsFeed.json news-backend-prod:/app/data/
docker-compose -f docker-compose.prod.yml restart news-backend

# Восстановление Redis
docker cp ./backup/dump.rdb news-redis-prod:/data/
docker-compose -f docker-compose.prod.yml restart redis
```

## 🎯 Оптимизация Производительности

### Рекомендуемые Настройки

1. **Кэширование**: Используйте Redis для кэширования API ответов
2. **CDN**: Настройте CloudFlare или AWS CloudFront
3. **Gzip**: Включен в nginx конфигурации
4. **Keep-Alive**: Настроен для HTTP соединений
5. **Connection Pooling**: Используется для SSE соединений

### Мониторинг Производительности

- **Response Time**: < 200ms для API
- **SSE Latency**: < 100ms для real-time обновлений
- **Memory Usage**: < 512MB на инстанс
- **CPU Usage**: < 50% в нормальном режиме

---

🎉 **Поздравляем!** Ваша News AI система готова к продакшн использованию!
