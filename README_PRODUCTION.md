# 🚀 News AI System - Production Ready

## ⚡ Быстрый Старт (5 минут)

### 1. Подготовка API ключей

```bash
# Скопируйте шаблон конфигурации
cp news-backend/.env.production news-backend/.env

# Отредактируйте файл с вашими API ключами
nano news-backend/.env
```

Заполните:
- `DEEPSEEK_API_KEY` - получите на https://platform.deepseek.com
- `NEWS_API_KEY` - получите на https://newsapi.org
- `CRYPTO_COMPARE_API_KEY` - получите на https://cryptocompare.com

### 2. Запуск

```bash
# Автоматическое развертывание
./deploy.sh

# ИЛИ ручной запуск
docker-compose -f docker-compose.prod.yml up -d
```

### 3. Проверка

```bash
# Проверка здоровья системы
./healthcheck.sh

# Открыть в браузере
open http://localhost
```

## 🎯 Что Получаете

### ✅ Полнофункциональная Система
- **Real-time новости** с AI анализом настроения
- **Мгновенные обновления** через Server-Sent Events
- **Фильтрация и поиск** по настроению, тегам, тексту
- **Responsive дизайн** для всех устройств

### ✅ Production-Ready Инфраструктура
- **Docker контейнеризация** всех сервисов
- **Nginx reverse proxy** с SSL поддержкой
- **Redis кэширование** для производительности
- **Rate limiting** и безопасность
- **Health checks** и мониторинг
- **Auto-restart** при сбоях

### ✅ Мониторинг и Управление
- **Prometheus метрики** на http://localhost:9090
- **Real-time статистика** через API
- **Централизованные логи** через Docker
- **Health check endpoint** для балансировщиков

## 📊 Архитектура

```
Internet → Nginx → News Backend → AI APIs
    ↓         ↓         ↓
Flutter    Redis    Prometheus
  Web     Cache    Monitoring
```

## 🔧 Управление

### Основные Команды

```bash
# Просмотр статуса
docker-compose -f docker-compose.prod.yml ps

# Просмотр логов
docker-compose -f docker-compose.prod.yml logs -f

# Перезапуск сервиса
docker-compose -f docker-compose.prod.yml restart news-backend

# Остановка
docker-compose -f docker-compose.prod.yml down

# Обновление
git pull && docker-compose -f docker-compose.prod.yml up --build -d
```

### API Endpoints

- `GET /api/news` - получение новостей
- `GET /api/news/stream` - real-time поток (SSE)
- `POST /api/admin/parse` - запуск парсинга
- `GET /api/status` - статус системы

## 🔒 Безопасность

### Встроенная Защита
- ✅ **Helmet.js** - защита HTTP заголовков
- ✅ **Rate Limiting** - защита от DDoS
- ✅ **CORS** - контроль доступа
- ✅ **Input Validation** - валидация данных
- ✅ **Non-root containers** - изоляция процессов

### SSL/HTTPS Настройка

```bash
# Получение Let's Encrypt сертификата
certbot certonly --webroot -w /var/www/html -d your-domain.com

# Копирование в nginx
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/cert.pem
cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/key.pem

# Перезапуск nginx
docker-compose -f docker-compose.prod.yml restart nginx
```

## 📈 Масштабирование

### Горизонтальное

```bash
# Запуск нескольких инстансов бэкенда
docker-compose -f docker-compose.prod.yml up --scale news-backend=3 -d
```

### Вертикальное

Отредактируйте ресурсы в `docker-compose.prod.yml`:

```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 4G
```

## 🚨 Troubleshooting

### Частые Проблемы

1. **Порт 4000 занят**
   ```bash
   # Измените порт в docker-compose.prod.yml
   ports: ["4001:4000"]
   ```

2. **API ключи не работают**
   ```bash
   # Проверьте переменные окружения
   docker exec news-backend-prod env | grep API
   ```

3. **SSE не подключается**
   ```bash
   # Проверьте nginx конфигурацию
   docker exec news-nginx-prod nginx -t
   ```

### Получение Помощи

```bash
# Детальные логи
docker-compose -f docker-compose.prod.yml logs -f news-backend

# Статус всех контейнеров
docker ps

# Использование ресурсов
docker stats
```

## 🎯 Оптимизация

### Рекомендуемые Настройки

1. **Для высокой нагрузки**: увеличьте `MAX_NEWS_CACHE_SIZE=4000` (по умолчанию теперь 4000)
2. **Для экономии ресурсов**: уменьшите `PARSE_INTERVAL_MINUTES=60`
3. **Для быстрого отклика**: включите Redis кэширование
4. **Для стабильности**: настройте health checks

### Мониторинг Производительности

- **API Response Time**: < 200ms
- **SSE Latency**: < 100ms  
- **Memory Usage**: < 512MB per instance
- **News Processing**: ~10 news/minute

## 📝 Backup

```bash
# Backup данных
docker exec news-backend-prod cp /app/data/newsFeed.json /tmp/
docker cp news-backend-prod:/tmp/newsFeed.json ./backup/

# Backup Redis
docker exec news-redis-prod redis-cli BGSAVE
docker cp news-redis-prod:/data/dump.rdb ./backup/
```

## 🌟 Дополнительные Возможности

### Интеграции
- **Slack/Discord** уведомления о важных новостях
- **Telegram бот** для мобильных уведомлений  
- **Email рассылка** дайджестов
- **Webhook API** для внешних систем

### Аналитика
- **Sentiment trends** - тренды настроений рынка
- **News impact scoring** - оценка влияния новостей
- **Real-time alerts** - алерты по ключевым событиям
- **Custom dashboards** - персонализированные дашборды

---

## 🎉 Готово!

Ваша News AI система готова к продакшн использованию!

**Полезные ссылки:**
- 📖 [Полная документация](PRODUCTION_DEPLOYMENT.md)
- 🔧 [Техническая документация](SENTIMENT_MIGRATION_README.md)
- 🐛 [Troubleshooting Guide](PRODUCTION_DEPLOYMENT.md#troubleshooting)

**Поддержка:**
- 💬 GitHub Issues для багов
- 📧 Email для вопросов
- 📱 Telegram для быстрой помощи
