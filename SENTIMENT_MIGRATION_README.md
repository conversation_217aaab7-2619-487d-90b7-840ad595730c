# 🚀 Миграция Анализа Настроения на Бэкенд

## 📋 Обзор Изменений

Мы полностью переработали логику определения настроения новостей, перенеся её с фронтенда на бэкенд с использованием AI анализа.

### ✅ Что Изменилось

**Бэкенд:**
- ✨ Расширенный AI промпт для детального анализа настроения
- 🧠 Новая структура данных `sentimentData` с полями:
  - `sentiment`: positive/negative/neutral
  - `impact`: high/medium/low  
  - `score`: 0.0-1.0 (сила настроения)
  - `confidence`: 0.0-1.0 (уверенность анализа)
  - `reasoning`: объяснение анализа
  - `marketImpact`: потенциальное влияние на рынок

**Фронтенд:**
- 🔄 Обновлена модель `SentimentData` для поддержки новых полей
- 📱 Новый метод `getNewsFromBackend()` в `NewsService`
- 🎯 Автоматическое использование данных с бэкенда в `NewsDetailData`
- 🔙 Fallback на старую логику при недоступности бэкенда

## 🛠️ Настройка

### 1. Настройка Бэкенда

```bash
cd flutter/news-backend
npm install
```

Создайте `.env` файл:
```env
DEEPSEEK_API_KEY=your_deepseek_api_key_here
PORT=4000
```

Запустите бэкенд:
```bash
npm start
```

### 2. Тестирование Бэкенда

```bash
node test_backend.js
```

### 3. Настройка Фронтенда

Обновите URL бэкенда в `news_service.dart`:
```dart
static const String _backendBaseUrl = 'http://your-backend-url:4000';
```

## 🔄 Как Это Работает

### Поток Данных

1. **Парсинг**: Бэкенд парсит новости из API источников
2. **AI Анализ**: DeepSeek анализирует каждую новость и определяет:
   - Настроение (positive/negative/neutral)
   - Силу воздействия (high/medium/low)
   - Уверенность анализа
   - Потенциальное влияние на рынок
3. **Real-time Обновления**: Как только новость проанализирована, она отправляется всем подключенным клиентам через SSE
4. **Кэширование**: Результаты сохраняются в кэш
5. **API**: Фронтенд получает готовые данные через `/news` endpoint

### 🚀 Real-time Архитектура

**Server-Sent Events (SSE):**
- Endpoint: `GET /news/stream`
- Автоматическое подключение при загрузке приложения
- Мгновенные обновления ленты новостей
- Индикатор статуса подключения в UI
- Автоматический fallback при отключении

### Структура Ответа API

```json
{
  "news": [
    {
      "id": "123",
      "title": "Bitcoin Surges to New Heights",
      "sentiment": "positive",
      "sentimentData": {
        "sentiment": "positive",
        "impact": "high",
        "score": 0.8,
        "confidence": 0.9,
        "reasoning": "Strong bullish signals and institutional adoption",
        "marketImpact": "Likely to drive significant price appreciation"
      },
      "tags": ["Bitcoin", "Cryptocurrency"],
      "summary": "AI-generated summary...",
      // ... другие поля
    }
  ]
}
```

## 🎯 Преимущества Новой Архитектуры

### ✅ Для Пользователей
- 🎯 **Точность**: AI анализ вместо простых ключевых слов
- ⚡ **Скорость**: Предварительно обработанные данные
- 📊 **Детализация**: Подробная информация о настроении
- 🔄 **Консистентность**: Единый анализ для всех пользователей

### ✅ Для Разработчиков  
- 🏗️ **Масштабируемость**: Централизованная обработка
- 💰 **Экономия**: Один API вызов вместо множества
- 🔧 **Гибкость**: Легко изменить логику анализа
- 📈 **Мониторинг**: Централизованные логи и метрики

## 🔧 API Endpoints

### GET /news
Получение новостей с фильтрацией:
- `?sentiment=positive` - фильтр по настроению
- `?tags=Bitcoin,Ethereum` - фильтр по тегам
- `?search=keyword` - поиск по тексту
- `?page=1&pageSize=20` - пагинация

### GET /news/stream (NEW! 🚀)
Real-time поток новостей через Server-Sent Events:
- Автоматические обновления при добавлении новых новостей
- События: `connected`, `news-added`
- Поддержка множественных клиентов

### POST /admin/parse
Запуск парсинга и анализа новостей

### GET /status
Проверка статуса сервера и количества подключенных клиентов

## 🚨 Fallback Стратегия

Если бэкенд недоступен, фронтенд автоматически:
1. Использует старые методы получения новостей
2. Применяет локальный анализ настроения
3. Показывает уведомление о режиме fallback

## 📝 Следующие Шаги

1. **Деплой бэкенда** на продакшн сервер
2. **Настройка мониторинга** для отслеживания работы AI
3. **Оптимизация промптов** на основе результатов
4. **Добавление кэширования** на уровне CDN
5. **Интеграция дополнительных AI моделей** для сравнения

## 🐛 Отладка

### Проблемы с AI анализом
- Проверьте API ключ DeepSeek
- Убедитесь в наличии интернет соединения
- Проверьте лимиты API

### Проблемы с подключением
- Убедитесь что бэкенд запущен на правильном порту
- Проверьте CORS настройки
- Убедитесь в правильности URL в фронтенде

Логи бэкенда покажут детальную информацию о процессе анализа.
