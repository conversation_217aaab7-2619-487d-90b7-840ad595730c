# Silk Background для новостного экрана

## Обзор

Новый красивый анимированный фон для новостного экрана, использующий Fragment Shader для создания эффекта шелковой ткани с плавными волнами и текстурой.

## Возможности

### 🎨 Визуальные эффекты
- **Silk Pattern**: Реалистичный эффект шелковой ткани
- **Плавные волны**: Анимированные волны с настраиваемой скоростью
- **Градиентные переходы**: Красивые цветовые переходы
- **Noise текстура**: Добавляет реалистичность и глубину
- **Fallback режим**: Автоматический переход на градиент при недоступности шейдеров

### ⚙️ Настройки
- `speed`: Скорость анимации (по умолчанию: 3.0)
- `scale`: Масштаб паттерна (по умолчанию: 1.2)
- `color`: Основной цвет (по умолчанию: темно-синий)
- `noiseIntensity`: Интенсивность шума (по умолчанию: 1.0)
- `rotation`: Поворот паттерна (по умолчанию: 0.0)

## Файлы

### Основные компоненты
- `lib/widgets/silk_background.dart` - Основной виджет
- `shaders/silk.frag` - Fragment shader для эффекта
- `lib/test_silk_background.dart` - Тестовый экран

### Интеграция
- Обновлен `lib/screens/news_screen.dart`
- Добавлен шейдер в `pubspec.yaml`

## Использование

### Базовое использование
```dart
SilkBackground(
  child: YourContent(),
)
```

### Для новостного экрана
```dart
NewsScreenSilkBackground(
  child: YourNewsContent(),
)
```

### Кастомизация
```dart
SilkBackground(
  speed: 2.5,
  scale: 1.5,
  color: Color(0xFF0f1419),
  noiseIntensity: 0.8,
  rotation: 0.1,
  child: YourContent(),
)
```

## Технические детали

### Fragment Shader
Использует GLSL шейдер для создания:
- Процедурного шума
- Волновых эффектов
- Поворота UV координат
- Сложных математических паттернов

### Fallback система
При недоступности шейдеров автоматически переключается на:
- Анимированный градиент
- CSS-подобные переходы
- Математические функции для имитации волн

### Производительность
- Оптимизирован для 60 FPS
- Минимальное использование CPU
- GPU-ускоренная анимация
- Автоматическое управление ресурсами

## Установка

1. **Добавить шейдер в pubspec.yaml**:
```yaml
flutter:
  shaders:
    - shaders/silk.frag
```

2. **Создать файл шейдера**:
Файл `shaders/silk.frag` уже создан с оптимизированным кодом.

3. **Импортировать виджет**:
```dart
import 'widgets/silk_background.dart';
```

## Тестирование

Запустите тестовый экран:
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => TestSilkBackgroundScreen(),
  ),
);
```

## Совместимость

### Поддерживаемые платформы
- ✅ Android
- ✅ iOS  
- ✅ Web (с ограничениями)
- ✅ Desktop

### Требования
- Flutter 3.0+
- Поддержка Fragment Shaders
- OpenGL ES 3.0+ (мобильные)
- WebGL 2.0+ (веб)

## Настройка цветов

### Предустановленные темы
```dart
// Темная тема для новостей
Color(0xFF0f1419)

// Синяя тема
Color(0xFF1a1a2e)

// Фиолетовая тема
Color(0xFF2d1b69)
```

### Кастомные цвета
```dart
SilkBackground(
  color: Color.fromRGBO(15, 20, 25, 1.0),
  // ...
)
```

## Производительность

### Оптимизации
- Использование GPU для вычислений
- Минимальные перерисовки
- Эффективное управление памятью
- Автоматическая очистка ресурсов

### Мониторинг
- Проверка FPS в debug режиме
- Логирование ошибок шейдеров
- Автоматический fallback

## Отладка

### Проверка поддержки шейдеров
```dart
// Логи в консоли покажут статус загрузки шейдера
// "Shader loading failed" - переход на fallback
// "Shader loaded successfully" - шейдер работает
```

### Fallback режим
Если шейдеры не поддерживаются, автоматически включается режим с градиентами.

## Будущие улучшения

- [ ] Поддержка множественных цветов
- [ ] Интерактивные эффекты от касаний
- [ ] Адаптивная интенсивность по производительности
- [ ] Дополнительные паттерны текстур
- [ ] Синхронизация с темой приложения
