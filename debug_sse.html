<!DOCTYPE html>
<html>
<head>
    <title>SSE Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .message { background-color: #f8f9fa; padding: 5px; margin: 5px 0; border-left: 3px solid #007bff; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>SSE Connection Debug</h1>
    
    <div id="status" class="status">Готов к подключению</div>
    
    <button onclick="connect()">Подключиться</button>
    <button onclick="disconnect()">Отключиться</button>
    <button onclick="clearMessages()">Очистить</button>
    
    <h3>Сообщения:</h3>
    <div id="messages"></div>

    <script>
        let eventSource = null;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        function addMessage(text, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${text}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function updateStatus(text, className) {
            statusDiv.textContent = text;
            statusDiv.className = `status ${className}`;
        }
        
        function connect() {
            if (eventSource) {
                addMessage('Уже подключен!', 'warning');
                return;
            }
            
            addMessage('Попытка подключения к http://localhost:4000/news/stream');
            updateStatus('Подключение...', '');
            
            eventSource = new EventSource('http://localhost:4000/news/stream');
            
            eventSource.onopen = function(event) {
                addMessage('✅ SSE соединение открыто');
                updateStatus('Подключен!', 'connected');
            };
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addMessage(`📨 Получено: ${data.type} - ${data.message || 'нет сообщения'}`);
                } catch (e) {
                    addMessage(`📨 Получено (raw): ${event.data}`);
                }
            };
            
            eventSource.onerror = function(event) {
                addMessage('❌ Ошибка SSE соединения');
                updateStatus('Ошибка подключения', 'error');
                
                if (eventSource.readyState === EventSource.CLOSED) {
                    addMessage('🔌 Соединение закрыто');
                    eventSource = null;
                }
            };
        }
        
        function disconnect() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                addMessage('🔌 Соединение закрыто вручную');
                updateStatus('Отключен', '');
            } else {
                addMessage('Соединение уже закрыто');
            }
        }
        
        function clearMessages() {
            messagesDiv.innerHTML = '';
        }
        
        // Автоматическое подключение при загрузке
        window.onload = function() {
            addMessage('🚀 Страница загружена, готов к подключению');
        };
    </script>
</body>
</html>
