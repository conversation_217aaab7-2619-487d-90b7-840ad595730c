#!/bin/bash

# Скрипт для развертывания в продакшн
set -e

echo "🚀 Начинаем развертывание в продакшн..."

# Проверяем наличие необходимых файлов
if [ ! -f "news-backend/.env.production" ]; then
    echo "❌ Файл .env.production не найден!"
    echo "Создайте файл news-backend/.env.production с вашими API ключами"
    exit 1
fi

# Проверяем Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker не установлен!"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose не установлен!"
    exit 1
fi

echo "✅ Предварительные проверки пройдены"

# Создаем необходимые директории
mkdir -p nginx/ssl
mkdir -p monitoring

# Билдим Flutter Web приложение
echo "📱 Собираем Flutter Web приложение..."
flutter build web --release --web-renderer html

# Копируем билд в volume для nginx
echo "📦 Подготавливаем статические файлы..."
docker volume create flutter_web_build
docker run --rm -v flutter_web_build:/data -v $(pwd)/build/web:/source alpine sh -c "cp -r /source/* /data/"

# Устанавливаем продакшн зависимости для бэкенда
echo "📦 Устанавливаем зависимости бэкенда..."
cd news-backend
npm ci --only=production
cd ..

# Билдим и запускаем контейнеры
echo "🐳 Запускаем Docker контейнеры..."
docker-compose -f docker-compose.prod.yml up --build -d

# Ждем запуска сервисов
echo "⏳ Ждем запуска сервисов..."
sleep 30

# Проверяем статус сервисов
echo "🔍 Проверяем статус сервисов..."
docker-compose -f docker-compose.prod.yml ps

# Проверяем health check
echo "🏥 Проверяем health check..."
if curl -f http://localhost/health > /dev/null 2>&1; then
    echo "✅ Nginx работает"
else
    echo "❌ Nginx не отвечает"
fi

if curl -f http://localhost/api/status > /dev/null 2>&1; then
    echo "✅ Backend API работает"
else
    echo "❌ Backend API не отвечает"
fi

# Запускаем первичный парсинг новостей
echo "📰 Запускаем первичный парсинг новостей..."
curl -X POST http://localhost/api/admin/parse || echo "⚠️ Не удалось запустить парсинг"

echo ""
echo "🎉 Развертывание завершено!"
echo ""
echo "📋 Доступные сервисы:"
echo "   🌐 Web приложение: http://localhost"
echo "   🔧 API: http://localhost/api"
echo "   📊 Мониторинг: http://localhost:9090"
echo "   📈 Метрики: http://localhost/api/status"
echo ""
echo "📝 Полезные команды:"
echo "   docker-compose -f docker-compose.prod.yml logs -f    # Просмотр логов"
echo "   docker-compose -f docker-compose.prod.yml down       # Остановка"
echo "   docker-compose -f docker-compose.prod.yml restart    # Перезапуск"
echo ""
echo "⚠️  Не забудьте:"
echo "   1. Настроить SSL сертификаты в nginx/ssl/"
echo "   2. Обновить домен в конфигурации"
echo "   3. Настроить мониторинг и алерты"
