version: '3.8'

services:
  # News Backend API
  news-backend:
    build:
      context: ./news-backend
      dockerfile: Dockerfile
    container_name: news-backend-prod
    restart: unless-stopped
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
      - PORT=4000
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - NEWS_API_KEY=${NEWS_API_KEY}
      - CRYPTO_COMPARE_API_KEY=${CRYPTO_COMPARE_API_KEY}
    volumes:
      - news_data:/app/data
      - ./news-backend/.env:/app/.env:ro
    networks:
      - news_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4000/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: news-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - flutter_web_build:/usr/share/nginx/html
    depends_on:
      - news-backend
    networks:
      - news_network

  # Redis для кэширования (опционально)
  redis:
    image: redis:7-alpine
    container_name: news-redis-prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - news_network
    command: redis-server --appendonly yes

  # Monitoring с Prometheus (опционально)
  prometheus:
    image: prom/prometheus:latest
    container_name: news-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - news_network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

volumes:
  news_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  flutter_web_build:
    driver: local

networks:
  news_network:
    driver: bridge
