import 'package:flutter/material.dart';
import '../models/news_item.dart';
import '../widgets/news_card.dart';
import '../widgets/filter_panel.dart';
import '../widgets/app_bottom_navigation.dart';

class NewsScreen extends StatefulWidget {
  const NewsScreen({super.key});

  @override
  State<NewsScreen> createState() => _NewsScreenState();
}

class _NewsScreenState extends State<NewsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<NewsItem> _newsItems = NewsItem.getMockItems();
  final List<String> _selectedTags = [];
  SentimentType? _selectedSentiment;
  final TextEditingController _searchController = TextEditingController();
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<NewsItem> get _filteredNews {
    return _newsItems.where((news) {
      // Filter by search query
      if (_searchController.text.isNotEmpty) {
        final query = _searchController.text.toLowerCase();
        if (!news.title.toLowerCase().contains(query) &&
            !news.description.toLowerCase().contains(query)) {
          return false;
        }
      }
      
      // Filter by category
      if (_tabController.index == 1 && news.category != NewsCategory.crypto) {
        return false;
      } else if (_tabController.index == 2 && news.category != NewsCategory.stocks) {
        return false;
      }
      
      // Filter by tags
      if (_selectedTags.isNotEmpty) {
        bool hasSelectedTag = false;
        for (final tag in _selectedTags) {
          if (news.tags.contains(tag)) {
            hasSelectedTag = true;
            break;
          }
        }
        if (!hasSelectedTag) {
          return false;
        }
      }
      
      // Filter by sentiment
      if (_selectedSentiment != null && news.sentiment != _selectedSentiment) {
        return false;
      }
      
      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('News'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Crypto'),
            Tab(text: 'Stocks'),
          ],
          onTap: (_) {
            setState(() {});
          },
        ),
      ),
      body: Column(
        children: [
          // Search and filter bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search news...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30.0),
                      ),
                      contentPadding: const EdgeInsets.symmetric(vertical: 0.0),
                    ),
                    onChanged: (_) {
                      setState(() {});
                    },
                  ),
                ),
                const SizedBox(width: 8.0),
                IconButton(
                  icon: Icon(
                    Icons.filter_list,
                    color: _showFilters ? Theme.of(context).primaryColor : Colors.grey,
                  ),
                  onPressed: () {
                    setState(() {
                      _showFilters = !_showFilters;
                    });
                  },
                ),
              ],
            ),
          ),
          
          // Filter panel
          if (_showFilters)
            FilterPanel(
              selectedTags: _selectedTags,
              selectedSentiment: _selectedSentiment,
              onTagToggle: (tag) {
                setState(() {
                  if (_selectedTags.contains(tag)) {
                    _selectedTags.remove(tag);
                  } else {
                    _selectedTags.add(tag);
                  }
                });
              },
              onSentimentChanged: (sentiment) {
                setState(() {
                  _selectedSentiment = sentiment;
                });
              },
            ),
          
          // News list
          Expanded(
            child: _filteredNews.isEmpty
                ? const Center(
                    child: Text('No news found matching your filters'),
                  )
                : ListView.builder(
                    itemCount: _filteredNews.length,
                    itemBuilder: (context, index) {
                      final newsItem = _filteredNews[index];
                      return NewsCard(
                        key: ValueKey('news-simple-${newsItem.id}'), // Уникальный ключ
                        newsItem: newsItem,
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            '/news_detail',
                            arguments: newsItem,
                          );
                        },
                      );
                    },
                  ),
          ),
        ],
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 0,
        onTap: (index) {
          if (index != 0) {
            switch (index) {
              case 1:
                Navigator.pushReplacementNamed(context, '/crypto_markets');
                break;
              case 2:
                Navigator.pushReplacementNamed(context, '/courses');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }
}
