#!/bin/bash

# Скрипт для проверки здоровья системы
set -e

echo "🏥 Проверка здоровья News AI System..."

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Функция для проверки сервиса
check_service() {
    local name=$1
    local url=$2
    local expected_status=${3:-200}
    
    echo -n "Проверяем $name... "
    
    if response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null); then
        if [ "$response" -eq "$expected_status" ]; then
            echo -e "${GREEN}✅ OK${NC} (HTTP $response)"
            return 0
        else
            echo -e "${RED}❌ FAIL${NC} (HTTP $response, ожидался $expected_status)"
            return 1
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (недоступен)"
        return 1
    fi
}

# Функция для проверки SSE
check_sse() {
    echo -n "Проверяем SSE поток... "
    
    if timeout 5 curl -s "http://localhost/api/news/stream" | head -n 1 | grep -q "data:"; then
        echo -e "${GREEN}✅ OK${NC}"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}"
        return 1
    fi
}

# Функция для проверки Docker контейнеров
check_containers() {
    echo "Проверяем Docker контейнеры:"
    
    containers=("news-backend-prod" "news-nginx-prod" "news-redis-prod")
    all_healthy=true
    
    for container in "${containers[@]}"; do
        echo -n "  $container... "
        
        if docker ps --filter "name=$container" --filter "status=running" | grep -q "$container"; then
            # Проверяем health status если есть
            health=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no-healthcheck")
            
            if [ "$health" = "healthy" ] || [ "$health" = "no-healthcheck" ]; then
                echo -e "${GREEN}✅ Running${NC}"
            else
                echo -e "${YELLOW}⚠️ Running but unhealthy${NC}"
                all_healthy=false
            fi
        else
            echo -e "${RED}❌ Not running${NC}"
            all_healthy=false
        fi
    done
    
    return $all_healthy
}

# Функция для проверки ресурсов
check_resources() {
    echo "Проверяем использование ресурсов:"
    
    # Проверяем использование диска
    disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    echo -n "  Диск: $disk_usage%... "
    if [ "$disk_usage" -lt 80 ]; then
        echo -e "${GREEN}✅ OK${NC}"
    elif [ "$disk_usage" -lt 90 ]; then
        echo -e "${YELLOW}⚠️ Warning${NC}"
    else
        echo -e "${RED}❌ Critical${NC}"
    fi
    
    # Проверяем использование памяти
    memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    echo -n "  Память: $memory_usage%... "
    if [ "$memory_usage" -lt 80 ]; then
        echo -e "${GREEN}✅ OK${NC}"
    elif [ "$memory_usage" -lt 90 ]; then
        echo -e "${YELLOW}⚠️ Warning${NC}"
    else
        echo -e "${RED}❌ Critical${NC}"
    fi
}

# Основные проверки
echo "=== Основные Сервисы ==="
check_service "Nginx" "http://localhost/health"
check_service "Backend API" "http://localhost/api/status"
check_service "Prometheus" "http://localhost:9090/-/healthy"

echo ""
echo "=== Real-time Функции ==="
check_sse

echo ""
echo "=== Docker Контейнеры ==="
check_containers

echo ""
echo "=== Ресурсы Системы ==="
check_resources

echo ""
echo "=== Дополнительная Информация ==="

# Получаем статистику из API
if api_stats=$(curl -s "http://localhost/api/status" 2>/dev/null); then
    total_news=$(echo "$api_stats" | jq -r '.totalNews // "N/A"')
    connected_clients=$(echo "$api_stats" | jq -r '.connectedClients // "N/A"')
    
    echo "  📰 Всего новостей: $total_news"
    echo "  👥 Подключенных клиентов: $connected_clients"
else
    echo "  ❌ Не удалось получить статистику API"
fi

# Проверяем последние логи на ошибки
echo -n "  🔍 Проверяем логи на ошибки... "
if docker-compose -f docker-compose.prod.yml logs --tail=100 2>/dev/null | grep -i error | wc -l | awk '{if($1>0) print "Found " $1 " errors"; else print "No errors"}' | grep -q "No errors"; then
    echo -e "${GREEN}✅ Чисто${NC}"
else
    error_count=$(docker-compose -f docker-compose.prod.yml logs --tail=100 2>/dev/null | grep -i error | wc -l)
    echo -e "${YELLOW}⚠️ Найдено $error_count ошибок${NC}"
fi

echo ""
echo "🏥 Проверка здоровья завершена!"

# Возвращаем код выхода на основе общего состояния
if check_service "Backend API" "http://localhost/api/status" >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Система работает нормально${NC}"
    exit 0
else
    echo -e "${RED}❌ Обнаружены проблемы${NC}"
    exit 1
fi
