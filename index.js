const WebSocket = require('ws');
const fs = require('fs');
const { parseTelegram } = require('./parser');

const PORT = 8765;
const GROUPS = ['whale_alert_io', 'WhaleSniper'];
const CACHE_FILE = 'cache.json';

// Загружаем кэш из файла, если он существует
let latestMessages = loadCache();

// Создаём WebSocket сервер
const wss = new WebSocket.Server({ port: PORT });

wss.on('connection', async (ws) => {
  console.log('🟢 Flutter подключен');

  // Отправляем клиенту кэш при подключении
  for (const group of GROUPS) {
    (latestMessages[group] || []).forEach((msg) => {
      ws.send(`[${group}] ${msg}`);
    });
  }

  // После отдачи кэша запускаем обновление, чтобы клиент сразу получил свежие данные
  try {
    await updateMessages();
  } catch (err) {
    console.error('⚠️ Ошибка мгновенного обновления при новом подключении:', err.message);
  }

  ws.on('close', () => console.log('🔴 Flutter отключился'));
});

// ----------------------- Функции кэша --------------------------
function loadCache() {
  try {
    if (fs.existsSync(CACHE_FILE)) {
      const raw = fs.readFileSync(CACHE_FILE, 'utf-8');
      return JSON.parse(raw);
    }
  } catch (err) {
    console.error('⚠️ Ошибка загрузки кэша:', err.message);
  }
  return {};
}

function saveCache() {
  try {
    fs.writeFileSync(CACHE_FILE, JSON.stringify(latestMessages, null, 2));
  } catch (err) {
    console.error('❌ Не удалось сохранить кэш:', err.message);
  }
}

// ------------- Обновление сообщений по группам ----------------
async function updateMessages() {
  console.log('🔄 Обновление всех групп...');
  for (const group of GROUPS) {
    const newMessages = await parseTelegram(group, 20);
    const oldMessages = latestMessages[group] || [];

    // Сравниваем массивы: если изменились — обновляем и шлём клиентам
    if (JSON.stringify(newMessages) !== JSON.stringify(oldMessages)) {
      latestMessages[group] = newMessages;
      console.log(`📤 Обновлены сообщения из ${group}`);

      wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          newMessages.forEach((msg) => {
            client.send(`[${group}] ${msg}`);
          });
        }
      });
    } else {
      console.log(`⏸️ Нет изменений в ${group}`);
    }
  }

  // Сохраняем кэш после всех обновлений
  saveCache();
}

// ---------------------- Запуск сервера -------------------------
(async () => {
  // Первичное обновление перед стартом
  await updateMessages();

  // Периодический опрос раз в минуту
  setInterval(updateMessages, 60 * 1000);

  console.log(`🚀 WebSocket сервер запущен: ws://localhost:${PORT}`);
})(); 