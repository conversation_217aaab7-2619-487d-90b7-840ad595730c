import 'package:flutter/material.dart';
import '../widgets/news_detail_modal.dart';
import '../models/news_detail.dart';
import '../models/sentiment_types.dart';

class ModalDemo extends StatelessWidget {
  const ModalDemo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF000000),
      appBar: AppBar(
        title: const Text('Neomorphic News Modal Demo'),
        backgroundColor: const Color(0xFF1C1C1E),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildDemoButton(
              context,
              'Positive News',
              SentimentType.positive,
              Colors.green,
            ),
            const SizedBox(height: 20),
            _buildDemoButton(
              context,
              'Negative News',
              SentimentType.negative,
              Colors.red,
            ),
            const SizedBox(height: 20),
            _buildDemoButton(
              context,
              'Neutral News',
              SentimentType.neutral,
              Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDemoButton(
    BuildContext context,
    String title,
    SentimentType sentiment,
    Color color,
  ) {
    return Container(
      width: 250,
      height: 60,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withOpacity(0.8),
            color.withOpacity(0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: -3,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(30),
          onTap: () => _showDemoModal(context, sentiment),
          child: Center(
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
                fontFamily: 'SF Pro Display',
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showDemoModal(BuildContext context, SentimentType sentiment) {
    final newsDetail = NewsDetailData(
      title: _getTitleForSentiment(sentiment),
      description: _getDescriptionForSentiment(sentiment),
      fullContent: _getContentForSentiment(sentiment),
      rewrittenContent: _getRewrittenContentForSentiment(sentiment),
      source: 'Demo News Source',
      publishedAt: DateTime.now().subtract(const Duration(hours: 2)),
      fetchedAt: DateTime.now(),
      url: 'https://example.com/news',
      imageUrl: 'https://via.placeholder.com/400x200?text=News+Image',
      sentiment: SentimentAnalysis(
        sentiment: sentiment,
        confidence: 0.85,
        reasoning: _getReasoningForSentiment(sentiment),
        marketImpact: _getMarketImpactForSentiment(sentiment),
      ),
      tags: ['финансы', 'рынок', 'новости', 'анализ'],
      summary: _getSummaryForSentiment(sentiment),
    );

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return NewsDetailModal(
          newsDetail: newsDetail,
          onClose: () => Navigator.of(context).pop(),
        );
      },
    );
  }

  String _getTitleForSentiment(SentimentType sentiment) {
    switch (sentiment) {
      case SentimentType.positive:
        return 'Bitcoin Volatility Declines Amid Record Highs, Signaling Market Maturity';
      case SentimentType.negative:
        return 'Crypto Markets Face Pressure as Regulatory Concerns Mount Globally';
      default:
        return 'Federal Reserve Maintains Interest Rates, Crypto Markets Show Mixed Signals';
    }
  }

  String _getDescriptionForSentiment(SentimentType sentiment) {
    switch (sentiment) {
      case SentimentType.positive:
        return 'Bitcoin volatility reaches multi-month lows as institutional adoption accelerates and major financial institutions integrate cryptocurrency services.';
      case SentimentType.negative:
        return 'Cryptocurrency markets face mounting regulatory pressure as global authorities intensify oversight of digital assets and trading platforms.';
      default:
        return 'Federal Reserve maintains current interest rates while cryptocurrency markets show mixed reactions to monetary policy decisions.';
    }
  }

  String _getContentForSentiment(SentimentType sentiment) {
    switch (sentiment) {
      case SentimentType.positive:
        return '''
Bitcoin's price volatility has reached its lowest levels in months, signaling potential market maturation as institutional adoption continues to grow. Major financial institutions are increasingly integrating cryptocurrency services, with several announcing new digital asset custody solutions.

The decline in volatility comes amid record-high trading volumes and increased participation from institutional investors. Market analysts suggest this trend indicates growing confidence in Bitcoin as a store of value, similar to traditional safe-haven assets.

Recent data shows that long-term holders are accumulating Bitcoin at unprecedented rates, while short-term speculation has decreased significantly. This shift in investor behavior is contributing to the more stable price action observed in recent weeks.
        ''';
      case SentimentType.negative:
        return '''
Cryptocurrency markets are facing mounting pressure as regulatory authorities worldwide intensify their scrutiny of digital assets. Several major economies have announced plans for stricter oversight, creating uncertainty among investors and market participants.

The regulatory concerns have led to increased selling pressure across major cryptocurrencies, with Bitcoin and Ethereum experiencing significant outflows from exchanges. Institutional investors are reportedly reassessing their digital asset allocations amid the evolving regulatory landscape.

Market volatility has spiked as traders react to each new regulatory announcement, creating challenging conditions for both retail and institutional participants.
        ''';
      default:
        return '''
The Federal Reserve has maintained interest rates at current levels, as widely expected by market participants. The decision reflects the central bank's cautious approach to monetary policy amid mixed economic signals and ongoing inflation concerns.

Cryptocurrency markets have shown mixed reactions to the Fed's decision, with Bitcoin trading in a narrow range while altcoins display varied performance. The digital asset market appears to be developing its own dynamics, less dependent on traditional monetary policy signals.

Market analysts note that crypto adoption continues to grow despite macroeconomic uncertainties, with several major corporations announcing blockchain initiatives and digital asset integration plans.
        ''';
    }
  }

  String _getRewrittenContentForSentiment(SentimentType sentiment) {
    return _getContentForSentiment(sentiment) +
           '\n\nOur AI analysis reveals significant shifts in market sentiment patterns. ' +
           'Investors are advised to monitor developments closely and adjust their strategies accordingly based on evolving market conditions.';
  }

  String _getReasoningForSentiment(SentimentType sentiment) {
    switch (sentiment) {
      case SentimentType.positive:
        return 'Reduced volatility and institutional adoption typically signal market maturation and increased confidence in digital assets as legitimate investment vehicles.';
      case SentimentType.negative:
        return 'Regulatory uncertainty and increased government oversight create market instability and investor hesitation in the cryptocurrency space.';
      default:
        return 'Central bank decisions align with market expectations and should have minimal direct impact on cryptocurrency valuations in the short term.';
    }
  }

  String _getMarketImpactForSentiment(SentimentType sentiment) {
    switch (sentiment) {
      case SentimentType.positive:
        return 'Ожидается рост технологического сектора на 3-5% в краткосрочной перспективе.';
      case SentimentType.negative:
        return 'Возможно снижение основных индексов на 2-4% в ближайшие торговые сессии.';
      default:
        return 'Минимальное влияние на рынки, возможны небольшие колебания в пределах 1%.';
    }
  }

  String _getSummaryForSentiment(SentimentType sentiment) {
    return 'AI-сводка: ' + _getDescriptionForSentiment(sentiment) + 
           ' Рекомендуется мониторинг ситуации для принятия обоснованных инвестиционных решений.';
  }
}
