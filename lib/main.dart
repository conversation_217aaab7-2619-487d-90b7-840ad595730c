import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_web/webview_flutter_web.dart';
import 'package:flutter/cupertino.dart';

import 'screens/news_screen_clean.dart';
import 'screens/profile_screen.dart';
import 'screens/courses_screen.dart';
import 'screens/materials_screen.dart';
import 'screens/games/games_screen.dart';

import 'screens/saved_analyses_screen.dart';
import 'screens/login_screen.dart';
import 'screens/minimal_news_detail_screen.dart';
import 'screens/charts_screen.dart';
import 'screens/coin_detail_screen.dart';

import 'screens/anti_fomo_simulator_screen.dart';
import 'screens/reactor_sinusoid_screen.dart';
import 'screens/enhanced_reactor_screen.dart';
import 'screens/market_sentiment_screen.dart';
import 'screens/advanced_market_sentiment_screen.dart' as advanced;
import 'screens/sinusoid_screen.dart';
import 'screens/welcome_screen.dart';
import 'screens/register_screen.dart';
import 'screens/hyperjump_animation_screen.dart';
import 'screens/crypto_simulator_mode_selection_screen.dart';
import 'screens/crypto_trading_simulator_screen.dart';
import 'screens/custom_mode_settings_screen.dart';
import 'screens/infinite_patterns_leverage_screen.dart';
import 'screens/simulator_menu_screen.dart';
import 'screens/crypto_quiz_screen.dart';
import 'providers/auth_provider.dart';
import 'providers/news_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/crypto_provider.dart';
import 'providers/trading_simulator_provider.dart';
import 'config/env_config.dart';
import 'utils/custom_page_transitions.dart';
import 'models/trading_simulator_models.dart' as models;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Загрузка переменных окружения
    await EnvConfig.load();

    // Инициализация WebView для веб-платформы
    if (kIsWeb) {
      WebViewPlatform.instance = WebWebViewPlatform();
    }

    // Initialize providers
    final themeProvider = ThemeProvider();
    await themeProvider.initTheme();

    final authProvider = AuthProvider();
    await authProvider.initAuth();

    // Initialize crypto provider
    final cryptoProvider = CryptoProvider();
    // Start loading crypto data
    cryptoProvider.initialize();

    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => themeProvider),
          ChangeNotifierProvider(create: (_) => authProvider),
          ChangeNotifierProvider(create: (_) => NewsProvider()),
          ChangeNotifierProvider(create: (_) => cryptoProvider),
          ChangeNotifierProvider(create: (_) => TradingSimulatorProvider()),
        ],
        child: const MyApp(),
      ),
    );
  } catch (e) {
    print('Error during app initialization: $e');
    // В случае ошибки запускаем минимальное приложение
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red),
                SizedBox(height: 16),
                Text('Application failed to initialize'),
                SizedBox(height: 8),
                Text('Error: $e', style: TextStyle(fontSize: 12)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);

    return MaterialApp(
      title: 'TMM',
      theme: themeProvider.themeData,
      initialRoute: authProvider.isAuthenticated ? '/news' : '/welcome',
      // Используем onGenerateRoute для создания красивых переходов между экранами
      onGenerateRoute: (settings) {
        // Получаем имя маршрута
        final String? name = settings.name;

        // Возвращаем соответствующий маршрут с анимацией
        switch (name) {
          case '/welcome':
            return CustomPageTransitions.buildScaleTransition(
              page: const WelcomeScreen(),
              settings: settings,
            );
          case '/login':
            return CustomPageTransitions.buildScaleTransition(
              page: const LoginScreen(),
              settings: settings,
            );
          case '/register':
            return CustomPageTransitions.buildScaleTransition(
              page: const RegisterScreen(),
              settings: settings,
            );
          case '/news':
            return CustomPageTransitions.buildScaleTransition(
              page: const NewsScreen(),
              settings: settings,
            );
          case '/news_detail':
            return CustomPageTransitions.buildSlideTransition(
              page: const MinimalNewsDetailScreen(),
              settings: settings,
            );
          case '/charts':
            return CustomPageTransitions.buildScaleTransition(
              page: const ChartsScreen(),
              settings: settings,
            );
          case '/coin_detail':
            return CustomPageTransitions.buildCoinDetailTransition(
              page: const CoinDetailScreen(),
              settings: settings,
            );
          case '/sinusoid':
            return CustomPageTransitions.buildScaleTransition(
              page: const ReactorSinusoidScreen(),
              settings: settings,
            );
          case '/enhanced_sinusoid':
            return CustomPageTransitions.buildScaleTransition(
              page: const EnhancedReactorScreen(),
              settings: settings,
            );
          case '/market_sentiment':
            return CustomPageTransitions.buildScaleTransition(
              page: const MarketSentimentScreen(),
              settings: settings,
            );
          case '/advanced_market_sentiment':
            return CustomPageTransitions.buildScaleTransition(
              page: const advanced.MarketSentimentScreen(),
              settings: settings,
            );
          case '/profile':
            return CustomPageTransitions.buildScaleTransition(
              page: const ProfileScreen(),
              settings: settings,
            );
          case '/courses':
            return CustomPageTransitions.buildScaleTransition(
              page: const CoursesScreen(),
              settings: settings,
            );
          case '/course-detail':
            return CustomPageTransitions.buildSlideTransition(
              page: const CoursesScreen(), 
              settings: settings,
            );
          case '/materials':
            return CustomPageTransitions.buildScaleTransition(
              page: const MaterialsScreen(),
              settings: settings,
            );
          case '/games':
            return CustomPageTransitions.buildScaleTransition(
              page: const GamesScreen(),
              settings: settings,
            );

          case '/saved_analyses':
            return CustomPageTransitions.buildScaleTransition(
              page: const SavedAnalysesScreen(),
              settings: settings,
            );

          case '/anti_fomo_simulator':
            return CustomPageTransitions.buildScaleTransition(
              page: const AntiFOMOSimulatorScreen(),
              settings: settings,
            );
          case '/hyperjump':
            return CustomPageTransitions.buildScaleTransition(
              page: const HyperjumpAnimationScreen(),
              settings: settings,
            );
          case '/crypto_simulator_mode_selection':
            return CustomPageTransitions.buildScaleTransition(
              page: const CryptoSimulatorModeSelectionScreen(),
              settings: settings,
            );
          case '/custom_mode_settings':
            return CustomPageTransitions.buildScaleTransition(
              page: const CustomModeSettingsScreen(),
              settings: settings,
            );
          case '/infinite_patterns_leverage':
            return CustomPageTransitions.buildScaleTransition(
              page: const InfinitePatternsLeverageScreen(),
              settings: settings,
            );
          case '/crypto_simulator':
            final args = settings.arguments as Map<String, dynamic>?;
            return CustomPageTransitions.buildScaleTransition(
              page: CryptoTradingSimulatorScreen(
                mode: args?['mode'] ?? models.SimulatorMode.infinitePatterns,
                leverage: args?['leverage'] ?? 10.0,
                initialBalance: args?['initialBalance'] ?? 1000.0,
                symbol: args?['symbol'] ?? 'BTCUSDT',
                timeframe: args?['timeframe'] ?? '1h',
                difficulty: args?['difficulty'] ?? 'Medium',
              ),
              settings: settings,
            );
          case '/simulator_menu':
            return CustomPageTransitions.buildScaleTransition(
              page: const SimulatorMenuScreen(),
              settings: settings,
            );
          case '/crypto_quiz':
            return CustomPageTransitions.buildScaleTransition(
              page: const CryptoQuizScreen(),
              settings: settings,
            );
          default:
            return MaterialPageRoute(
              builder: (_) => const NewsScreen(),
              settings: settings,
            );
        }
      },
      debugShowCheckedModeBanner: false,
    );
  }
}
