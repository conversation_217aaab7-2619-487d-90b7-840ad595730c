class Game {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final GameType type;

  Game({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.type,
  });

  // Mock data factory
  static List<Game> getMockGames() {
    return [
      Game(
        id: '1',
        title: 'Crypto Trading Simulator',
        description: 'Level up your skills in real market situations without risking real money.',
        imageUrl: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?q=80&w=2070&auto=format&fit=crop',
        type: GameType.simulator,
      ),
      Game(
        id: '2',
        title: 'Anti-FOMO Simulator',
        description: 'Learn to control emotional trading decisions and avoid the Fear Of Missing Out.',
        imageUrl: 'https://images.unsplash.com/photo-1535320903710-d993d3d77d29?q=80&w=2070&auto=format&fit=crop',
        type: GameType.simulator,
      ),
      Game(
        id: '3',
        title: 'Blockchain Builder',
        description: 'Build your own blockchain and learn how transactions are processed and verified.',
        imageUrl: 'https://via.placeholder.com/300x200?text=Blockchain+Builder',
        type: GameType.educational,
      ),
      Game(
        id: '4',
        title: 'Crypto Millionaire',
        description: 'Strategy game where you build a cryptocurrency portfolio and navigate market cycles.',
        imageUrl: 'https://via.placeholder.com/300x200?text=Crypto+Millionaire',
        type: GameType.strategy,
      ),
      Game(
        id: '5',
        title: 'Crypto Quiz',
        description: 'Test your knowledge of cryptocurrency and blockchain with 1000+ questions across different categories.',
        imageUrl: 'https://via.placeholder.com/300x200?text=Crypto+Quiz',
        type: GameType.quiz,
      ),
    ];
  }
}

enum GameType {
  simulator,
  educational,
  strategy,
  puzzle,
  quiz,
}
