import 'package:flutter/foundation.dart';
import 'news_item.dart'; // For SentimentType

// --- Enums (Existing and New) ---

/// Сила влияния новости на рынок
enum StrengthImpact {
  high("высокое"),
  moderate("умеренное"),
  low("низкое");

  const StrengthImpact(this.value);
  final String value;

  static StrengthImpact fromString(String? value) {
    if (value == null) return StrengthImpact.low;
    return StrengthImpact.values.firstWhere(
      (e) => e.value.toLowerCase() == value.toLowerCase(),
      orElse: () => StrengthImpact.low,
    );
  }
}

/// Направление влияния новости на рынок
enum DirectionImpact {
  positive("позитивное"),
  negative("негативное"),
  neutral("нейтральное");

  const DirectionImpact(this.value);
  final String value;

  static DirectionImpact fromString(String? value) {
    if (value == null) return DirectionImpact.neutral;
    return DirectionImpact.values.firstWhere(
      (e) => e.value.toLowerCase() == value.toLowerCase(),
      orElse: () => DirectionImpact.neutral,
    );
  }
}

/// Временной горизонт влияния новости
enum TimeHorizon {
  shortTerm("краткосрочное"),
  mediumTerm("среднесрочное"),
  longTerm("долгосрочное");

  const TimeHorizon(this.value);
  final String value;

  static TimeHorizon fromString(String? value) {
    if (value == null) return TimeHorizon.shortTerm;
    return TimeHorizon.values.firstWhere(
      (e) => e.value.toLowerCase() == value.toLowerCase(),
      orElse: () => TimeHorizon.shortTerm,
    );
  }
}

enum MarketType {
  crypto("crypto"),
  stock("stock"),
  mixed("смешанный");

  const MarketType(this.value);
  final String value;

  static MarketType fromString(String? value) {
    if (value == null) return MarketType.mixed;
    return MarketType.values.firstWhere(
      (e) => e.value.toLowerCase() == value.toLowerCase(),
      orElse: () => MarketType.mixed,
    );
  }
}

// --- Helper Classes ---

class AffectedAsset {
  final String ticker;
  final MarketType type;
  final String impactDescription; // Renamed from 'impact' to avoid confusion
  final int impactMagnitude;

  AffectedAsset({
    required this.ticker,
    required this.type,
    required this.impactDescription,
    required this.impactMagnitude,
  });

  factory AffectedAsset.fromJson(Map<String, dynamic> json) {
    return AffectedAsset(
      ticker: json['ticker'] ?? '',
      type: MarketType.fromString(json['type']),
      impactDescription: json['impact'] ?? '', // API uses 'impact'
      impactMagnitude: (json['impactMagnitude'] is String)
          ? (int.tryParse(json['impactMagnitude']) ?? 0)
          : (json['impactMagnitude'] ?? 0),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ticker': ticker,
      'type': type.value, // Use .value for enum serialization
      'impact': impactDescription, // Match API expectation if different from field name
      'impactMagnitude': impactMagnitude,
    };
  }
}

class MarketImpact {
  final StrengthImpact strength;
  final DirectionImpact direction;
  final String rationale;
  final List<AffectedAsset> affectedAssets;
  final TimeHorizon timeHorizon;

  MarketImpact({
    required this.strength,
    required this.direction,
    required this.rationale,
    required this.affectedAssets,
    required this.timeHorizon,
  });

  factory MarketImpact.fromJson(Map<String, dynamic> json) {
    var assetsList = <AffectedAsset>[];
    if (json['affectedAssets'] != null && json['affectedAssets'] is List) {
      assetsList = (json['affectedAssets'] as List)
          .map((assetJson) => AffectedAsset.fromJson(assetJson))
          .toList();
    }
    return MarketImpact(
      strength: StrengthImpact.fromString(json['strength']),
      direction: DirectionImpact.fromString(json['direction']),
      rationale: json['rationale'] ?? '',
      affectedAssets: assetsList,
      timeHorizon: TimeHorizon.fromString(json['timeHorizon']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'strength': strength.value,
      'direction': direction.value,
      'rationale': rationale,
      'affectedAssets': affectedAssets.map((e) => e.toJson()).toList(),
      'timeHorizon': timeHorizon.value,
    };
  }
}

class TechnicalLevels {
  final List<String> support;
  final List<String> resistance;

  TechnicalLevels({
    required this.support,
    required this.resistance,
  });

  factory TechnicalLevels.fromJson(Map<String, dynamic> json) {
    return TechnicalLevels(
      support: List<String>.from(json['support'] ?? []),
      resistance: List<String>.from(json['resistance'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'support': support,
      'resistance': resistance,
    };
  }
}

class Forecast {
  final String baseScenario;
  final String alternativeScenario;
  final TechnicalLevels technicalLevels;

  Forecast({
    required this.baseScenario,
    required this.alternativeScenario,
    required this.technicalLevels,
  });

  factory Forecast.fromJson(Map<String, dynamic> json) {
    return Forecast(
      baseScenario: json['baseScenario'] ?? '',
      alternativeScenario: json['alternativeScenario'] ?? '',
      technicalLevels: TechnicalLevels.fromJson(json['technicalLevels'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'baseScenario': baseScenario,
      'alternativeScenario': alternativeScenario,
      'technicalLevels': technicalLevels.toJson(),
    };
  }
}

// --- Main NewsAnalysisResult Class ---

class NewsAnalysisResult {
  final String summary;
  final MarketImpact marketImpact;
  final Forecast forecast;
  final String interestingFact;

  NewsAnalysisResult({
    required this.summary,
    required this.marketImpact,
    required this.forecast,
    required this.interestingFact,
  });

  factory NewsAnalysisResult.fromJson(Map<String, dynamic> json) {
    return NewsAnalysisResult(
      summary: json['summary'] ?? '',
      marketImpact: MarketImpact.fromJson(json['marketImpact'] ?? {}),
      forecast: Forecast.fromJson(json['forecast'] ?? {}),
      interestingFact: json['interestingFact'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'summary': summary,
      'marketImpact': marketImpact.toJson(),
      'forecast': forecast.toJson(),
      'interestingFact': interestingFact,
    };
  }

  // Helper getters to simplify migration in CosmicNewsDetailScreen
  DirectionImpact get sentiment => marketImpact.direction;
  List<String> get keyPoints => [marketImpact.rationale]; // Simplified
}
