import 'package:finance_ai/models/sentiment_types.dart';
import 'package:finance_ai/models/sentiment_data.dart';

class NewsItem {
  final String id;
  final String title;
  final String? aiGeneratedTitle;
  final String description;
  final String imageUrl;
  final DateTime publishedAt;
  final String source;
  final String url;
  final SentimentType sentiment;
  final List<String> tags;
  final NewsCategory category;
  final int importanceLevel;
  final String? content;
  final String? summary;
  final SentimentData? sentimentData;
  final String? rewrittenContent;
  final DateTime fetchedAt;
  final DateTime? cachedAt; // Время сохранения в кэш

  // Новые поля для классификации
  final String? newsCategory; // 'crypto', 'stocks', 'whales', 'general'
  final double? categoryConfidence;
  final Map<String, double>? categoryScores;
  final List<String>? classificationTags;

  NewsItem({
    required this.id,
    required this.title,
    this.aiGeneratedTitle,
    required this.description,
    required this.imageUrl,
    required this.publishedAt,
    required this.source,
    required this.url,
    required this.sentiment,
    required this.tags,
    required this.category,
    this.importanceLevel = 1,
    this.content,
    this.summary,
    this.sentimentData,
    this.rewrittenContent,
    required this.fetchedAt,
    this.cachedAt,
    this.newsCategory,
    this.categoryConfidence,
    this.categoryScores,
    this.classificationTags,
  });

  factory NewsItem.fromJson(Map<String, dynamic> json) {
    final aiTitle = json['aiGeneratedTitle'] ?? '';
    return NewsItem(
      id: json['id'] ?? '',
      title: aiTitle.isNotEmpty ? aiTitle : (json['title'] ?? ''),
      aiGeneratedTitle: aiTitle,
      description: json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? 'https://via.placeholder.com/300x200?text=No+Image',
      publishedAt: DateTime.parse(json['publishedAt'] ?? DateTime.now().toIso8601String()),
      source: json['source'] ?? '',
      url: json['url'] ?? '',
      sentiment: _parseSentiment(json['sentiment']),
      tags: List<String>.from(json['tags'] ?? []),
      category: _parseNewsCategory(List<String>.from(json['tags'] ?? [])),
      content: json['content'],
      summary: json['summary'] ?? '',
      sentimentData: json['sentimentData'] != null 
          ? SentimentData.fromJson(json['sentimentData'])
          : null,
      rewrittenContent: json['rewrittenContent'],
      fetchedAt: json['fetchedAt'] != null ? DateTime.parse(json['fetchedAt']) : DateTime.now(),
      cachedAt: json['cachedAt'] != null ? DateTime.parse(json['cachedAt']) : null,
      newsCategory: json['category'],
      categoryConfidence: json['categoryConfidence']?.toDouble(),
      categoryScores: json['categoryScores'] != null
          ? Map<String, double>.from(json['categoryScores'].map((k, v) => MapEntry(k, v.toDouble())))
          : null,
      classificationTags: json['classificationTags'] != null
          ? List<String>.from(json['classificationTags'])
          : null,
    );
  }

  static SentimentType _parseSentiment(dynamic sentiment) {
    if (sentiment == null) return SentimentType.neutral;
    
    final sentimentStr = sentiment.toString().toLowerCase();
    switch (sentimentStr) {
      case 'positive':
        return SentimentType.positive;
      case 'negative':
        return SentimentType.negative;
      default:
        return SentimentType.neutral;
    }
  }

  static NewsCategory _parseNewsCategory(List<String> tags) {
    final tagsLower = tags.map((tag) => tag.toLowerCase()).toList();
    
    if (tagsLower.any((tag) => ['bitcoin', 'ethereum', 'crypto', 'blockchain', 'btc', 'eth'].contains(tag))) {
      return NewsCategory.crypto;
    } else if (tagsLower.any((tag) => ['stocks', 'market', 'nasdaq', 'dow', 'finance'].contains(tag))) {
      return NewsCategory.stocks;
    } else if (tagsLower.any((tag) => ['ai', 'artificial intelligence', 'machine learning'].contains(tag))) {
      return NewsCategory.ai;
    } else if (tagsLower.any((tag) => ['politics', 'government', 'regulation'].contains(tag))) {
      return NewsCategory.politics;
    } else if (tagsLower.any((tag) => ['whale', 'institutional'].contains(tag))) {
      return NewsCategory.whales;
    }
    
    return NewsCategory.all;
  }

  /// Возвращает максимум 3 тега для отображения (AI/источник)
  List<String> get displayTags {
    // Приоритет: AI (professionalTags/tags), затем источник (tags)
    if (tags.isNotEmpty) {
      return tags.take(3).toList();
    }
    return [];
  }

  static const Map<String, String> _tagMapping = {
    // Криптовалюты
    'btc': 'Bitcoin',
    'bitcoin': 'Bitcoin',
    'eth': 'Ethereum',
    'ethereum': 'Ethereum',
    'crypto': 'Crypto',
    'cryptocurrency': 'Crypto',
    'altcoin': 'Altcoins',
    'altcoins': 'Altcoins',
    'defi': 'DeFi',
    'nft': 'NFT',
    'token': 'Token',
    'airdrop': 'Airdrop',
    'web3': 'Web3',
    'layer 2': 'Layer2',
    'zk': 'ZK',
    'rollup': 'Rollup',
    'stablecoin': 'Stablecoin',
    'usdt': 'USDT',
    'usdc': 'USDC',
    'binance': 'Binance',
    'coinbase': 'Coinbase',
    'solana': 'Solana',
    'sol': 'Solana',
    'xrp': 'XRP',
    'ada': 'Cardano',
    'doge': 'Dogecoin',
    'shiba': 'Shiba',
    'shib': 'Shiba',
    'ton': 'TON',
    'avax': 'Avalanche',
    'dot': 'Polkadot',
    'trx': 'TRON',
    'link': 'Chainlink',
    'matic': 'Polygon',
    'uni': 'Uniswap',
    'ltc': 'Litecoin',
    'dai': 'DAI',
    // Финансы
    'stock': 'Stocks',
    'stocks': 'Stocks',
    'nasdaq': 'NASDAQ',
    's&p': 'S&P500',
    'dow': 'DOW',
    'etf': 'ETF',
    'ipo': 'IPO',
    'earnings': 'Earnings',
    'dividend': 'Dividend',
    'fed': 'Fed',
    'inflation': 'Inflation',
    'interest rate': 'InterestRate',
    'index': 'Index',
    'futures': 'Futures',
    'options': 'Options',
    'short': 'Short',
    'long': 'Long',
    'bull': 'Bull',
    'bear': 'Bear',
    'buyback': 'Buyback',
    'regulation': 'Regulation',
    'law': 'Law',
    'politics': 'Politics',
    'macro': 'Macro',
    'g7': 'G7',
    'g20': 'G20',
    'opec': 'OPEC',
    'brics': 'BRICS',
    'china': 'China',
    'russia': 'Russia',
    'usa': 'USA',
    'ukraine': 'Ukraine',
    // Компании/технологии
    'apple': 'Apple',
    'tesla': 'Tesla',
    'microsoft': 'Microsoft',
    'amazon': 'Amazon',
    'google': 'Google',
    'openai': 'OpenAI',
    'ai': 'AI',
    'ml': 'ML',
    'neural': 'Neural',
    'innovation': 'Innovation',
    'tech': 'Tech',
    // События
    'whale': 'Whale',
    'hack': 'Hack',
    'upgrade': 'Upgrade',
    'scandal': 'Scandal',
    'lawsuit': 'Lawsuit',
    'court': 'Court',
    'sanctions': 'Sanctions',
    'conflict': 'Conflict',
    'merger': 'Merger',
    'acquisition': 'Acquisition',
    'partnership': 'Partnership',
    'listing': 'Listing',
    'delisting': 'Delisting',
    'fork': 'Fork',
    'burn': 'Burn',
    'halving': 'Halving',
    'tokenomics': 'Tokenomics',
    'metaverse': 'Metaverse',
    'dao': 'DAO',
    'cefi': 'CeFi',
    'staking': 'Staking',
    'mining': 'Mining',
    'wallet': 'Wallet',
    'exchange': 'Exchange',
    'security': 'Security',
    'privacy': 'Privacy',
    'blockchain': 'Blockchain',
    'yield': 'Yield',
    'liquidity': 'Liquidity',
    'leverage': 'Leverage',
    'margin': 'Margin',
    'derivative': 'Derivative',
    'option': 'Options',
    'future': 'Futures',
    'spot': 'Spot',
    'market': 'Market',
    'trend': 'Trend',
    'volatility': 'Volatility',
    'volume': 'Volume',
    'capital': 'Capital',
    'fund': 'Fund',
    'portfolio': 'Portfolio',
    'asset': 'Asset',
    'liquidation': 'Liquidation',
    'arbitrage': 'Arbitrage',
    'flash loan': 'FlashLoan',
    'oracle': 'Oracle',
    'bridge': 'Bridge',
    'cross-chain': 'CrossChain',
    'layer 1': 'Layer1',
    'evm': 'EVM',
    'smart contract': 'SmartContract',
    'governance': 'Governance',
    'validator': 'Validator',
    'node': 'Node',
    'mainnet': 'Mainnet',
    'testnet': 'Testnet',
    'snapshot': 'Snapshot',
    'exploit': 'Exploit',
    'bug': 'Bug',
    'patch': 'Patch',
    'release': 'Release',
    'announcement': 'Announcement',
    'integration': 'Integration',
    'collaboration': 'Collaboration',
    'investment': 'Investment',
    'funding': 'Funding',
    'grant': 'Grant',
    'donation': 'Donation',
    'roadmap': 'Roadmap',
    'milestone': 'Milestone',
    'event': 'Event',
    'conference': 'Conference',
    'summit': 'Summit',
    'webinar': 'Webinar',
    'meetup': 'Meetup',
    'hackathon': 'Hackathon',
    'award': 'Award',
    'recognition': 'Recognition',
    'launch': 'Launch',
    'deployment': 'Deployment',
    'expansion': 'Expansion',
    'growth': 'Growth',
    'adoption': 'Adoption',
  };

  /// Возвращает максимум 3 дефолтных тега для отображения (по ключевым словам)
  List<String> get defaultTags {
    final text = (title + ' ' + (description ?? '') + ' ' + (content ?? '')).toLowerCase();
    final found = <String>{};
    _tagMapping.forEach((key, value) {
      if (text.contains(key)) found.add(value);
    });
    return found.take(3).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'aiGeneratedTitle': aiGeneratedTitle,
      'description': description,
      'imageUrl': imageUrl,
      'publishedAt': publishedAt.toIso8601String(),
      'source': source,
      'url': url,
      'sentiment': sentiment.toString().split('.').last,
      'tags': tags,
      'category': category.toString().split('.').last,
      'content': content,
      'summary': summary,
      'sentimentData': sentimentData?.toJson(),
      'rewrittenContent': rewrittenContent,
      'fetchedAt': fetchedAt.toIso8601String(),
      'cachedAt': cachedAt?.toIso8601String(),
      'newsCategory': newsCategory,
      'categoryConfidence': categoryConfidence,
      'categoryScores': categoryScores,
      'classificationTags': classificationTags,
    };
  }
}

enum NewsCategory {
  all,
  crypto,
  stocks,
  whales,
  ai,
  politics,
}
