class QuizQuestion {
  final int id;
  final String question;
  final List<String> options;
  final String correctAnswer;
  final QuizCategory category;
  final DifficultyLevel difficulty;
  final String explanation;

  QuizQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.category,
    required this.difficulty,
    required this.explanation,
  });

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    return QuizQuestion(
      id: json['id'],
      question: json['question'],
      options: List<String>.from(json['options']),
      correctAnswer: json['correct_answer'],
      category: QuizCategory.values.firstWhere(
        (e) => e.toString() == 'QuizCategory.${json['category']}',
        orElse: () => QuizCategory.blockchain,
      ),
      difficulty: DifficultyLevel.values.firstWhere(
        (e) => e.toString() == 'DifficultyLevel.${json['difficulty']}',
        orElse: () => DifficultyLevel.easy,
      ),
      explanation: json['explanation'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'options': options,
      'correct_answer': correctAnswer,
      'category': category.toString().split('.').last,
      'difficulty': difficulty.toString().split('.').last,
      'explanation': explanation,
    };
  }
}

enum QuizCategory {
  blockchain,
  trading,
  defi,
  security,
  general,
  technical,
  economics,
  altcoins,
  bitcoin,
  ethereum,
}

enum DifficultyLevel {
  easy,
  medium,
  hard,
  expert,
}

extension QuizCategoryExtension on QuizCategory {
  String get displayName {
    switch (this) {
      case QuizCategory.blockchain:
        return 'Блокчейн';
      case QuizCategory.trading:
        return 'Трейдинг';
      case QuizCategory.defi:
        return 'DeFi';
      case QuizCategory.security:
        return 'Безопасность';
      case QuizCategory.general:
        return 'Общие';
      case QuizCategory.technical:
        return 'Технические';
      case QuizCategory.economics:
        return 'Экономика';
      case QuizCategory.altcoins:
        return 'Альткоины';
      case QuizCategory.bitcoin:
        return 'Bitcoin';
      case QuizCategory.ethereum:
        return 'Ethereum';
    }
  }

  String get icon {
    switch (this) {
      case QuizCategory.blockchain:
        return '⛓️';
      case QuizCategory.trading:
        return '📈';
      case QuizCategory.defi:
        return '🔄';
      case QuizCategory.security:
        return '🔒';
      case QuizCategory.general:
        return '🌐';
      case QuizCategory.technical:
        return '⚙️';
      case QuizCategory.economics:
        return '💰';
      case QuizCategory.altcoins:
        return '🪙';
      case QuizCategory.bitcoin:
        return '₿';
      case QuizCategory.ethereum:
        return 'Ξ';
    }
  }
}

extension DifficultyLevelExtension on DifficultyLevel {
  String get displayName {
    switch (this) {
      case DifficultyLevel.easy:
        return 'Легкий';
      case DifficultyLevel.medium:
        return 'Средний';
      case DifficultyLevel.hard:
        return 'Сложный';
      case DifficultyLevel.expert:
        return 'Эксперт';
    }
  }

  String get color {
    switch (this) {
      case DifficultyLevel.easy:
        return '🟢';
      case DifficultyLevel.medium:
        return '🟡';
      case DifficultyLevel.hard:
        return '🟠';
      case DifficultyLevel.expert:
        return '🔴';
    }
  }
} 