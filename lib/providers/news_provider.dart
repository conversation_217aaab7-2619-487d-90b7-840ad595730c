import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:http/http.dart' as http;
import '../models/news_item.dart';
import '../services/news_service.dart';
import '../services/news_stream_service.dart';
import 'package:finance_ai/models/sentiment_types.dart';

// Условный импорт для Web
import '../services/news_stream_service_web.dart' if (dart.library.io) '../services/news_stream_service.dart' as web_service;

class NewsProvider with ChangeNotifier {
  final NewsService _newsService = NewsService();
  late final dynamic _streamService;

  NewsProvider() {
    // Используем правильную реализацию в зависимости от платформы
    if (kIsWeb) {
      _streamService = web_service.NewsStreamServiceWeb();
    } else {
      _streamService = NewsStreamService();
    }

    // 🔍 Тестируем подключение к бэкенду при запуске
    testBackendConnection();

    // 🚀 АВТОМАТИЧЕСКАЯ ЗАГРУЗКА КЕША ПРИ ИНИЦИАЛИЗАЦИИ
    _initializeNews();
  }

  // Инициализация новостей
  void _initializeNews() async {
    debugPrint('🚀 [NewsProvider] _initializeNews() ВЫЗВАН!');
    debugPrint('🚀 [NewsProvider] Инициализация новостей...');

    try {
      // Небольшая задержка для инициализации
      await Future.delayed(const Duration(milliseconds: 100));

      debugPrint('🚀 [NewsProvider] Вызываем fetchAllNews()...');
      // Загружаем новости
      await fetchAllNews();
      debugPrint('✅ [NewsProvider] fetchAllNews() завершен');
    } catch (e) {
      debugPrint('❌ [NewsProvider] Ошибка в _initializeNews: $e');
    }
  }

  List<NewsItem> _news = [];
  List<NewsItem> get news => _news;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  String _error = '';
  String get error => _error;

  bool _isStreamConnected = false;
  bool get isStreamConnected => _isStreamConnected;

  StreamSubscription? _streamSubscription;

  // Selected filters
  NewsCategory _selectedCategory = NewsCategory.all;
  NewsCategory get selectedCategory => _selectedCategory;

  List<String> _selectedTags = [];
  List<String> get selectedTags => _selectedTags;

  SentimentType? _selectedSentiment;
  SentimentType? get selectedSentiment => _selectedSentiment;

  // Дополнительная фильтрация по строковым категориям (crypto, stocks, whales)
  String _selectedStringCategory = '';
  String get selectedStringCategory => _selectedStringCategory;

  String _searchQuery = '';
  String get searchQuery => _searchQuery;

  // Пагинация
  int _currentPage = 1;
  int get currentPage => _currentPage;

  bool _hasMoreNews = true;
  bool get hasMoreNews => _hasMoreNews;

  bool _isLoadingMore = false;
  bool get isLoadingMore => _isLoadingMore;

  // Filtered news based on selected filters
  List<NewsItem> get filteredNews {
    return _news.where((news) {
      // Filter by category
      if (_selectedCategory != NewsCategory.all && news.category != _selectedCategory) {
        return false;
      }

      // Filter by tags
      if (_selectedTags.isNotEmpty) {
        bool hasSelectedTag = false;
        for (final tag in _selectedTags) {
          if (news.tags.contains(tag)) {
            hasSelectedTag = true;
            break;
          }
        }
        if (!hasSelectedTag) {
          return false;
        }
      }

      // Filter by sentiment
      if (_selectedSentiment != null && news.sentiment != _selectedSentiment) {
        return false;
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!news.title.toLowerCase().contains(query) &&
            !news.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  // Fetch headlines from backend with AI sentiment analysis
  Future<void> fetchTopHeadlines() async {
    _setLoading(true);
    _clearError();

    try {
      final sentimentFilter = _selectedSentiment?.toString().split('.').last;
      final tagsFilter = _selectedTags.isNotEmpty ? _selectedTags.join(',') : null;

      final fetchedNews = await _newsService.getNewsFromBackend(
        sentiment: sentimentFilter,
        tags: tagsFilter,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      _news = fetchedNews;
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch news: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Search news
  Future<void> searchNews(String query) async {
    if (query.isEmpty) {
      return fetchTopHeadlines();
    }

    _setLoading(true);
    _clearError();
    _searchQuery = query;

    try {
      final fetchedNews = await _newsService.getNewsFromBackend(
        search: query,
        sentiment: _selectedSentiment?.toString().split('.').last,
        tags: _selectedTags.isNotEmpty ? _selectedTags.join(',') : null,
      );
      _news = fetchedNews;
      notifyListeners();
    } catch (e) {
      _setError('Failed to search news: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Set category filter
  void setCategory(NewsCategory category) {
    _selectedCategory = category;
    notifyListeners();
  }

  // Toggle tag filter
  void toggleTag(String tag) {
    if (_selectedTags.contains(tag)) {
      _selectedTags.remove(tag);
    } else {
      _selectedTags.add(tag);
    }
    notifyListeners();
  }

  // Set sentiment filter
  void setSentiment(SentimentType? sentiment) {
    _selectedSentiment = sentiment;
    notifyListeners();
  }

  void setStringCategory(String category) {
    _selectedStringCategory = category;
    notifyListeners();
  }

  // Clear all filters
  void clearFilters() {
    _selectedCategory = NewsCategory.all;
    _selectedTags = [];
    _selectedSentiment = null;
    _searchQuery = '';
    notifyListeners();
  }

  // Clear search query only
  void clearSearch() {
    _searchQuery = '';
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void _setError(String message) {
    _error = message;
    notifyListeners();
  }

  void _clearError() {
    _error = '';
    notifyListeners();
  }

  // 🚀 УЛУЧШЕННАЯ ЗАГРУЗКА: Загружаем ВЕСЬ кэш при запуске
  Future<void> fetchAllNews() async {
    debugPrint('🚀 [NewsProvider] fetchAllNews() ВЫЗВАН!');
    debugPrint('🔄 [NewsProvider] Начинаем загрузку всех новостей...');
    _setLoading(true);
    _clearError();
    _currentPage = 1; // Сбрасываем пагинацию
    _hasMoreNews = true;

    try {
      debugPrint('📡 [NewsProvider] Отправляем запрос на бэкенд...');

      // 📦 ЗАГРУЖАЕМ МАКСИМУМ НОВОСТЕЙ ИЗ КЭША (до 800 новостей)
      final fetchedNews = await _newsService.getNewsFromBackend(
        page: _currentPage,
        pageSize: 800, // 🎯 УВЕЛИЧЕНО: загружаем весь кэш сразу
        category: _selectedStringCategory.isNotEmpty ? _selectedStringCategory : null,
      );

      debugPrint('📦 [NewsProvider] Получен ответ от бэкенда: ${fetchedNews.length} новостей');

      if (fetchedNews.isEmpty) {
        debugPrint('⚠️ [NewsProvider] КРИТИЧЕСКАЯ ПРОБЛЕМА: Получен пустой список новостей!');
        debugPrint('🔍 [NewsProvider] Проверьте работу NewsService.getNewsFromBackend()');
      } else {
        debugPrint('✅ [NewsProvider] Новости успешно получены, устанавливаем в _news');
      }

      _news = fetchedNews;

      // Если получили меньше 800 новостей, значит больше нет
      if (fetchedNews.length < 800) {
        _hasMoreNews = false;
      }

      debugPrint('✅ [NewsProvider] Загружено ${fetchedNews.length} новостей из кэша при запуске');

      // Показываем первые несколько новостей для отладки
      if (fetchedNews.isNotEmpty) {
        debugPrint('📰 [NewsProvider] Первые новости:');
        for (int i = 0; i < 3 && i < fetchedNews.length; i++) {
          debugPrint('   ${i + 1}. ${fetchedNews[i].title}');
        }
      } else {
        debugPrint('⚠️ [NewsProvider] ВНИМАНИЕ: Получен пустой список новостей!');
      }

      notifyListeners();
    } catch (e) {
      debugPrint('❌ [NewsProvider] Ошибка загрузки новостей: $e');
      _setError('Failed to fetch news: $e');
    } finally {
      _setLoading(false);
    }
  }



  // Загрузка дополнительных новостей (пагинация)
  Future<void> loadMoreNews() async {
    if (_isLoadingMore || !_hasMoreNews || _isLoading) return;

    _isLoadingMore = true;
    notifyListeners();

    try {
      _currentPage++;
      final moreNews = await _newsService.getNewsFromBackend(
        page: _currentPage,
        pageSize: 100, // 🎯 УВЕЛИЧЕНО: загружаем больше новостей за раз
        sentiment: _selectedSentiment?.toString().split('.').last,
        tags: _selectedTags.isNotEmpty ? _selectedTags.join(',') : null,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
        category: _selectedStringCategory.isNotEmpty ? _selectedStringCategory : null,
      );

      if (moreNews.isNotEmpty) {
        // Добавляем новые новости к существующим, избегая дубликатов
        final existingIds = _news.map((news) => news.id).toSet();
        final uniqueNews = moreNews.where((news) => !existingIds.contains(news.id)).toList();

        _news.addAll(uniqueNews);

        // Если получили меньше 100 новостей, значит больше нет
        if (moreNews.length < 100) {
          _hasMoreNews = false;
        }
      } else {
        _hasMoreNews = false;
      }

      notifyListeners();
    } catch (e) {
      _currentPage--; // Откатываем страницу при ошибке
      debugPrint('Failed to load more news: $e');
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  // Подключение к real-time потоку новостей
  Future<void> connectToNewsStream() async {
    if (_isStreamConnected) {
      debugPrint('[NewsProvider] Уже подключен к потоку новостей');
      return;
    }

    try {
      debugPrint('[NewsProvider] Начинаем подключение к потоку новостей...');

      // Подписываемся на поток событий ДО подключения
      _streamSubscription = _streamService.newsStream.listen(
        (event) {
          debugPrint('[NewsProvider] Получено событие: ${event.type}');

          // Обрабатываем события из обеих реализаций
          final eventType = event.type.toString();

          if (eventType.contains('connected')) {
            debugPrint('[NewsProvider] Подключение к потоку установлено');
            _isStreamConnected = true;
            _clearError();
            notifyListeners();
          } else if (eventType.contains('newNews')) {
            if (event.news != null) {
              debugPrint('[NewsProvider] Получена новая новость: ${event.news!.title}');

              // Проверяем на дубликаты перед добавлением
              final isDuplicate = _news.any((existingNews) =>
                existingNews.id == event.news!.id ||
                (existingNews.title == event.news!.title &&
                 existingNews.source == event.news!.source)
              );

              if (!isDuplicate) {
                _news.insert(0, event.news!);
                debugPrint('[NewsProvider] ✅ Новость добавлена в ленту: ${event.news!.title}');
                notifyListeners();
              } else {
                debugPrint('[NewsProvider] 🔄 Дубликат отклонен: ${event.news!.title}');
              }
            }
          } else if (eventType.contains('disconnected')) {
            debugPrint('[NewsProvider] Отключение от потока');
            _isStreamConnected = false;
            notifyListeners();
          } else if (eventType.contains('error')) {
            debugPrint('[NewsProvider] Ошибка в потоке: ${event.message}');
            _setError('Stream error: ${event.message}');
          }
        },
        onError: (error) {
          debugPrint('[NewsProvider] Ошибка потока: $error');
          _setError('Stream connection error: $error');
          _isStreamConnected = false;
          notifyListeners();
        },
      );

      // Теперь подключаемся к серверу
      await _streamService.connect();

      debugPrint('[NewsProvider] Инициализация подключения к потоку завершена');
    } catch (e) {
      debugPrint('[NewsProvider] Ошибка подключения к потоку: $e');
      _setError('Failed to connect to news stream: $e');
      _isStreamConnected = false;
      notifyListeners();
    }
  }

  // Отключение от потока новостей
  void disconnectFromNewsStream() {
    debugPrint('[NewsProvider] Отключение от потока новостей');
    _streamSubscription?.cancel();
    _streamSubscription = null;
    _streamService.disconnect();
    _isStreamConnected = false;
    notifyListeners();
  }

  // Переподключение к потоку новостей
  Future<void> reconnectToNewsStream() async {
    debugPrint('[NewsProvider] Переподключение к потоку новостей');
    disconnectFromNewsStream();
    await Future.delayed(const Duration(milliseconds: 1000)); // Небольшая задержка
    await connectToNewsStream();
  }

  // 🔧 ТЕСТИРОВАНИЕ ПОДКЛЮЧЕНИЯ К БЭКЕНДУ
  Future<void> testBackendConnection() async {
    try {
      debugPrint('🔍 [NewsProvider] Тестируем подключение к бэкенду...');

      final response = await http.get(Uri.parse('http://localhost:4000/status'));

      debugPrint('📡 [NewsProvider] Статус бэкенда: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        debugPrint('✅ [NewsProvider] Бэкенд работает! Новостей в кэше: ${data['totalNews'] ?? 'неизвестно'}');
      } else {
        debugPrint('❌ [NewsProvider] Бэкенд недоступен: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ [NewsProvider] Ошибка подключения к бэкенду: $e');
    }
  }

  @override
  void dispose() {
    disconnectFromNewsStream();
    super.dispose();
  }
}
