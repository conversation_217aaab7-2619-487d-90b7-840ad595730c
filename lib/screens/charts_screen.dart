import 'dart:async';
import 'dart:math' as math;
import 'dart:ui'; // For ImageFilter / glass blur
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_bottom_navigation.dart';
import '../providers/crypto_provider.dart';
import '../widgets/mini_chart.dart';
import '../models/crypto_currency.dart';
import '../services/local_crypto_icons_service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import '../widgets/cosmic_background.dart';
import '../utils/custom_page_transitions.dart';
import '../screens/coin_detail_screen.dart';
import '../services/fear_greed_index_service.dart';
import '../services/crypto_sentiment_index_service.dart';
import '../widgets/fear_greed_gauge.dart';
import '../services/altcoin_season_index_service.dart';
import '../providers/news_provider.dart';
import '../models/sentiment_types.dart';

class ChartsScreen extends StatefulWidget {
  const ChartsScreen({Key? key}) : super(key: key);

  @override
  State<ChartsScreen> createState() => _ChartsScreenState();
}

class _ChartsScreenState extends State<ChartsScreen> with WidgetsBindingObserver, SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();

  // Время последнего обновления данных
  late DateTime _lastUpdateTime;

  // Таймер для автоматического обновления данных
  Timer? _refreshTimer;

  // Таймер для автоматической прокрутки бегущей строки
  Timer? _scrollTimer;

  // Флаг для отслеживания состояния приложения
  bool _isAppActive = true;

  // Текущий выбранный фильтр
  String _selectedFilter = 'All';

  // Список криптовалют для отображения
  List<CryptoCurrency> _displayedCryptos = [];

  // Переменная для переключения количества колонок (2 или 3)
  int _columnsCount = 2;

  // Контроллеры и переменные для поиска
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  List<CryptoCurrency> _searchResults = [];
  bool _isSearching = false;
  OverlayEntry? _overlayEntry;

  // Таймер для debounce поиска
  Timer? _searchDebounceTimer;

  // Флаг для предотвращения множественных навигаций
  bool _isNavigating = false;

  int? _fearGreedIndex;
  bool _isLoadingFearGreed = false;

  double? _cryptoSentimentIndex;
  bool _isLoadingSentiment = false;

  double? _altcoinSeasonIndex;
  bool _isLoadingAltcoinSeason = false;

  // ---------- New pagination & scroll helpers ----------
  static const int _itemsPerPage = 50;
  int _currentPage = 0;
  final ScrollController _cryptoListController = ScrollController();
  bool _showBackToTopButton = false;

  // Background animation controller for slow gradient movement
  late final AnimationController _bgController;

  @override
  void initState() {
    super.initState();
    _lastUpdateTime = DateTime.now();

    // Регистрируем наблюдатель за жизненным циклом приложения
    WidgetsBinding.instance.addObserver(this);

    // Слушатель для поля поиска
    _searchController.addListener(_onSearchChanged);
    _searchFocusNode.addListener(_onSearchFocusChanged);

    // Загружаем данные о криптовалютах при инициализации
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
      if (cryptoProvider.allCryptos.isEmpty && !cryptoProvider.isLoading) {
        cryptoProvider.loadAllCryptos();
      }

      // Запускаем автоматическое обновление данных
      _startPeriodicDataRefresh();

      // Запускаем автоматическую прокрутку бегущей строки
      _startAutoScroll();
    });

    _loadFearGreedIndex();
    _loadCryptoSentimentIndex();
    _loadAltcoinSeasonIndex();

    // Слушатель для вертикального списка (показываем кнопку возврата наверх)
    _cryptoListController.addListener(() {
      if (!_cryptoListController.hasClients) return;
      final shouldShow = _cryptoListController.offset > 400;
      if (shouldShow != _showBackToTopButton) {
        setState(() {
          _showBackToTopButton = shouldShow;
        });
      }
    });

    // Init background gradient animation (30-second loop)
    _bgController = AnimationController(vsync: this, duration: const Duration(seconds: 30))..repeat();
  }

  Future<void> _loadFearGreedIndex() async {
    setState(() { _isLoadingFearGreed = true; });
    final value = await FearGreedIndexService.fetchFearGreedIndex();
    setState(() {
      _fearGreedIndex = value;
      _isLoadingFearGreed = false;
    });
  }

  Future<void> _loadCryptoSentimentIndex() async {
    setState(() { _isLoadingSentiment = true; });
    final value = await CryptoSentimentIndexService.fetchCryptoSentimentIndex(
      fallbackFearGreed: _fearGreedIndex,
      fallbackAltcoinSeason: _altcoinSeasonIndex,
    );
    print('CryptoSentimentIndex: ' + (value?.toString() ?? 'null'));
    setState(() {
      _cryptoSentimentIndex = value;
      _isLoadingSentiment = false;
    });
  }

  Future<void> _loadAltcoinSeasonIndex() async {
    setState(() { _isLoadingAltcoinSeason = true; });
    final value = await AltcoinSeasonIndexService.fetchAltcoinSeasonIndex();
    print('AltcoinSeasonIndex: ' + (value?.toString() ?? 'null'));
    setState(() {
      _altcoinSeasonIndex = value;
      _isLoadingAltcoinSeason = false;
    });
  }

  @override
  void dispose() {
    // Отменяем таймеры при уничтожении виджета
    _refreshTimer?.cancel();
    _scrollTimer?.cancel();
    _searchDebounceTimer?.cancel();

    // Отписываемся от наблюдения за жизненным циклом
    WidgetsBinding.instance.removeObserver(this);

    // Очищаем ресурсы поиска
    _searchController.dispose();
    _searchFocusNode.dispose();
    _removeOverlay();

    // Сбрасываем флаг навигации
    _isNavigating = false;

    _scrollController.dispose();
    // Контроллер вертикального списка
    _cryptoListController.dispose();
    _bgController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Отслеживаем состояние приложения для оптимизации обновлений
    if (state == AppLifecycleState.resumed) {
      // Приложение стало активным
      _isAppActive = true;

      // Немедленно обновляем данные
      _refreshData();

      // Перезапускаем таймеры
      _startPeriodicDataRefresh();
      _startAutoScroll();
    } else if (state == AppLifecycleState.paused ||
               state == AppLifecycleState.inactive ||
               state == AppLifecycleState.detached) {
      // Приложение стало неактивным
      _isAppActive = false;

      // Останавливаем таймеры для экономии ресурсов
      _refreshTimer?.cancel();
      _scrollTimer?.cancel();
    }
  }

  // Метод для запуска периодического обновления данных
  void _startPeriodicDataRefresh() {
    // Отменяем существующий таймер, если он есть
    _refreshTimer?.cancel();

    // Создаем новый таймер, который будет срабатывать каждую минуту
    _refreshTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (_isAppActive) {
        _refreshData();
      }
    });
  }

  // Метод для обновления данных
  void _refreshData() {
    if (!mounted) return;

    final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
    cryptoProvider.loadAllCryptos();

    // Обновляем время последнего обновления
    setState(() {
      _lastUpdateTime = DateTime.now();
    });
  }

  // Метод для запуска автоматической прокрутки бегущей строки
  void _startAutoScroll() {
    // Отменяем существующий таймер, если он есть
    _scrollTimer?.cancel();

    // Задержка перед началом прокрутки для инициализации
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      // Проверяем, что контроллер прокрутки имеет клиентов
      if (_scrollController.hasClients) {
        // Создаем таймер для более частой и плавной прокрутки
        _scrollTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
          if (!mounted || !_isAppActive) {
            timer.cancel();
            return;
          }

          if (_scrollController.hasClients) {
            // Получаем текущую позицию и максимальную позицию
            final currentOffset = _scrollController.offset;
            final maxScrollExtent = _scrollController.position.maxScrollExtent;
            
            // Вычисляем следующую позицию прокрутки
            final nextOffset = currentOffset + 150;
            
            // Проверяем, если осталось меньше двух прокруток до конца
            final remainingScrolls = (maxScrollExtent - currentOffset) / 150;
            
            if (remainingScrolls <= 2) {
              // Предзагружаем новые элементы, плавно перемещаясь в начало списка
              // но продолжая движение в том же направлении
              final newPosition = currentOffset - (maxScrollExtent * 0.3);
              _scrollController.jumpTo(math.max(0, newPosition));
            } else {
              // Плавно прокручиваем к следующей позиции
              _scrollController.animateTo(
                nextOffset,
                duration: const Duration(milliseconds: 1200),
                curve: Curves.easeInOut,
              );
            }
          }
        });

        // Начинаем прокрутку сразу
        _scrollController.animateTo(
          150,
          duration: const Duration(milliseconds: 1200),
          curve: Curves.easeInOut,
        );
      } else {
        // Если контроллер еще не имеет клиентов, пробуем позже
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) _startAutoScroll();
        });
      }
    });
  }

  // Метод для создания фильтра с современным дизайном в стиле iOS
  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = label;
          _updateDisplayedCryptos();
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        margin: const EdgeInsets.only(right: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF007AFF) // iOS blue
              : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20.0),
          border: Border.all(
            color: isSelected 
                ? const Color(0xFF007AFF)
                : Colors.white.withOpacity(0.2),
            width: 1.5,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: const Color(0xFF007AFF).withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                  BoxShadow(
                    color: const Color(0xFF007AFF).withOpacity(0.2),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.white.withOpacity(0.8),
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            fontSize: 14.0,
            letterSpacing: 0.3,
          ),
        ),
      ),
    );
  }

  // Метод для обновления отображаемых криптовалют в зависимости от выбранного фильтра
  void _updateDisplayedCryptos() {
    final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);

    switch (_selectedFilter) {
      case 'Favorites':
        _displayedCryptos = cryptoProvider.favoritesCryptos;
        break;
      case 'Trending':
        // Логика для трендовых токенов: популярность, объем торгов, новые листинги
        // Сортируем по комбинации объема торгов и рыночной капитализации (показатель популярности)
        _displayedCryptos = List.from(cryptoProvider.allCryptos)
          ..sort((a, b) {
            // Вычисляем "трендовость" как комбинацию объема торгов и рыночной капитализации
            double trendScoreA = (a.volume24h / a.marketCap) * 1000; // Нормализуем
            double trendScoreB = (b.volume24h / b.marketCap) * 1000;

            // Добавляем бонус для AI и Meme токенов
            if (a.categories.contains('AI Agents') || a.categories.contains('Memes')) {
              trendScoreA *= 1.5;
            }
            if (b.categories.contains('AI Agents') || b.categories.contains('Memes')) {
              trendScoreB *= 1.5;
            }

            return trendScoreB.compareTo(trendScoreA);
          });

        // Берем топ-50 самых трендовых
        _displayedCryptos = _displayedCryptos.take(50).toList();
        break;

      case 'Gainers':
        // Логика для Gainers: только токены с положительным ростом, отсортированные по проценту роста
        _displayedCryptos = cryptoProvider.allCryptos
          .where((crypto) => (crypto.priceChangePercentage24h ?? 0) > 0) // Только растущие
          .toList()
          ..sort((a, b) => (b.priceChangePercentage24h ?? 0).compareTo(a.priceChangePercentage24h ?? 0)); // Сортируем по убыванию роста
        break;
      case 'Losers':
        _displayedCryptos = cryptoProvider.allCryptos
          .where((crypto) => (crypto.priceChangePercentage24h ?? 0) < 0) // Только падающие (с проверкой на null)
          .toList()
          ..sort((a, b) => (a.priceChangePercentage24h ?? 0).compareTo(b.priceChangePercentage24h ?? 0)); // Сортируем по возрастанию убытка
        break;
      case 'AI':
        _displayedCryptos = cryptoProvider.aiAgentsCryptos;
        break;
      case 'Memes':
        _displayedCryptos = cryptoProvider.memesCryptos;
        break;
      case 'All':
      default:
        _displayedCryptos = cryptoProvider.allCryptos;
        break;
    }

    // После обновления данных возвращаемся на первую страницу
    _currentPage = 0;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CryptoProvider>(
      builder: (context, cryptoProvider, _) {
        // Обновляем список отображаемых криптовалют
        if (_displayedCryptos.isEmpty) {
          _updateDisplayedCryptos();
        }

        final cryptos = cryptoProvider.allCryptos;

        // Подсчёт процента растущих и падающих
        int upCount = cryptos.where((c) => c.priceChangePercentage24h > 0).length;
        int downCount = cryptos.where((c) => c.priceChangePercentage24h < 0).length;
        int total = cryptos.length;
        double upPercent = total > 0 ? upCount / total : 0;
        double downPercent = total > 0 ? downCount / total : 0;

        // Определяем цвет настроения для фона и sidebar
        Color sentimentColor;
        if (upPercent > 0.6) {
          sentimentColor = const Color(0xFF34C759); // Зеленый
        } else if (downPercent > 0.4) {
          sentimentColor = const Color(0xFFFF3B30); // Красный
        } else {
          sentimentColor = const Color(0xFF4A90E2); // Серый/нейтральный
        }

        return Scaffold(
          backgroundColor: const Color(0xFF0A0B0D),
          extendBodyBehindAppBar: true,
          extendBody: true,
          floatingActionButton: null,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: Builder(
              builder: (context) => IconButton(
                icon: const Icon(Icons.menu, color: Colors.white),
                onPressed: () => Scaffold.of(context).openDrawer(),
              ),
            ),
            title: const Text('Графики', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
            centerTitle: false,
          ),
          drawer: Drawer(
            backgroundColor: const Color(0xFF181A20),
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                DrawerHeader(
                  decoration: const BoxDecoration(
                    color: Color(0xFF23252B),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: const [
                      Text('Меню', style: TextStyle(color: Colors.white, fontSize: 22, fontWeight: FontWeight.bold)),
                    ],
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.article, color: Colors.white),
                  title: const Text('Новости', style: TextStyle(color: Colors.white)),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/news');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.assessment, color: Colors.white),
                  title: const Text('Индексы', style: TextStyle(color: Colors.white)),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/indices');
                  },
                ),
                // Можно добавить другие пункты меню
              ],
            ),
          ),
          body: Stack(
            children: [
              _buildAnimatedMintlifyBackground(sentimentColor),
              SafeArea(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Основной контент — 2/3 ширины
                    Expanded(
                      flex: 2,
                      child: Column(
                        children: [
                          // Верхняя панель с фильтрами и кнопками управления
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                            child: Column(
                              children: [
                                // Первая строка - фильтры и управляющие кнопки
                                Row(
                                  children: [
                                    // Переключатель колонок (вместо кнопки назад)
                                    Container(
                                      decoration: BoxDecoration(
                                        color: Colors.black.withOpacity(0.3),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1.5),
                                      ),
                                      child: IconButton(
                                        icon: Icon(
                                          _columnsCount == 2 ? Icons.view_column : Icons.view_agenda,
                                          color: Colors.white,
                                          size: 22,
                                        ),
                                        onPressed: () {
                                          setState(() {
                                            _columnsCount = _columnsCount == 2 ? 3 : 2;
                                          });
                                        },
                                      ),
                                    ),

                                    const SizedBox(width: 12),

                                    // Фильтры (перемещены сюда)
                                    Expanded(
                                      child: SingleChildScrollView(
                                        scrollDirection: Axis.horizontal,
                                        child: Row(
                                          children: [
                                            _buildPremiumFilterChip('All', _selectedFilter == 'All'),
                                            _buildPremiumFilterChip('Trending', _selectedFilter == 'Trending'),
                                            _buildPremiumFilterChip('Gainers', _selectedFilter == 'Gainers'),
                                            _buildPremiumFilterChip('Losers', _selectedFilter == 'Losers'),
                                            _buildPremiumFilterChip('AI', _selectedFilter == 'AI'),
                                            _buildPremiumFilterChip('Memes', _selectedFilter == 'Memes'),
                                            
                                            const SizedBox(width: 12.0),
                                            
                                            // Кнопки "Новости" и "Индексы"
                                            ElevatedButton(
                                              onPressed: () {
                                                Navigator.pushNamed(context, '/news');
                                              },
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: Colors.white.withOpacity(0.08),
                                                foregroundColor: Colors.white,
                                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                                              ),
                                              child: const Text('Новости', style: TextStyle(fontWeight: FontWeight.w600)),
                                            ),
                                            const SizedBox(width: 8),
                                            ElevatedButton(
                                              onPressed: () {
                                                Navigator.pushNamed(context, '/indices');
                                              },
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: Colors.white.withOpacity(0.08),
                                                foregroundColor: Colors.white,
                                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                                              ),
                                              child: const Text('Индексы', style: TextStyle(fontWeight: FontWeight.w600)),
                                            ),
                                            const SizedBox(width: 12.0),
                                            
                                            // Кнопка-лупа в неоморфном стиле
                                            Container(
                                              height: 44,
                                              margin: const EdgeInsets.only(left: 12),
                                              padding: const EdgeInsets.symmetric(horizontal: 8),
                                              decoration: BoxDecoration(
                                                color: Colors.white.withOpacity(0.05),
                                                borderRadius: BorderRadius.circular(22),
                                                border: Border.all(color: Colors.white.withOpacity(0.2), width: 1.5),
                                              ),
                                              child: Row(
                                                children: [
                                                  Icon(Icons.search, color: Colors.white70),
                                                  const SizedBox(width: 6),
                                                  SizedBox(
                                                    width: 140,
                                                    child: TextField(
                                                      controller: _searchController,
                                                      focusNode: _searchFocusNode,
                                                      style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w500),
                                                      decoration: const InputDecoration(
                                                        border: InputBorder.none,
                                                        hintText: 'Search...',
                                                        hintStyle: TextStyle(color: Colors.white54),
                                                        isDense: true,
                                                        contentPadding: EdgeInsets.symmetric(vertical: 8),
                                                      ),
                                                      onChanged: (val) => _onSearchChanged(),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),

                                    const SizedBox(width: 12), // Смещаем кнопки справа ближе к поиску

                                    // Время последнего обновления
                                    Container(
                                      margin: const EdgeInsets.only(right: 8),
                                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                      decoration: BoxDecoration(
                                        color: Colors.black.withOpacity(0.4),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
                                        boxShadow: [
                                          BoxShadow(
                                            color: sentimentColor.withOpacity(0.2),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.access_time,
                                            color: Colors.white.withOpacity(0.8),
                                            size: 14,
                                          ),
                                          const SizedBox(width: 6),
                                          Text(
                                            _getFormattedDateTime(),
                                            style: TextStyle(
                                              color: Colors.white.withOpacity(0.9),
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    // Избранное
                                    Container(
                                      margin: const EdgeInsets.only(right: 8),
                                      decoration: BoxDecoration(
                                        color: _selectedFilter == 'Favorites' 
                                            ? Colors.amber.withOpacity(0.2)
                                            : Colors.black.withOpacity(0.3),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: _selectedFilter == 'Favorites' 
                                              ? Colors.amber.withOpacity(0.6)
                                              : Colors.white.withOpacity(0.2), 
                                          width: 1.5
                                        ),
                                        boxShadow: _selectedFilter == 'Favorites' 
                                            ? [
                                                BoxShadow(
                                                  color: Colors.amber.withOpacity(0.3),
                                                  blurRadius: 8,
                                                  offset: const Offset(0, 2),
                                                ),
                                              ]
                                            : [],
                                      ),
                                      child: IconButton(
                                        icon: Icon(
                                          _selectedFilter == 'Favorites' ? Icons.star_rounded : Icons.star_border_rounded,
                                          color: _selectedFilter == 'Favorites' ? Colors.amber : Colors.white,
                                          size: 20,
                                        ),
                                        onPressed: () {
                                          setState(() {
                                            _selectedFilter = _selectedFilter == 'Favorites' ? 'All' : 'Favorites';
                                            _updateDisplayedCryptos();
                                          });
                                        },
                                      ),
                                    ),

                                    // Обновление
                                    Container(
                                      decoration: BoxDecoration(
                                        color: Colors.black.withOpacity(0.3),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1.5),
                                      ),
                                      child: IconButton(
                                        icon: const Icon(Icons.refresh, color: Colors.white, size: 22),
                                        onPressed: () {
                                          cryptoProvider.loadAllCryptos();
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            const SnackBar(
                                              content: Text('Refreshing data...'),
                                              duration: Duration(seconds: 1),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),

                          // --- Carousel moved inside scrollable list header ---
                          const SizedBox(height: 8),

                          // Основной список криптовалют или результаты поиска
                          Expanded(
                            child: _isSearching && _searchResults.isNotEmpty
                                ? _buildSearchResultsList()
                                : Consumer<CryptoProvider>(
                                    builder: (context, cryptoProvider, child) {
                                      if (cryptoProvider.isLoading) {
                                        return Center(
                                          child: CircularProgressIndicator(
                                            valueColor: AlwaysStoppedAnimation<Color>(sentimentColor),
                                          ),
                                        );
                                      }

                                      if (_displayedCryptos.isEmpty) {
                                        return const Center(
                                          child: Text(
                                            'No data available',
                                            style: TextStyle(color: Colors.white),
                                          ),
                                        );
                                      }

                                      // -------- Основной список с пагинацией --------
                                      final totalPages = (_displayedCryptos.length / _itemsPerPage).ceil();
                                      final pageStart = _currentPage * _itemsPerPage;
                                      final pageEnd = math.min(pageStart + _itemsPerPage, _displayedCryptos.length);
                                      final pageCryptos = _displayedCryptos.sublist(pageStart, pageEnd);

                                      return Column(
                                        children: [
                                          Expanded(
                                            child: ListView.builder(
                                              controller: _cryptoListController,
                                              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                                              itemCount: (pageCryptos.length / _columnsCount).ceil() + 2, // header + footer
                                              itemBuilder: (context, index) {
                                                // Header (carousel)
                                                if (index == 0) {
                                                  return Container(
                                                    height: 120,
                                                    margin: const EdgeInsets.only(bottom: 12),
                                                    child: ListView.builder(
                                                      controller: _scrollController,
                                                      scrollDirection: Axis.horizontal,
                                                      itemCount: cryptos.length * 3,
                                                      itemBuilder: (ctx, i) {
                                                        final c = cryptos[i % cryptos.length];
                                                        final col = c.priceChangePercentage24h >= 0 ? sentimentColor : const Color(0xFFFF4444);
                                                        return GestureDetector(
                                                          onTap: () => Navigator.of(context).push(
                                                            CustomPageTransitions.buildCoinDetailTransition(page: const CoinDetailScreen(), settings: RouteSettings(arguments: c)),
                                                          ),
                                                          child: LayoutBuilder(
                                                            builder: (context, constraints) {
                                                              final isMobile = constraints.maxWidth < 600;
                                                              final cardWidth = (isMobile
                                                                ? math.max(160, MediaQuery.of(context).size.width * 0.9)
                                                                : math.max(140, MediaQuery.of(context).size.width * 0.32)).toDouble();
                                                              final cardMargin = EdgeInsets.symmetric(horizontal: isMobile ? 10.0 : 6.0, vertical: isMobile ? 14.0 : 8.0);
                                                              final cardPadding = EdgeInsets.all(isMobile ? 18.0 : 12.0);
                                                              final priceFontSize = isMobile ? 15.0 : 11.0;
                                                              return Container(
                                                                width: cardWidth,
                                                                margin: cardMargin,
                                                                padding: cardPadding,
                                                                decoration: BoxDecoration(
                                                                  color: Colors.white.withOpacity(0.04),
                                                                  borderRadius: BorderRadius.circular(16.0),
                                                                  border: Border.all(color: Colors.white.withOpacity(0.18)),
                                                                ),
                                                                child: Column(
                                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                                  children: [
                                                                    Row(children: [
                                                                      CircleAvatar(radius: 12, backgroundImage: NetworkImage(c.imageUrl)),
                                                                      const SizedBox(width: 6),
                                                                      Expanded(child: Text(c.symbol, style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold))),
                                                                      Container(padding: const EdgeInsets.symmetric(horizontal:4,vertical:1),decoration: BoxDecoration(color: col.withOpacity(.25),borderRadius: BorderRadius.circular(4)),child: Text('${c.priceChangePercentage24h>=0?'+':'-'}${c.priceChangePercentage24h.abs().toStringAsFixed(1)}%',style: TextStyle(color: col,fontSize:8))),
                                                                    ]),
                                                                    const Spacer(),
                                                                    Text('\$${_formatPrice(c.price)}',style: TextStyle(color: Colors.white,fontSize: priceFontSize,fontWeight: FontWeight.bold)),
                                                                  ],
                                                                ),
                                                              );
                                                            },
                                                          ),
                                                        );
                                                      },
                                                    ),
                                                  );
                                                }

                                                // Последний элемент – футер
                                                final rowsCount = (pageCryptos.length / _columnsCount).ceil();
                                                if (index == rowsCount + 1) {
                                                  return Padding(
                                                    padding: const EdgeInsets.symmetric(vertical: 12.0),
                                                    child: Row(
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        IconButton(
                                                          icon: const Icon(Icons.chevron_left),
                                                          color: Colors.white,
                                                          onPressed: _currentPage > 0
                                                              ? () {
                                                                  setState(() {
                                                                    _currentPage--;
                                                                    _cryptoListController.jumpTo(0);
                                                                  });
                                                                }
                                                              : null,
                                                        ),
                                                        Container(
                                                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                                          decoration: BoxDecoration(
                                                            color: Colors.white.withOpacity(0.08),
                                                            borderRadius: BorderRadius.circular(8),
                                                            border: Border.all(color: Colors.white.withOpacity(0.15)),
                                                          ),
                                                          child: Text(
                                                            'Page ${_currentPage + 1} / $totalPages',
                                                            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                                                          ),
                                                        ),
                                                        IconButton(
                                                          icon: const Icon(Icons.chevron_right),
                                                          color: Colors.white,
                                                          onPressed: _currentPage < totalPages - 1
                                                              ? () {
                                                                  setState(() {
                                                                    _currentPage++;
                                                                    _cryptoListController.jumpTo(0);
                                                                  });
                                                                }
                                                              : null,
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                }

                                                // --- ряд тикеров ---
                                                List<Widget> rowItems = [];
                                                for (int i = 0; i < _columnsCount; i++) {
                                                  final itemIndex = (index - 1) * _columnsCount + i;
                                                  if (itemIndex < pageCryptos.length) {
                                                    final globalRank = pageStart + itemIndex + 1;
                                                    rowItems.add(
                                                      Expanded(
                                                        child: _buildCryptoListItem(pageCryptos[itemIndex], globalRank, cryptoProvider),
                                                      ),
                                                    );
                                                    if (i < _columnsCount - 1) {
                                                      rowItems.add(const SizedBox(width: 8.0));
                                                    }
                                                  } else {
                                                    rowItems.add(const Expanded(child: SizedBox.shrink()));
                                                    if (i < _columnsCount - 1) {
                                                      rowItems.add(const SizedBox(width: 8.0));
                                                    }
                                                  }
                                                }

                                                return Padding(
                                                  padding: const EdgeInsets.only(bottom: 8.0),
                                                  child: Row(children: rowItems),
                                                );
                                              },
                                            ),
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Sidebar — 1/3 ширины, цвет зависит от sentimentColor
                    // УДАЛЕНО: Сайдбар с новостями и индексами
                    // Вместо этого ничего не добавляем, основной контент теперь занимает всю ширину
                  ],
                ),
              ),
            ],
          ),
          bottomNavigationBar: AppBottomNavigation(
            currentIndex: 1,
            onTap: (index) {
              if (index != 1) {
                switch (index) {
                  case 0:
                    Navigator.pushReplacementNamed(context, '/news');
                    break;
                  case 2:
                    Navigator.pushReplacementNamed(context, '/sinusoid');
                    break;
                  case 3:
                    Navigator.pushReplacementNamed(context, '/courses');
                    break;
                  case 4:
                    Navigator.pushReplacementNamed(context, '/profile');
                    break;
                }
              }
            },
          ),
        );
      },
    );
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  // Форматирование рыночной капитализации
  String _formatMarketCap(double marketCap) {
    if (marketCap >= 1000000000000) {
      return '\$${(marketCap / 1000000000000).toStringAsFixed(1)}T';
    } else if (marketCap >= 1000000000) {
      return '\$${(marketCap / 1000000000).toStringAsFixed(1)}B';
    } else if (marketCap >= 1000000) {
      return '\$${(marketCap / 1000000).toStringAsFixed(1)}M';
    } else {
      return '\$${marketCap.toStringAsFixed(0)}';
    }
  }

  // Получение форматированного времени последнего обновления (только часы и минуты)
  String _getFormattedDateTime() {
    return '${_lastUpdateTime.hour.toString().padLeft(2, '0')}:${_lastUpdateTime.minute.toString().padLeft(2, '0')}';
  }

  Widget _buildCryptoListItem(CryptoCurrency crypto, int rank, CryptoProvider cryptoProvider) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeSign = isPositive ? '+' : '';
    final changeColor = isPositive ? const Color(0xFF00FF88) : const Color(0xFFFF4444);

    return InkWell(
      onTap: () async {
        print('🔥 КЛИК ПО РЕЗУЛЬТАТУ ПОИСКА: ${crypto.name} (${crypto.symbol})');
        
        // Защита от множественных кликов
        if (_isNavigating) {
          print('⚠️ Навигация уже выполняется, игнорируем клик');
          return;
        }
        
        // Проверяем, что context всё ещё валиден
        if (!mounted) {
          print('❌ Widget не монтирован, отменяем навигацию');
          return;
        }
        
        // Устанавливаем флаг навигации
        _isNavigating = true;
        
        print('🚀 Выполняем навигацию...');
        
        try {
          // Сохраняем context перед асинхронными операциями
          final contextToUse = context;
          
          // Выполняем навигацию
          final result = await Navigator.of(contextToUse).push(
            CustomPageTransitions.buildCoinDetailTransition(
              page: const CoinDetailScreen(),
              settings: RouteSettings(arguments: crypto),
            ),
          );
          
          print('✅ Навигация завершена успешно: $result');
          
        } catch (error) {
          print('❌ ОШИБКА НАВИГАЦИИ: $error');
          
          // Показываем уведомление об ошибке только если widget ещё монтирован
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Не удалось открыть ${crypto.name}: ${error.toString()}'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
                action: SnackBarAction(
                  label: 'Повторить',
                  textColor: Colors.white,
                  onPressed: () {
                    // Повторяем попытку навигации
                    if (!_isNavigating && mounted) {
                      Navigator.of(context).push(
                        CustomPageTransitions.buildCoinDetailTransition(
                          page: const CoinDetailScreen(),
                          settings: RouteSettings(arguments: crypto),
                        ),
                      );
                    }
                  },
                ),
              ),
            );
          }
        } finally {
          // Сбрасываем флаг навигации и очищаем состояние поиска
          if (mounted) {
            _isNavigating = false;
            _cleanupSearchState();
          }
        }
      },
      borderRadius: BorderRadius.circular(8),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(14),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            margin: const EdgeInsets.only(bottom: 6),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.18),
                  Colors.white.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(14),
              border: Border.all(color: Colors.white.withOpacity(0.25), width: 1.2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.35),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
                BoxShadow(
                  color: changeColor.withOpacity(0.25),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Row(
              children: [
                // Ранг
                Container(
                  width: 40,
                  alignment: Alignment.center,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: const Color(0xFF546E7A),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      '$rank',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // Иконка
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey.shade800,
                    boxShadow: [
                      BoxShadow(
                        color: changeColor.withOpacity(0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(15),
                    child: CachedNetworkImage(
                      imageUrl: crypto.imageUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey.shade800,
                        child: const Center(
                          child: SizedBox(
                            width: 14,
                            height: 14,
                            child: CircularProgressIndicator(strokeWidth: 1),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) {
                        return Container(
                          color: Colors.grey.shade800,
                          child: Center(
                            child: Text(
                              crypto.symbol.substring(0, 1),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Название и символ
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        crypto.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        crypto.symbol.toUpperCase(),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Цена и изменение
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Hero(
                      tag: 'crypto_price_${crypto.symbol}', // Hero для цены
                      child: Material(
                        color: Colors.transparent,
                        child: Text(
                          '\$${_formatPrice(crypto.price)}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Hero(
                      tag: 'crypto_change_${crypto.symbol}', // Hero для процента изменения
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                        decoration: BoxDecoration(
                          color: changeColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: changeColor.withOpacity(0.4),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          '${isPositive ? '+' : ''}${crypto.priceChangePercentage24h.toStringAsFixed(1)}%',
                          style: TextStyle(
                            color: changeColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCryptoItem(CryptoCurrency crypto, CryptoProvider cryptoProvider) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeSign = isPositive ? '+' : '';
    final changeColor = isPositive ? Color(0xFF00FF00) : Color(0xFFFF0000);

    return InkWell(
      onTap: () {
        Navigator.of(context).push(
          CustomPageTransitions.buildCoinDetailTransition(
            page: const CoinDetailScreen(),
            settings: RouteSettings(arguments: crypto),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 3.0),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1D23).withOpacity(0.5),
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: Colors.black.withOpacity(0.5), width: 1),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Верхняя часть с иконкой и названием
            Row(
              children: [
                // Coin icon
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey.shade800,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      crypto.imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey.shade800,
                          child: Center(
                            child: Text(
                              crypto.symbol.substring(0, crypto.symbol.length > 2 ? 2 : crypto.symbol.length),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 6),

                // Название и символ
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        crypto.name,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          shadows: [Shadow(color: Colors.black54, blurRadius: 2, offset: Offset(1,1))],
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      Row(
                        children: [
                          Text(
                            crypto.symbol,
                            style: TextStyle(
                              color: Colors.grey.shade400,
                              fontSize: 10,
                            ),
                          ),
                          const SizedBox(width: 4),
                          // Индикатор рыночной капитализации
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '${_formatMarketCap(crypto.marketCap)}',
                              style: TextStyle(
                                color: Colors.grey.shade400,
                                fontSize: 9,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Favorite button
                GestureDetector(
                  onTap: () {
                    cryptoProvider.toggleFavorite(crypto.id);
                  },
                  child: Icon(
                    crypto.isFavorite ? Icons.star : Icons.star_border,
                    color: crypto.isFavorite ? Colors.amber : Colors.grey.shade600,
                    size: 16,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 4),

            // Нижняя часть с ценой и изменением
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Цена
                Text(
                  '\$${_formatPrice(crypto.price)}',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    shadows: [Shadow(color: Colors.black54, blurRadius: 2, offset: Offset(1,1))],
                  ),
                ),

                // Изменение цены
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: isPositive ? Colors.green.withOpacity(0.8) : Colors.red.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isPositive ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                        color: Colors.white,
                        size: 14,
                      ),
                      Text(
                        '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(1)}%',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 11,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // --- Tech-grid animated background ---
  Widget _buildAnimatedMintlifyBackground(Color _ignored) {
    // two base color pairs for breathing effect
    const Color c1Start = Color(0xFF1F222A);
    const Color c1End   = Color(0xFF2A2E38);
    const Color c2Start = Color(0xFF13151B);
    const Color c2End   = Color(0xFF1B1E25);

    return AnimatedBuilder(
      animation: _bgController,
      builder: (context, child) {
        final t = (math.sin(_bgController.value * 2 * math.pi) + 1) / 2; // 0..1
        final color1 = Color.lerp(c1Start, c1End, t)!;
        final color2 = Color.lerp(c2Start, c2End, t)!;

        return Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [color1, color2],
                ),
              ),
            ),
            CustomPaint(size: Size.infinite, painter: _GridPainter()),
            Positioned.fill(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                child: Container(color: Colors.transparent),
              ),
            ),
          ],
        );
      },
    );
  }

  // Премиальные кнопки фильтров
  Widget _buildPremiumFilterChip(String label, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = label;
          _updateDisplayedCryptos();
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeInOut,
        margin: const EdgeInsets.only(right: 12.0),
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF1A1A2E), // Темно-синий
                    const Color(0xFF16213E), // Еще темнее
                  ],
                )
              : LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.08),
                    Colors.white.withOpacity(0.04),
                  ],
                ),
          borderRadius: BorderRadius.circular(25.0),
          border: Border.all(
            color: isSelected 
                ? const Color(0xFF4A90E2) // Менее яркий синий цвет вместо циана
                : Colors.white.withOpacity(0.15),
            width: isSelected ? 2.0 : 1.0, // Немного уменьшил толщину
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: const Color(0xFF4A90E2).withOpacity(0.3), // Обновленный цвет для тени
                    blurRadius: 15,
                    offset: const Offset(0, 6),
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color: const Color(0xFF1A1A2E).withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 1,
                    offset: const Offset(0, 1),
                  ),
                ],
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.white.withOpacity(0.9),
            fontWeight: isSelected ? FontWeight.w700 : FontWeight.w600,
            fontSize: 15.0,
            letterSpacing: 0.4,
            shadows: isSelected 
                ? [
                    const Shadow(
                      color: Colors.black26,
                      blurRadius: 4,
                      offset: Offset(0, 1),
                    ),
                  ]
                : null,
          ),
        ),
      ),
    );
  }

  // Обработчик изменения текста поиска
  void _onSearchChanged() {
    // Отменяем предыдущий таймер
    _searchDebounceTimer?.cancel();
    
    final query = _searchController.text.trim();
    print('Поиск: "$query"');
    
    // Если запрос пустой, сразу очищаем
    if (query.isEmpty) {
      setState(() {
        _isSearching = false;
        _searchResults.clear();
      });
      _removeOverlay();
      return;
    }

    // Устанавливаем debounce на 300ms
    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      _performSearch(query);
    });
  }

  // Выполнение поиска
  void _performSearch(String query) {
    if (!mounted) return;
    
    try {
      final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
      final allCryptos = cryptoProvider.allCryptos;
      
      print('Всего криптовалют: ${allCryptos.length}');

      if (allCryptos.isEmpty) {
        print('Данные еще не загружены');
        setState(() {
          _isSearching = false;
          _searchResults.clear();
        });
        _removeOverlay();
        return;
      }

      final lowerQuery = query.toLowerCase();
      
      // Поиск по имени и символу
      final results = allCryptos.where((crypto) {
        final name = crypto.name.toLowerCase();
        final symbol = crypto.symbol.toLowerCase();
        return name.contains(lowerQuery) || symbol.contains(lowerQuery) || 
               name.startsWith(lowerQuery) || symbol.startsWith(lowerQuery);
      }).take(10).toList();

      print('Найдено: ${results.length} результатов');

      if (mounted) {
        setState(() {
          _isSearching = query.isNotEmpty;
          _searchResults = results;
        });

        // Не показываем overlay - используем встроенный список
        // if (results.isNotEmpty && _searchFocusNode.hasFocus) {
        //   _showSearchOverlay();
        // } else {
        //   _removeOverlay();
        // }
      }
    } catch (e) {
      print('Ошибка поиска: $e');
      if (mounted) {
        setState(() {
          _isSearching = false;
          _searchResults.clear();
        });
        _removeOverlay();
      }
    }
  }

  // Обработчик изменения фокуса поля поиска
  void _onSearchFocusChanged() {
    // Не показываем overlay - используем встроенный список
    // if (_searchFocusNode.hasFocus && _searchController.text.isNotEmpty) {
    //   _showSearchOverlay();
    // } else {
    //   _removeOverlay();
    // }
  }

  // Показ overlay с результатами поиска
  void _showSearchOverlay() {
    if (!mounted || _searchResults.isEmpty) {
      print('🚫 Не показываем overlay: mounted=$mounted, results=${_searchResults.length}');
      return;
    }
    
    print('🎭 Показываем overlay с ${_searchResults.length} результатами');
    
    _removeOverlay(); // Убираем предыдущий overlay

    // Получаем позиционирование
    final mediaQuery = MediaQuery.of(context);
    final topPadding = mediaQuery.padding.top;
    
    _overlayEntry = OverlayEntry(
      builder: (context) {
        print('🏗️ Строим overlay entry');
        return Positioned(
          top: topPadding + 140, // Позиционируем под поисковым полем
          left: 16,
          right: 16,
          child: Material(
            elevation: 12,
            color: Colors.transparent,
            child: Container(
              constraints: const BoxConstraints(maxHeight: 350),
              decoration: BoxDecoration(
                color: const Color(0xFF1A1A1A),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withOpacity(0.25), 
                  width: 1.5
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.8),
                    blurRadius: 25,
                    offset: const Offset(0, 12),
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Заголовок
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.08),
                        border: Border(
                          bottom: BorderSide(
                            color: Colors.white.withOpacity(0.15),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.search,
                            color: Colors.white.withOpacity(0.8),
                            size: 18,
                          ),
                          const SizedBox(width: 10),
                          Text(
                            'Результаты поиска • ${_searchResults.length}',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.9),
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Нажмите для перехода',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.6),
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: () {
                                print('❌ Закрытие overlay через крестик');
                                _cleanupSearchState();
                              },
                              borderRadius: BorderRadius.circular(6),
                              child: Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Icon(
                                  Icons.close,
                                  color: Colors.white.withOpacity(0.7),
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Список результатов с явным указанием на интерактивность
                    Flexible(
                      child: ListView.builder(
                        shrinkWrap: true,
                        padding: const EdgeInsets.all(12),
                        itemCount: _searchResults.length,
                        physics: const ClampingScrollPhysics(),
                        itemBuilder: (context, index) {
                          final crypto = _searchResults[index];
                          print('🔨 Создаем элемент ${index + 1}/${_searchResults.length}: ${crypto.name}');
                          return _buildSearchResultItem(crypto);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );

    if (mounted) {
      Overlay.of(context).insert(_overlayEntry!);
      print('✅ Overlay добавлен в дерево виджетов');
    }
  }

  // Создание элемента результата поиска
  Widget _buildSearchResultItem(CryptoCurrency crypto) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeColor = isPositive ? const Color(0xFF00FF88) : const Color(0xFFFF4444);

    print('🏗️ Создаю элемент поиска для: ${crypto.name}');

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () async {
          print('🎯 ПОЛЬЗОВАТЕЛЬ КЛИКНУЛ ПО РЕЗУЛЬТАТУ: ${crypto.name} (${crypto.symbol})');
          print('📍 mounted: $mounted, _isNavigating: $_isNavigating');
          
          // Защита от множественных кликов
          if (_isNavigating) {
            print('⚠️ Навигация уже выполняется, игнорируем клик');
            return;
          }
          
          // Проверяем, что context всё ещё валиден
          if (!mounted) {
            print('❌ Widget не монтирован, отменяем навигацию');
            return;
          }
          
          // Устанавливаем флаг навигации
          _isNavigating = true;
          print('🔒 Установлен флаг навигации');
          
          // Немедленно очищаем overlay чтобы он не мешал навигации
          _removeOverlay();
          _searchFocusNode.unfocus();
          print('🧹 Overlay удален и фокус снят');
          
          print('🚀 Начинаем навигацию...');
          
          try {
            print('📱 Используем кастомную анимацию навигации...');
            
            final result = await Navigator.of(context).push(
              CustomPageTransitions.buildCoinDetailTransition(
                page: const CoinDetailScreen(),
                settings: RouteSettings(arguments: crypto),
              ),
            );
            
            print('✅ Навигация завершена успешно. Результат: $result');
            
          } catch (error, stackTrace) {
            print('❌ КРИТИЧЕСКАЯ ОШИБКА НАВИГАЦИИ: $error');
            print('📚 Stack trace: $stackTrace');
            
            // Показываем уведомление об ошибке только если widget ещё монтирован
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Не удалось открыть ${crypto.name}: ${error.toString()}'),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          } finally {
            // Сбрасываем флаг навигации и очищаем состояние поиска
            print('🔓 Сбрасываем флаг навигации и очищаем состояние');
            if (mounted) {
              _isNavigating = false;
              setState(() {
                _searchController.clear();
                _isSearching = false;
                _searchResults.clear();
              });
            }
          }
        },
        borderRadius: BorderRadius.circular(8),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(14),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              margin: const EdgeInsets.only(bottom: 6),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.18),
                    Colors.white.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(14),
                border: Border.all(color: Colors.white.withOpacity(0.25), width: 1.2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.35),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                  BoxShadow(
                    color: changeColor.withOpacity(0.25),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Иконка
                  Hero(
                    tag: 'crypto_icon_${crypto.symbol}', // Уникальный тег для Hero анимации
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey.shade800,
                        boxShadow: [
                          BoxShadow(
                            color: changeColor.withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: CachedNetworkImage(
                          imageUrl: crypto.imageUrl,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: Colors.grey.shade800,
                            child: const Center(
                              child: SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              ),
                            ),
                          ),
                          errorWidget: (context, url, error) {
                            return Container(
                              color: Colors.grey.shade800,
                              child: Center(
                                child: Text(
                                  crypto.symbol.substring(0, 1),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // Название и символ
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          crypto.name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          crypto.symbol.toUpperCase(),
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Цена и изменение
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Hero(
                        tag: 'crypto_price_${crypto.symbol}', // Hero для цены
                        child: Material(
                          color: Colors.transparent,
                          child: Text(
                            '\$${_formatPrice(crypto.price)}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Hero(
                        tag: 'crypto_change_${crypto.symbol}', // Hero для процента изменения
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                          decoration: BoxDecoration(
                            color: changeColor.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              color: changeColor.withOpacity(0.4),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            '${isPositive ? '+' : ''}${crypto.priceChangePercentage24h.toStringAsFixed(1)}%',
                            style: TextStyle(
                              color: changeColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // Индикатор кликабельности
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white.withOpacity(0.5),
                    size: 12,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Удаление overlay
  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  // Очистка состояния поиска
  void _cleanupSearchState() {
    setState(() {
      _searchController.clear();
      _isSearching = false;
      _searchResults.clear();
    });
    _searchFocusNode.unfocus();
  }

  // Создание списка результатов поиска (встроенного в основной UI)
  Widget _buildSearchResultsList() {
    print('🔍 Создаем встроенный список результатов поиска: ${_searchResults.length} элементов');
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.25), 
          width: 1.5
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Заголовок
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.08),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              border: Border(
                bottom: BorderSide(
                  color: Colors.white.withOpacity(0.15),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.search,
                  color: Colors.white.withOpacity(0.8),
                  size: 18,
                ),
                const SizedBox(width: 10),
                Text(
                  'Результаты поиска • ${_searchResults.length}',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      print('❌ Закрытие результатов поиска');
                      setState(() {
                        _searchController.clear();
                        _isSearching = false;
                        _searchResults.clear();
                      });
                      _searchFocusNode.unfocus();
                    },
                    borderRadius: BorderRadius.circular(6),
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        Icons.close,
                        color: Colors.white.withOpacity(0.7),
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Список результатов
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(12),
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final crypto = _searchResults[index];
                print('🔨 Создаем встроенный элемент ${index + 1}/${_searchResults.length}: ${crypto.name}');
                return _buildSimpleSearchResultItem(crypto);
              },
            ),
          ),
        ],
      ),
    );
  }

  // Простой элемент результата поиска (для встроенного списка)
  Widget _buildSimpleSearchResultItem(CryptoCurrency crypto) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeColor = isPositive ? const Color(0xFF00FF88) : const Color(0xFFFF4444);

    print('🏗️ Создаю простой элемент поиска для: ${crypto.name}');

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
            print('🎯 КЛИК ПО ВСТРОЕННОМУ РЕЗУЛЬТАТУ: ${crypto.name} (${crypto.symbol})');
            print('📍 mounted: $mounted, _isNavigating: $_isNavigating');
            
            // Защита от множественных кликов
            if (_isNavigating) {
              print('⚠️ Навигация уже выполняется, игнорируем клик');
              return;
            }
            
            // Проверяем, что context всё ещё валиден
            if (!mounted) {
              print('❌ Widget не монтирован, отменяем навигацию');
              return;
            }
            
            // Устанавливаем флаг навигации
            _isNavigating = true;
            print('🔒 Установлен флаг навигации');
            
            // Очищаем поиск сразу
            setState(() {
              _searchController.clear();
              _isSearching = false;
              _searchResults.clear();
            });
            _searchFocusNode.unfocus();
            print('🧹 Поиск очищен');
            
            print('🚀 Начинаем навигацию...');
            
            try {
              print('📱 Вызываем Navigator.pushNamed...');
              
              final result = await Navigator.of(context).push(
                CustomPageTransitions.buildCoinDetailTransition(
                  page: const CoinDetailScreen(),
                  settings: RouteSettings(arguments: crypto),
                ),
              );
              
              print('✅ Навигация завершена успешно. Результат: $result');
              
            } catch (error, stackTrace) {
              print('❌ КРИТИЧЕСКАЯ ОШИБКА НАВИГАЦИИ: $error');
              print('📚 Stack trace: $stackTrace');
              
              // Показываем уведомление об ошибке только если widget ещё монтирован
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Не удалось открыть ${crypto.name}: ${error.toString()}'),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            } finally {
              // Сбрасываем флаг навигации
              print('🔓 Сбрасываем флаг навигации');
              if (mounted) {
                _isNavigating = false;
              }
            }
          },
          borderRadius: BorderRadius.circular(8),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(14),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                margin: const EdgeInsets.only(bottom: 6),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(0.18),
                      Colors.white.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(14),
                  border: Border.all(color: Colors.white.withOpacity(0.25), width: 1.2),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.35),
                      blurRadius: 12,
                      offset: const Offset(0, 6),
                    ),
                    BoxShadow(
                      color: changeColor.withOpacity(0.25),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Иконка
                    Hero(
                      tag: 'crypto_icon_${crypto.symbol}', // Уникальный тег для Hero анимации
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.grey.shade800,
                          boxShadow: [
                            BoxShadow(
                              color: changeColor.withOpacity(0.3),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(16),
                          child: CachedNetworkImage(
                            imageUrl: crypto.imageUrl,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: Colors.grey.shade800,
                              child: const Center(
                                child: SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                              ),
                            ),
                            errorWidget: (context, url, error) {
                              return Container(
                                color: Colors.grey.shade800,
                                child: Center(
                                  child: Text(
                                    crypto.symbol.substring(0, 1),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    
                    // Название и символ
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            crypto.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 15,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 3),
                          Text(
                            crypto.symbol.toUpperCase(),
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.7),
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Цена и изменение
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Hero(
                          tag: 'crypto_price_${crypto.symbol}', // Hero для цены
                          child: Material(
                            color: Colors.transparent,
                            child: Text(
                              '\$${_formatPrice(crypto.price)}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Hero(
                          tag: 'crypto_change_${crypto.symbol}', // Hero для процента изменения
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                            decoration: BoxDecoration(
                              color: changeColor.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: changeColor.withOpacity(0.4),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              '${isPositive ? '+' : ''}${crypto.priceChangePercentage24h.toStringAsFixed(1)}%',
                              style: TextStyle(
                                color: changeColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(width: 12),
                    
                    // Индикатор кликабельности
                    Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.white.withOpacity(0.6),
                      size: 14,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // --- SIDEBAR PLACEHOLDERS ---
  // УДАЛЕНО: Функции _buildNewsPlaceholder и _buildIndicesPlaceholder
  // Теперь новости и индексы доступны через отдельные кнопки в верхней панели
}

// ------- Tech-grid painter (PCB style) -------
class _GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF50545C).withOpacity(0.12)
      ..strokeWidth = 1;

    const double gap = 40;
    for (double x = 0; x <= size.width; x += gap) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
    for (double y = 0; y <= size.height; y += gap) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

