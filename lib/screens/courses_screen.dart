import 'package:flutter/material.dart';
import '../models/course.dart';
import '../models/game.dart';
import '../widgets/course_card.dart';
import '../widgets/app_bottom_navigation.dart';
import 'dart:ui';

class CoursesScreen extends StatefulWidget {
  final int initialTabIndex;
  const CoursesScreen({super.key, this.initialTabIndex = 0});

  @override
  State<CoursesScreen> createState() => _CoursesScreenState();
}

class _CoursesScreenState extends State<CoursesScreen> with SingleTickerProviderStateMixin {
  final List<Course> _courses = Course.getMockCourses();
  final List<Game> _games = Game.getMockGames();
  late int _selectedTabIndex;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Цвета для табов
  final Color coursesColor = const Color(0xFF1976D2); // Синий
  final Color materialsColor = const Color(0xFFF57C00); // Оранжевый
  final Color gamesColor = const Color(0xFF388E3C); // Зелёный

  Color _getGlassColor(int selectedTab) {
    if (selectedTab == 0) {
      return coursesColor.withOpacity(0.25).withBlue((coursesColor.blue * 0.85).toInt());
    } else if (selectedTab == 1) {
      return materialsColor.withOpacity(0.25).withRed((materialsColor.red * 0.85).toInt());
    } else if (selectedTab == 2) {
      return gamesColor.withOpacity(0.25).withGreen((gamesColor.green * 0.85).toInt());
    }
    return coursesColor.withOpacity(0.25).withBlue((coursesColor.blue * 0.85).toInt());
  }

  @override
  void initState() {
    super.initState();
    _selectedTabIndex = widget.initialTabIndex;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTabChanged(int index) {
    if (index != _selectedTabIndex) {
      setState(() {
        _selectedTabIndex = index;
      });
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0B0D), // Dark background like sinusoid
      extendBody: true, // Allow body to extend behind navigation bar
      body: Stack(
        children: [
          // Фоновый градиент как в Profile
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFF000000), Color(0xFF121212)],
                ),
              ),
            ),
          ),
          // Контент
          SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Кастомная верхняя панель навигации (glassmorphism)
                Padding(
                  padding: const EdgeInsets.only(top: 35),
                  child: Center(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(36),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 32, sigmaY: 32),
                        child: Container(
                          constraints: const BoxConstraints(maxWidth: 1400),
                          height: 125,
                          decoration: BoxDecoration(
                            color: _getGlassColor(_selectedTabIndex),
                            borderRadius: BorderRadius.circular(36),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.28),
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.15),
                                blurRadius: 32,
                                offset: const Offset(0, 8),
                              ),
                              BoxShadow(
                                color: Colors.white.withOpacity(0.08),
                                blurRadius: 16,
                                offset: const Offset(0, -4),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              _buildPngTabItem(
                                0,
                                _selectedTabIndex == 0
                                    ? 'logo/Learn/Clicked/Courses_clicked.png'
                                    : 'logo/Learn/Unclicked/Courses_unclicked.png',
                                'Courses',
                                iconSize: 56,
                              ),
                              _buildPngTabItem(
                                1,
                                _selectedTabIndex == 1
                                    ? 'logo/Learn/Clicked/Materials_clicked.png'
                                    : 'logo/Learn/Unclicked/Materials_unclicked.png',
                                'Materials',
                                iconSize: 56,
                              ),
                              _buildPngTabItem(
                                2,
                                _selectedTabIndex == 2
                                    ? 'logo/Learn/Clicked/Games_clicked.png'
                                    : 'logo/Learn/Unclicked/Games_unclicked.png',
                                'Games',
                                iconSize: 56,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                // Контент по выбранному табу
                Expanded(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        child: Center(
                          child: Container(
                            constraints: const BoxConstraints(maxWidth: 1400),
                            child: _selectedTabIndex == 0
                                ? _buildCoursesTabProfileStyle()
                                : _selectedTabIndex == 1
                                    ? _buildMaterialsTabProfileStyle()
                                    : _buildGamesTab(),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 3,
        onTap: (index) {
          if (index != 3) {
            switch (index) {
              case 0:
                Navigator.pushReplacementNamed(context, '/news');
                break;
              case 1:
                Navigator.pushReplacementNamed(context, '/charts');
                break;
              case 2:
                Navigator.pushReplacementNamed(context, '/sinusoid');
                break;
              case 4:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }

  // Кастомный таб с PNG-иконкой
  Widget _buildPngTabItem(int index, String assetPath, String label, {double iconSize = 56}) {
    final isSelected = _selectedTabIndex == index;
    return GestureDetector(
      onTap: () => _onTabChanged(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: iconSize + 8,
              height: iconSize + 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.25),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : [],
              ),
              child: Image.asset(
                assetPath,
                width: iconSize,
                height: iconSize,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.broken_image,
                    size: iconSize,
                    color: Colors.grey.shade400,
                  );
                },
              ),
            ),
            const SizedBox(height: 8),
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                shadows: isSelected
                    ? [
                        Shadow(
                          color: Colors.black.withOpacity(0.4),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : [],
              ),
              child: Text(label),
            ),
          ],
        ),
      ),
    );
  }

  // Курсы в стиле Profile
  Widget _buildCoursesTabProfileStyle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Courses',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        ...List.generate(_courses.length, (index) {
          final course = _courses[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: GestureDetector(
              onTap: () {
                Navigator.pushNamed(
                  context,
                  '/course_detail',
                  arguments: course,
                );
              },
              child: Container(
                height: 80,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: const Color(0xFF1C1C1E),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.book,
                        color: Colors.blue,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          course.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          course.subtitle,
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 12,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.chevron_right,
                        color: Colors.blue,
                        size: 18,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
        }),
      ],
    );
  }

  // Материалы в стиле Profile
  Widget _buildMaterialsTabProfileStyle() {
    final List<Map<String, String>> materials = [
      {'title': 'Understanding Cryptocurrency Market Cycles', 'description': 'Learn about the different market cycles in cryptocurrency and how to identify them.'},
      {'title': 'Technical Analysis Fundamentals', 'description': 'Master the basics of technical analysis for cryptocurrency trading.'},
      {'title': 'DeFi Yield Farming Strategies', 'description': 'Explore different yield farming strategies in decentralized finance.'},
      {'title': 'NFT Creation and Monetization', 'description': 'Learn how to create, mint, and monetize your own NFTs.'},
    ];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Materials',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        ...List.generate(materials.length, (index) {
          final material = materials[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: GestureDetector(
              onTap: () {},
              child: Container(
                height: 80,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: const Color(0xFF1C1C1E),
                  borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.article,
                        color: Colors.orange,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          material['title']!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          material['description']!,
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 12,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.chevron_right,
                        color: Colors.orange,
                        size: 18,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
        }),
      ],
    );
  }

  // Игры в стиле Profile (контейнеры игр не меняем, только общий паддинг и фон)
  Widget _buildGamesTab() {
    // Filter simulators and quiz games
    final simulators = _games.where((game) => game.type == GameType.simulator).toList();
    final quizGames = _games.where((game) => game.type == GameType.quiz).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Simulators',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        ...simulators.map((game) => _buildGameCard(game)).toList(),
        
        if (quizGames.isNotEmpty) ...[
          const SizedBox(height: 32),
          const Text(
            'Quiz',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          ...quizGames.map((game) => _buildGameCard(game)).toList(),
        ],
      ],
    );
  }

  Widget _buildGameCard(Game game) {
    // Определяем цвет в зависимости от игры
    Color cardColor;

    if (game.id == '1') { // Crypto Trading Simulator
      cardColor = const Color(0xFF1E2E1E); // Темно-зеленый
    } else if (game.id == '2') { // Anti FOMO Simulator
      cardColor = const Color(0xFF2E1E1E); // Темно-красный
    } else if (game.id == '5') { // Crypto Quiz
      cardColor = const Color(0xFF1E1E2E); // Темно-синий
    } else {
      cardColor = const Color(0xFF1E2E2E); // Темно-серый по умолчанию
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      height: 80,
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Row(
        children: [
          // Иконка игры
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildGameIcon(game),
          ),

          // Информация об игре
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  game.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  game.description,
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 12,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // Кнопка Play
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton(
              onPressed: () {
                // Launch game
                if (game.id == '1') {
                  Navigator.pushNamed(context, '/crypto_simulator_mode_selection');
                } else if (game.id == '2') {
                  Navigator.pushNamed(context, '/anti_fomo_simulator');
                } else if (game.id == '5') {
                  Navigator.pushNamed(context, '/crypto_quiz');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4A4A5A), // Серо-фиолетовый
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0),
                ),
                minimumSize: const Size(70, 36),
              ),
              child: const Text(
                'Play',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameIcon(Game game) {
    if (game.id == '1') {
      return Image.asset(
        'logo/Learn/Games/Crypto_Trading_Simulator.png',
        width: 48,
        height: 48,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: const Color(0xFF00FF00),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: const Center(
              child: Text(
                'BTC',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          );
        },
      );
    } else if (game.id == '2') {
      return Image.asset(
        'logo/Learn/Games/Anti_FOMO.png',
        width: 48,
        height: 48,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: const Color(0xFFFF0000),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: const Center(
              child: Text(
                'FOMO',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          );
        },
      );
    } else if (game.id == '5') {
      // Crypto Quiz icon
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF007AFF), Color(0xFF5856D6)],
          ),
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: const Center(
          child: Icon(
            Icons.quiz,
            color: Colors.white,
            size: 24,
          ),
        ),
      );
    } else {
      // Default icon for other games
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.grey[600],
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: const Center(
          child: Icon(
            Icons.gamepad,
            color: Colors.white,
            size: 24,
          ),
        ),
      );
    }
  }
}
