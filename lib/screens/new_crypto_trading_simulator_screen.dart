import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/trading_simulator_controller.dart';
import '../widgets/platform_improved_tradingview_chart.dart';
import '../providers/trading_simulator_provider.dart';

/// Новый экран симулятора торговли криптовалютой
class NewCryptoTradingSimulatorScreen extends StatelessWidget {
  const NewCryptoTradingSimulatorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<TradingSimulatorController>(
      create: (context) => TradingSimulatorController(),
      child: Scaffold(
        backgroundColor: const Color(0xFF131722),
        appBar: AppBar(
          backgroundColor: const Color(0xFF131722),
          title: const Text('Crypto Trading Simulator'),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                Provider.of<TradingSimulatorController>(context, listen: false).resetGame();
              },
            ),
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () {
                _showSettingsDialog(context);
              },
            ),
          ],
        ),
        body: Consumer<TradingSimulatorController>(
          builder: (context, controller, child) {
            if (controller.allCandles.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Loading candles...',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                Container(
                  height: 36,
                  color: const Color(0xFF1A2029),
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: controller.availableSymbols.length,
                    itemBuilder: (context, index) {
                      final symbol = controller.availableSymbols[index];
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 4.0),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(18),
                          onTap: () {
                            controller.selectSymbol(symbol);
                          },
                          child: Chip(
                            label: Text(symbol, style: TextStyle(fontSize: 13)),
                            backgroundColor: controller.currentSymbol == symbol
                                ? Colors.blueGrey
                                : const Color(0xFF2D333C),
                            labelStyle: TextStyle(
                              color: controller.currentSymbol == symbol
                                  ? Colors.white
                                  : Colors.white70,
                              fontWeight: controller.currentSymbol == symbol
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                            padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 0.0),
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ),
                        ),
                      );
                    },
                  ),
                ),

                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                           Text(
                            '${controller.currentSymbol}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          ),
                          Text(
                            'Balance: \$${controller.balance.toStringAsFixed(2)}',
                            style: TextStyle(
                              color: controller.balance >= 1000.0
                                  ? Colors.green
                                  : Colors.red,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          ...controller.availableTimeframes.map((tf) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 4.0),
                              child: ElevatedButton(
                                onPressed: () {
                                  controller.selectTimeframe(tf);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: controller.currentTimeframe == tf
                                      ? Colors.blueGrey
                                      : const Color(0xFF2D333C),
                                  padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                                  minimumSize: Size(50, 35),
                                ),
                                child: Text(
                                  tf.toUpperCase(),
                                  style: TextStyle(
                                    color: controller.currentTimeframe == tf
                                        ? Colors.white
                                        : Colors.white70,
                                    fontWeight: controller.currentTimeframe == tf
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ],
                      ),
                    ],
                  ),
                ),

                Expanded(
                  child: PlatformImprovedTradingViewChart(
                    key: GlobalKey(),
                    allCandles: controller.getAllVisibleCandles(),
                    onEntryPointSet: (price, time) {
                      controller.setEntryPoint(price, time);
                    },
                    onTradeResult: (isUp, percentChange, finalPrice) {
                      controller.handleTradeResult(isUp, percentChange, finalPrice);
                    },
                  ),
                ),

                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildControlButtons(controller),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
  
  Widget _buildControlButtons(TradingSimulatorController controller) {
    if (controller.showingFutureCandles) {
      return Center(
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          ),
          onPressed: () {
            PlatformImprovedTradingViewChart.clearChartElements(GlobalKey());
            controller.goToNextLevel();
          },
          child: const Text('Next Pattern'),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          ),
          onPressed: controller.gameStatus == 'playing'
              ? () {
                  PlatformImprovedTradingViewChart.setEntryPoint(GlobalKey());
                  PlatformImprovedTradingViewChart.showAllCandles(GlobalKey());
                  controller.makeTrade('buy');
                }
              : null,
          child: const Text('UP'),
        ),

        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          ),
          onPressed: controller.gameStatus == 'playing'
              ? () {
                  PlatformImprovedTradingViewChart.setEntryPoint(GlobalKey());
                  PlatformImprovedTradingViewChart.showAllCandles(GlobalKey());
                  controller.makeTrade('sell');
                }
              : null,
          child: const Text('DOWN'),
        ),
      ],
    );
  }
  
  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Settings'),
          content: Consumer<TradingSimulatorController>(
            builder: (context, controller, child) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ListTile(
                    title: const Text('Game Mode'),
                    trailing: DropdownButton<String>(
                      value: controller.gameMode,
                      items: const [
                        DropdownMenuItem(
                          value: 'custom',
                          child: Text('Custom'),
                        ),
                        DropdownMenuItem(
                          value: 'infinite',
                          child: Text('Infinite Patterns'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          controller.setGameMode(value);
                        }
                      },
                    ),
                  ),

                  if (controller.gameMode == 'custom')
                    ListTile(
                      title: const Text('Symbol'),
                      trailing: DropdownButton<String>(
                        value: controller.currentSymbol,
                        items: controller.availableSymbols
                            .map((symbol) => DropdownMenuItem(
                                  value: symbol,
                                  child: Text(symbol),
                                ))
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            controller.selectSymbol(value);
                          }
                        },
                      ),
                    ),
                  
                  if (controller.gameMode == 'custom')
                    ListTile(
                      title: const Text('Timeframe'),
                      trailing: DropdownButton<String>(
                        value: controller.currentTimeframe,
                        items: controller.availableTimeframes
                            .map((tf) => DropdownMenuItem(
                                  value: tf,
                                  child: Text(tf.toUpperCase()),
                                ))
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            controller.selectTimeframe(value);
                          }
                        },
                      ),
                    ),
                  
                  ListTile(
                    title: const Text('Leverage'),
                    trailing: DropdownButton<int>(
                      value: controller.leverage.toInt(),
                      items: [1, 2, 3, 5, 10, 20, 50, 100]
                          .map((lev) => DropdownMenuItem(
                                value: lev,
                                child: Text('${lev}x'),
                              ))
                          .toList(),
                      onChanged: (value) {
                        if (value != null) {
                          controller.setLeverage(value.toDouble());
                        }
                      },
                    ),
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }
}
