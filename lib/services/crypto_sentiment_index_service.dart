import 'dart:convert';
import 'package:http/http.dart' as http;
import 'dart:math';

class CryptoSentimentIndexService {
  static const String _apiKey = 'acrgutqicdgpl07erft6ug6ygumbt98obtjz5c6mb';
  static const String _endpoint = 'https://lunarcrush.com/api3/coins/global?key=';

  static Future<double?> fetchCryptoSentimentIndex({int? fallbackFearGreed, double? fallbackAltcoinSeason}) async {
    try {
      final response = await http.get(Uri.parse('http://localhost:4000/crypto-sentiment'));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['sentiment'] != null) {
          print('CryptoSentimentIndex (proxy): ' + data['sentiment'].toString());
          return (data['sentiment'] as num).toDouble();
        }
      }
    } catch (e) {
      print('CryptoSentimentIndex proxy error: ' + e.toString());
    }
    // Если API не сработал — парсим с сайта alternative.me (пример)
    try {
      final htmlResponse = await http.get(Uri.parse('https://alternative.me/crypto/fear-and-greed-index/'));
      if (htmlResponse.statusCode == 200) {
        final html = htmlResponse.body;
        final regex = RegExp(r'"fngValue":\s*"(\d+)"');
        final match = regex.firstMatch(html);
        if (match != null) {
          final value = double.parse(match.group(1)!);
          print('CryptoSentimentIndex (parsed): ' + value.toString());
          return value;
        }
      }
    } catch (e) {
      print('CryptoSentimentIndex parse error: ' + e.toString());
    }
    // Fallback: если нет значения, вычисляем среднее между другими индексами
    if (fallbackFearGreed != null && fallbackAltcoinSeason != null) {
      final avg = ((fallbackFearGreed + fallbackAltcoinSeason) / 2).toDouble();
      print('CryptoSentimentIndex (fallback): ' + avg.toString());
      return avg;
    }
    // Если совсем ничего нет, возвращаем 50.0 (нейтрально)
    print('CryptoSentimentIndex (default): 50.0');
    return 50.0;
  }
} 