import 'dart:convert';
import 'dart:async';
import 'dart:math';

import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sentiment_history_model.dart';
import '../services/sentiment_prediction_service.dart';

// Custom HTTP client with headers that works across all platforms
class CustomHttpClient {
  static final client = http.Client();

  static Future<http.Response> get(Uri uri, {Map<String, String>? headers}) async {
    debugPrint('Making HTTP request to: $uri');

    try {
      // Merge default headers with custom headers if provided
      final Map<String, String> mergedHeaders = {
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
      };

      // Add custom headers if provided
      if (headers != null) {
        mergedHeaders.addAll(headers);
      }

      final response = await client.get(
        uri,
        headers: mergedHeaders,
      ).timeout(const Duration(seconds: 20)); // Increased timeout

      debugPrint('Response status code: ${response.statusCode}');
      if (response.statusCode == 200) {
        debugPrint('Response headers: ${response.headers}');
        // Only print first 200 chars of body to avoid flooding logs
        if (response.body.isNotEmpty) {
          debugPrint('Response body (truncated): ${response.body.substring(0, response.body.length > 200 ? 200 : response.body.length)}...');
        } else {
          debugPrint('Response body is empty');
        }
      } else {
        debugPrint('Error response: ${response.statusCode}');
        if (response.body.isNotEmpty) {
          debugPrint('Error body: ${response.body}');
        }
      }

      return response;
    } catch (e) {
      debugPrint('HTTP request error: $e');
      rethrow;
    }
  }
}

class MarketSentiment {
  final double score;
  final String sentiment;
  final Map<String, dynamic> details;

  MarketSentiment({
    required this.score,
    required this.sentiment,
    required this.details,
  });

  factory MarketSentiment.fromJson(Map<String, dynamic> json) {
    return MarketSentiment(
      score: json['score']?.toDouble() ?? 0.0,
      sentiment: json['sentiment'] ?? 'neutral',
      details: json['details'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'score': score,
      'sentiment': sentiment,
      'details': details,
    };
  }
}

class MarketSentimentService {
  // Base URLs for different APIs
  static const String _fearGreedUrl = 'https://api.alternative.me/fng/';
  static const String _binanceUrl = 'https://api.binance.com/api/v3';
  static const String baseUrl = 'http://localhost:4000'; // URL вашего бэкенда

  // Cache keys
  static const String _cacheKeyPrefix = 'market_sentiment_';
  static const String _fearGreedCacheKey = '${_cacheKeyPrefix}fear_greed';
  static const String _newsSentimentCacheKey = '${_cacheKeyPrefix}news_sentiment';
  static const String _holdersScoreCacheKey = '${_cacheKeyPrefix}holders_score';
  static const String _volumeScoreCacheKey = '${_cacheKeyPrefix}volume_score';
  static const String _socialEngagementCacheKey = '${_cacheKeyPrefix}social_engagement';
  static const String _priceVolatilityCacheKey = '${_cacheKeyPrefix}price_volatility';
  static const String _bitcoinDominanceCacheKey = '${_cacheKeyPrefix}bitcoin_dominance';
  static const String _lastUpdateCacheKey = '${_cacheKeyPrefix}last_update';
  static const String _lastIndicatorCacheKey = '${_cacheKeyPrefix}last_indicator';

  // Store the last calculated indicator value in memory
  double? _lastCalculatedIndicator;

  // Cache expiration time (3 minutes as requested)
  static const Duration _cacheExpiration = Duration(minutes: 3);

  // Get Fear & Greed Index from alternative.me API
  Future<double> fetchFearGreedIndex() async {
    debugPrint('Fetching Fear & Greed Index from alternative.me API...');
    try {
      // Make request to alternative.me API
      final response = await CustomHttpClient.get(Uri.parse('$_fearGreedUrl?limit=1'));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Log the response for debugging (truncated)
        debugPrint('Alternative.me API response: ${response.body.substring(0, response.body.length > 200 ? 200 : response.body.length)}...');

        // Validate the response structure
        if (data.containsKey('data') && data['data'] is List && data['data'].isNotEmpty) {
          // Extract the value from alternative.me response
          final value = double.parse(data['data'][0]['value']);
          debugPrint('Alternative.me Fear & Greed Index value: $value');

          // Alternative.me returns values in the range 0-100, so we can use it directly
          return value;
        } else {
          // Log the error and response structure
          debugPrint('Invalid response structure from alternative.me API:');
          debugPrint('Response keys: ${data.keys.toList()}');

          // Return default value on error
          return 50.0;
        }
      } else {
        debugPrint('Failed to load alternative.me fear & greed index: ${response.statusCode}');
        debugPrint('Response body: ${response.body}');

        // Return default value on error
        return 50.0;
      }
    } catch (e) {
      debugPrint('Error fetching alternative.me fear & greed index: $e');
      // Return default value on error
      return 50.0;
    }
  }

  // Get news sentiment from CryptoPanic with improved validation
  Future<double> fetchNewsSentiment() async {
    // Use CoinGecko as fallback for web platforms
    if (kIsWeb) {
      debugPrint('Using CoinGecko as proxy for news sentiment on web');
      return await fetchNewsSentimentFromCoinGecko();
    }

    try {
      final response = await CustomHttpClient.get(
        Uri.parse('https://cryptopanic.com/api/v1/posts/?auth_token=38f4c3b9e89ed5c0fda9211409cd20a05a19b079&currencies=BTC,ETH')
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Check if the API returned a valid response
        if (data.containsKey('results')) {
          final results = data['results'] as List;

          if (results.isEmpty) {
            return 0.0; // Neutral if no news
          }

          double sentiment = 0.0;
          int count = 0;

          // Calculate sentiment based on votes
          for (var news in results.take(10)) {
            final votes = news['votes'] ?? {};
            final positive = votes['positive'] ?? 0;
            final negative = votes['negative'] ?? 0;

            if (positive > 0 || negative > 0) {
              sentiment += (positive > negative) ? 1 : -1;
              count++;
            }
          }

          // Return normalized sentiment between -1 and 1
          return count > 0 ? sentiment / count : 0.0;
        } else if (data.containsKey('status') && data['status'] != 'ok') {
          throw Exception('API Error: ${data['info'] ?? 'Unknown error'}');
        } else {
          throw Exception('Invalid response format from CryptoPanic API');
        }
      } else {
        debugPrint('Failed to load news sentiment: Status ${response.statusCode}');
        // Fallback to CoinGecko immediately on error
        return await fetchNewsSentimentFromCoinGecko();
      }
    } catch (e) {
      debugPrint('Error fetching news sentiment: $e');
      // Fallback to CoinGecko immediately on error
      return await fetchNewsSentimentFromCoinGecko();
    }
  }

  // Fallback method to get sentiment data from CoinGecko
  Future<double> fetchNewsSentimentFromCoinGecko() async {
    try {
      final response = await CustomHttpClient.get(
          Uri.parse('https://api.coingecko.com/api/v3/coins/bitcoin'));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final priceChange24h = data['market_data']['price_change_percentage_24h'] ?? 0.0;
        // Convert price change to sentiment (-1 to 1 range)
        return (priceChange24h / 20).clamp(-1.0, 1.0); // Normalize to -1 to 1 range
      } else {
        debugPrint('Failed to load CoinGecko data: Status ${response.statusCode}');
        return 0.0; // Default neutral sentiment on error
      }
    } catch (e) {
      debugPrint('Error fetching news sentiment from CoinGecko: $e');
      return 0.0; // Default neutral sentiment on error
    }
  }

  // Get holders score using historical market cap and volume data from CoinGecko
  Future<double> fetchHoldersScore() async {
    try {
      // Get current data
      debugPrint('Fetching current Bitcoin data from CoinGecko...');
      final currentResponse = await CustomHttpClient.get(
          Uri.parse('https://api.coingecko.com/api/v3/coins/bitcoin'));

      if (currentResponse.statusCode != 200) {
        debugPrint('Failed to load CoinGecko current data: Status ${currentResponse.statusCode}');
        return 50.0; // Return default value on error
      }

      final currentData = jsonDecode(currentResponse.body);
      final marketCap = currentData['market_data']['market_cap']['usd'];
      final totalVolume = currentData['market_data']['total_volume']['usd'];

      debugPrint('Current market cap: $marketCap, Current volume: $totalVolume');

      // Get historical data for the last 30 days
      debugPrint('Fetching historical Bitcoin data from CoinGecko...');
      final historicalResponse = await CustomHttpClient.get(
          Uri.parse('https://api.coingecko.com/api/v3/coins/bitcoin/market_chart?vs_currency=usd&days=30&interval=daily'));

      if (historicalResponse.statusCode != 200) {
        debugPrint('Failed to load CoinGecko historical data: Status ${historicalResponse.statusCode}');
        return 50.0; // Return default value on error
      }

      final historicalData = jsonDecode(historicalResponse.body);
      final marketCaps = historicalData['market_caps'] as List<dynamic>;
      final volumes = historicalData['total_volumes'] as List<dynamic>;

      // Calculate average market cap and trading volume
      final averageMarketCap =
          marketCaps.map((entry) => entry[1] as num).reduce((a, b) => a + b) / marketCaps.length;
      final averageVolume =
          volumes.map((entry) => entry[1] as num).reduce((a, b) => a + b) / volumes.length;

      debugPrint('Average market cap (30d): $averageMarketCap, Average volume (30d): $averageVolume');

      // Calculate ratios
      final marketCapRatio = (marketCap / averageMarketCap).clamp(0.0, 2.0);
      final volumeRatio = (totalVolume / averageVolume).clamp(0.0, 2.0);

      debugPrint('Market cap ratio: $marketCapRatio, Volume ratio: $volumeRatio');

      // Combine metrics (50% market cap, 50% volume)
      final combinedScore = (marketCapRatio * 0.5 + volumeRatio * 0.5);

      // Normalize to 0-100 range
      final normalizedScore = (combinedScore * 50).clamp(0.0, 100.0);
      debugPrint('Combined score: $combinedScore, Normalized score: $normalizedScore');

      return normalizedScore;
    } catch (e) {
      debugPrint('Error fetching holders score: $e');
      return 50.0; // Default neutral value on error
    }
  }

  // Get volume score based on 24h trading volume with improved error handling
  Future<double> fetchVolumeScore() async {
    // Use CoinGecko as fallback for web platforms
    if (kIsWeb) {
      debugPrint('Using CoinGecko as proxy for volume data on web');
      return await fetchVolumeScoreFromCoinGecko();
    }

    try {
      final response = await CustomHttpClient.get(
        Uri.parse('$_binanceUrl/ticker/24hr?symbol=BTCUSDT')
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final currentVolume = double.parse(data['volume']);

        // Compare to average volume (this is a simplified approach)
        // In a real app, you would fetch historical volume data
        const averageVolume = 10000.0; // Example average volume

        // Calculate ratio (current / average)
        final ratio = currentVolume / averageVolume;
        debugPrint('Binance - Volume: $currentVolume, Ratio: $ratio');

        // Limit to a reasonable range
        return ratio.clamp(0.0, 2.0);
      } else {
        debugPrint('Failed to load volume data: Status ${response.statusCode}');
        throw Exception('Failed to load volume data: Status ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching volume data: $e');
      // Try CoinGecko as a fallback
      debugPrint('Falling back to CoinGecko for volume data');
      return await fetchVolumeScoreFromCoinGecko();
    }
  }

  // Fallback method to get volume data from CoinGecko
  Future<double> fetchVolumeScoreFromCoinGecko() async {
    try {
      final response = await CustomHttpClient.get(
          Uri.parse('https://api.coingecko.com/api/v3/coins/bitcoin'));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        // Use 24h trading volume as a direct replacement
        final volume = data['market_data']['total_volume']['usd'] ?? 0.0;
        // Use a reference value for normalization
        const averageVolume = 30000000000.0; // 30 billion USD
        final ratio = volume / averageVolume;
        debugPrint('CoinGecko - Volume: $volume, Ratio: $ratio');
        return ratio.clamp(0.0, 2.0);
      } else {
        debugPrint('Failed to load CoinGecko data: Status ${response.statusCode}');
        return 1.0; // Default neutral value on error
      }
    } catch (e) {
      debugPrint('Error fetching volume data from CoinGecko: $e');
      return 1.0; // Default neutral value on error
    }
  }

  // Get social engagement score based on Twitter followers and Reddit subscribers
  Future<double> fetchSocialEngagement() async {
    try {
      final response = await CustomHttpClient.get(
          Uri.parse('https://api.coingecko.com/api/v3/coins/bitcoin'));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Get Twitter followers and Reddit subscribers
        final twitterFollowers = (data['community_data']['twitter_followers'] ?? 0).toDouble();
        final redditSubscribers = (data['community_data']['reddit_subscribers'] ?? 0).toDouble();

        // Reference values for normalization
        const averageTwitterFollowers = 5000000.0; // 5 million followers
        const averageRedditSubscribers = 4000000.0; // 4 million subscribers

        // Calculate ratios
        final twitterRatio = (twitterFollowers / averageTwitterFollowers).clamp(0.0, 2.0);
        final redditRatio = (redditSubscribers / averageRedditSubscribers).clamp(0.0, 2.0);

        // Combine metrics (50% Twitter, 50% Reddit)
        final combinedRatio = (twitterRatio * 0.5 + redditRatio * 0.5);

        // Normalize to 0-100 range
        final score = combinedRatio * 50;

        debugPrint('Social Engagement - Twitter: $twitterFollowers, Reddit: $redditSubscribers');
        debugPrint('Social Engagement - Twitter Ratio: $twitterRatio, Reddit Ratio: $redditRatio');
        debugPrint('Social Engagement Score: $score');

        return score;
      } else {
        debugPrint('Failed to load CoinGecko data: Status ${response.statusCode}');
        return 50.0; // Default neutral value on error
      }
    } catch (e) {
      debugPrint('Error fetching social engagement: $e');
      return 50.0; // Default neutral value on error
    }
  }

  // Get price volatility based on 7-day price data
  Future<double> fetchPriceVolatility() async {
    try {
      final response = await CustomHttpClient.get(
          Uri.parse('https://api.coingecko.com/api/v3/coins/bitcoin/market_chart?vs_currency=usd&days=7&interval=daily'));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final prices = (data['prices'] as List<dynamic>).map((entry) => entry[1] as num).toList();

        // Calculate mean
        final mean = prices.reduce((a, b) => a + b) / prices.length;

        // Calculate variance
        final variance = prices.map((x) => pow(x - mean, 2)).reduce((a, b) => a + b) / prices.length;

        // Calculate standard deviation (volatility)
        final volatility = sqrt(variance);

        // Reference value for normalization
        const averageVolatility = 1000.0; // Adjust based on typical BTC volatility

        // Calculate ratio and normalize
        final ratio = (volatility / averageVolatility).clamp(0.0, 2.0);
        final score = ratio * 50;

        debugPrint('Price Volatility - Standard Deviation: $volatility, Ratio: $ratio');
        debugPrint('Price Volatility Score: $score');

        return score;
      } else {
        debugPrint('Failed to load CoinGecko market chart data: Status ${response.statusCode}');
        return 50.0; // Default neutral value on error
      }
    } catch (e) {
      debugPrint('Error fetching price volatility: $e');
      return 50.0; // Default neutral value on error
    }
  }

  // Get Bitcoin dominance in the overall crypto market
  Future<double> fetchBitcoinDominance() async {
    try {
      final response = await CustomHttpClient.get(
          Uri.parse('https://api.coingecko.com/api/v3/global'));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final dominance = data['data']['market_cap_percentage']['btc'] ?? 50.0;

        debugPrint('Bitcoin Dominance: $dominance%');

        return dominance.clamp(0.0, 100.0);
      } else {
        debugPrint('Failed to load CoinGecko global data: Status ${response.statusCode}');
        return 50.0; // Default neutral value on error
      }
    } catch (e) {
      debugPrint('Error fetching Bitcoin dominance: $e');
      return 50.0; // Default neutral value on error
    }
  }

  // Check if cache is valid with more strict validation
  Future<bool> isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdateStr = prefs.getString(_lastUpdateCacheKey);

      if (lastUpdateStr == null) {
        debugPrint('No cache timestamp found');
        return false;
      }

      final lastUpdate = DateTime.parse(lastUpdateStr);
      final now = DateTime.now();
      final difference = now.difference(lastUpdate);

      // Use the standard cache expiration time (3 minutes)
      final isTimeValid = difference < _cacheExpiration;
      debugPrint('Cache timestamp: $lastUpdate, now: $now, difference: ${difference.inSeconds}s');

      // Check if all required metrics are present in the cache
      final hasFearGreed = prefs.containsKey(_fearGreedCacheKey);
      final hasNewsSentiment = prefs.containsKey(_newsSentimentCacheKey);
      final hasHoldersScore = prefs.containsKey(_holdersScoreCacheKey);
      final hasVolumeScore = prefs.containsKey(_volumeScoreCacheKey);
      final hasSocialEngagement = prefs.containsKey(_socialEngagementCacheKey);
      final hasPriceVolatility = prefs.containsKey(_priceVolatilityCacheKey);
      final hasBitcoinDominance = prefs.containsKey(_bitcoinDominanceCacheKey);

      final hasAllMetrics = hasFearGreed && hasNewsSentiment && hasHoldersScore &&
                           hasVolumeScore && hasSocialEngagement &&
                           hasPriceVolatility && hasBitcoinDominance;

      if (!hasAllMetrics) {
        debugPrint('Cache is missing some metrics');
        return false;
      }

      // Check if prediction cache is also valid
      final hasPredictions = prefs.containsKey(SentimentPredictionService.predictionsKey);
      final hasMetricsHash = prefs.containsKey(SentimentPredictionService.lastMetricsKey);

      final hasPredictionCache = hasPredictions && hasMetricsHash;
      if (!hasPredictionCache) {
        debugPrint('Prediction cache is missing');
      }

      // Cache is valid only if time is valid, all metrics are present, and prediction cache exists
      final isValid = isTimeValid && hasAllMetrics && hasPredictionCache;

      debugPrint('Cache is ${isValid ? 'valid' : 'expired'} (using ${_cacheExpiration.inMinutes}m expiration)');
      debugPrint('Time valid: $isTimeValid, All metrics present: $hasAllMetrics, Prediction cache exists: $hasPredictionCache');

      return isValid;
    } catch (e) {
      debugPrint('Error checking cache validity: $e');
      return false;
    }
  }

  // Get the last calculated indicator value
  double? getLastCalculatedIndicator() {
    try {
      // Check if we have a cached indicator value
      final cachedIndicator = _lastCalculatedIndicator;
      if (cachedIndicator != null && cachedIndicator > 0) {
        debugPrint('Using last calculated indicator: $cachedIndicator');
        return cachedIndicator;
      }

      // If no cached value, try to get from SharedPreferences synchronously
      // We can't use async here, so we'll return the in-memory value or null
      _loadLastIndicatorFromPrefs(); // This will load it for next time
      return null;
    } catch (e) {
      debugPrint('Error getting last calculated indicator: $e');
      return null;
    }
  }

  // Load the last indicator value from SharedPreferences
  Future<void> _loadLastIndicatorFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Try to get the last calculated indicator
      final lastIndicator = prefs.getDouble(_lastIndicatorCacheKey);
      if (lastIndicator != null && lastIndicator > 0) {
        debugPrint('Loaded last indicator from prefs: $lastIndicator');
        _lastCalculatedIndicator = lastIndicator;
      }
    } catch (e) {
      debugPrint('Error loading last indicator from prefs: $e');
    }
  }

  // Save the last calculated indicator
  Future<void> _saveLastCalculatedIndicator(double value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_lastIndicatorCacheKey, value);
      debugPrint('Saved last calculated indicator: $value');
    } catch (e) {
      debugPrint('Error saving last calculated indicator: $e');
    }
  }

  // Save metric to cache
  Future<void> _saveMetricToCache(String key, double value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(key, value);
    } catch (e) {
      debugPrint('Error saving metric to cache: $e');
    }
  }

  // Get metric from cache
  Future<double?> _getMetricFromCache(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getDouble(key);
    } catch (e) {
      debugPrint('Error getting metric from cache: $e');
      return null;
    }
  }

  // Update cache timestamp
  Future<void> _updateCacheTimestamp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      await prefs.setString(_lastUpdateCacheKey, now.toIso8601String());
      debugPrint('Updated cache timestamp to: $now');
    } catch (e) {
      debugPrint('Error updating cache timestamp: $e');
    }
  }

  // Clear all cached data (for testing)
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // Clear metric caches
      await prefs.remove(_fearGreedCacheKey);
      await prefs.remove(_newsSentimentCacheKey);
      await prefs.remove(_holdersScoreCacheKey);
      await prefs.remove(_volumeScoreCacheKey);
      await prefs.remove(_socialEngagementCacheKey);
      await prefs.remove(_priceVolatilityCacheKey);
      await prefs.remove(_bitcoinDominanceCacheKey);
      await prefs.remove(_lastUpdateCacheKey);

      // Clear prediction caches
      await prefs.remove(SentimentPredictionService.predictionsKey);
      await prefs.remove(SentimentPredictionService.lastMetricsKey);

      debugPrint('Cleared all cached data including predictions');
    } catch (e) {
      debugPrint('Error clearing cache: $e');
    }
  }

  // Save current sentiment data to history
  Future<void> saveToHistory(double indicatorValue, Map<String, double> metrics) async {
    try {
      // Create a new history entry
      final entry = SentimentHistoryEntry(
        date: DateTime.now(),
        value: indicatorValue,
        metrics: metrics,
      );

      // Get the sentiment prediction service
      final predictionService = SentimentPredictionService();

      // Add the entry to history
      await predictionService.addHistoricalEntry(entry);

      // Log the saved data for debugging
      debugPrint('Saved current sentiment data to history:');
      debugPrint('Date: ${entry.date}');
      debugPrint('Value: ${entry.value}');
      debugPrint('Metrics: ${entry.metrics}');
    } catch (e) {
      debugPrint('Error saving to history: $e');
    }
  }

  // Calculate the combined market sentiment indicator with improved error handling
  Future<double> calculateMarketSentiment() async {
    try {
      // Check if we have valid cached data
      final cacheValid = await isCacheValid();

      if (cacheValid) {
        debugPrint('Using cached sentiment metrics...');

        // Get metrics from cache
        final fearGreedIndex = await _getMetricFromCache(_fearGreedCacheKey) ?? 50.0;
        final newsSentiment = await _getMetricFromCache(_newsSentimentCacheKey) ?? 0.0;
        final holdersScore = await _getMetricFromCache(_holdersScoreCacheKey) ?? 50.0;
        final volumeScore = await _getMetricFromCache(_volumeScoreCacheKey) ?? 1.0;
        final socialEngagement = await _getMetricFromCache(_socialEngagementCacheKey) ?? 50.0;
        final priceVolatility = await _getMetricFromCache(_priceVolatilityCacheKey) ?? 50.0;
        final bitcoinDominance = await _getMetricFromCache(_bitcoinDominanceCacheKey) ?? 50.0;

        // Normalize news sentiment from [-1, 1] to [0, 100]
        final normalizedNewsSentiment = (newsSentiment + 1) * 50;

        // Normalize volume score from [0, 2.0] to [0, 100]
        final normalizedVolumeScore = volumeScore.clamp(0.0, 1.0) * 100;

        // Calculate and return the weighted average
        return _calculateWeightedAverage(
          fearGreedIndex,
          normalizedNewsSentiment,
          holdersScore,
          normalizedVolumeScore,
          socialEngagement,
          priceVolatility,
          bitcoinDominance
        );
      }

      // Fetch all metrics in parallel
      debugPrint('Fetching all market sentiment metrics in parallel...');

      final futures = await Future.wait([
        fetchFearGreedIndex(),
        fetchNewsSentiment(),
        fetchHoldersScore(),
        fetchVolumeScore(),
        fetchSocialEngagement(),
        fetchPriceVolatility(),
        fetchBitcoinDominance(),
      ]);

      // Extract results
      final fearGreedIndex = futures[0];
      final newsSentiment = futures[1];
      final holdersScore = futures[2];
      final volumeScore = futures[3];
      final socialEngagement = futures[4];
      final priceVolatility = futures[5];
      final bitcoinDominance = futures[6];

      // Save metrics to cache
      await _saveMetricToCache(_fearGreedCacheKey, fearGreedIndex);
      await _saveMetricToCache(_newsSentimentCacheKey, newsSentiment);
      await _saveMetricToCache(_holdersScoreCacheKey, holdersScore);
      await _saveMetricToCache(_volumeScoreCacheKey, volumeScore);
      await _saveMetricToCache(_socialEngagementCacheKey, socialEngagement);
      await _saveMetricToCache(_priceVolatilityCacheKey, priceVolatility);
      await _saveMetricToCache(_bitcoinDominanceCacheKey, bitcoinDominance);

      // Update cache timestamp
      await _updateCacheTimestamp();

      // Normalize news sentiment from [-1, 1] to [0, 100]
      final normalizedNewsSentiment = (newsSentiment + 1) * 50;

      // Normalize volume score from [0, 2.0] to [0, 100]
      final normalizedVolumeScore = volumeScore.clamp(0.0, 1.0) * 100;

      // Log all metrics
      debugPrint('=== MARKET SENTIMENT METRICS ===');
      debugPrint('Fear & Greed Index: $fearGreedIndex');
      debugPrint('News Sentiment: $normalizedNewsSentiment');
      debugPrint('Holders Score: $holdersScore');
      debugPrint('Volume Score: $normalizedVolumeScore');
      debugPrint('Social Engagement: $socialEngagement');
      debugPrint('Price Volatility: $priceVolatility');
      debugPrint('Bitcoin Dominance: $bitcoinDominance');

      // Calculate weighted average
      final finalScore = _calculateWeightedAverage(
        fearGreedIndex,
        normalizedNewsSentiment,
        holdersScore,
        normalizedVolumeScore,
        socialEngagement,
        priceVolatility,
        bitcoinDominance
      );

      // Save to history
      final metrics = {
        'Fear & Greed Index': fearGreedIndex,
        'News Sentiment': normalizedNewsSentiment,
        'Holders Score': holdersScore,
        'Volume Score': normalizedVolumeScore,
        'Social Engagement': socialEngagement,
        'Price Volatility': priceVolatility,
        'Bitcoin Dominance': bitcoinDominance,
      };

      await saveToHistory(finalScore, metrics);

      // Save the last calculated indicator
      _lastCalculatedIndicator = finalScore;
      await _saveLastCalculatedIndicator(finalScore);

      return finalScore;
    } catch (e) {
      debugPrint('Overall fetch error: $e');
      return 50.0; // Default neutral value
    }
  }

  /// Get historical data
  Future<SentimentHistory> getHistoricalData() async {
    try {
      // Get the sentiment prediction service
      final predictionService = SentimentPredictionService();

      // Get historical data from the prediction service
      return await predictionService.getHistoricalData();
    } catch (e) {
      debugPrint('Error getting historical data: $e');
      // Return empty history if there's an error
      return SentimentHistory(entries: []);
    }
  }

  // Helper method to calculate the weighted average of all metrics
  double _calculateWeightedAverage(
    double fearGreedIndex,
    double normalizedNewsSentiment,
    double holdersScore,
    double normalizedVolumeScore,
    double socialEngagement,
    double priceVolatility,
    double bitcoinDominance,
  ) {
    // Check if all values are default (50.0)
    bool allDefaultValues =
        (fearGreedIndex == 50.0 || fearGreedIndex.isNaN) &&
        (normalizedNewsSentiment == 50.0 || normalizedNewsSentiment.isNaN) &&
        (holdersScore == 50.0 || holdersScore.isNaN) &&
        (normalizedVolumeScore == 50.0 || normalizedVolumeScore.isNaN) &&
        (socialEngagement == 50.0 || socialEngagement.isNaN) &&
        (priceVolatility == 50.0 || priceVolatility.isNaN) &&
        (bitcoinDominance == 50.0 || bitcoinDominance.isNaN);

    // If all values are default, return a slightly randomized value to make the indicator move
    if (allDefaultValues) {
      final random = Random();
      final randomizedValue = 45.0 + random.nextDouble() * 10.0;
      debugPrint('All metrics are default values, returning randomized value: $randomizedValue');
      return randomizedValue;
    }

    // Calculate weighted average with new weights
    // Original metrics: 70% total (reduced from 100%)
    // New metrics: 30% total (10% each)
    final weightedAverage =
        (fearGreedIndex.isNaN ? 50.0 : fearGreedIndex * 0.30) +                // 30% Fear & Greed (was 40%)
        (normalizedNewsSentiment.isNaN ? 50.0 : normalizedNewsSentiment * 0.20) +       // 20% News (was 30%)
        (holdersScore.isNaN ? 50.0 : holdersScore * 0.10) +                  // 10% Holders (was 15%)
        (normalizedVolumeScore.isNaN ? 50.0 : normalizedVolumeScore * 0.10) +         // 10% Volume (was 15%)
        (socialEngagement.isNaN ? 50.0 : socialEngagement * 0.10) +              // 10% Social Engagement (new)
        (priceVolatility.isNaN ? 50.0 : priceVolatility * 0.10) +               // 10% Price Volatility (new)
        (bitcoinDominance.isNaN ? 50.0 : bitcoinDominance * 0.10);               // 10% Bitcoin Dominance (new)

    final finalScore = weightedAverage.clamp(0.0, 100.0);
    debugPrint('Final market sentiment score: $finalScore');

    // Interpret the score
    String marketPhase;
    if (finalScore <= 20) {
      marketPhase = "Extreme Fear (Crash)";
    } else if (finalScore <= 40) {
      marketPhase = "Fear (Anxiety)";
    } else if (finalScore <= 60) {
      marketPhase = "Neutral (Stasis)";
    } else if (finalScore <= 80) {
      marketPhase = "Greed (Lift)";
    } else {
      marketPhase = "Extreme Greed (Surge)";
    }

    debugPrint('Market Phase: $marketPhase');
    debugPrint('================================');

    return finalScore;
  }

  Future<MarketSentiment> analyzeMarketSentiment() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/sentiment'));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return MarketSentiment.fromJson(data);
      } else {
        throw Exception('Failed to load market sentiment: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to load market sentiment: $e');
    }
  }

  // Вспомогательные методы для расчета настроений рынка
  double calculateSentimentScore(List<Map<String, dynamic>> news) {
    if (news.isEmpty) return 0.0;

    double totalScore = 0.0;
    int count = 0;

    for (var item in news) {
      if (item['sentiment'] != null) {
        switch (item['sentiment'].toLowerCase()) {
          case 'bullish':
            totalScore += 1.0;
            break;
          case 'bearish':
            totalScore -= 1.0;
            break;
          case 'neutral':
            totalScore += 0.0;
            break;
        }
        count++;
      }
    }

    return count > 0 ? totalScore / count : 0.0;
  }

  String getSentimentLabel(double score) {
    if (score > 0.3) return 'bullish';
    if (score < -0.3) return 'bearish';
    return 'neutral';
  }
}