import 'dart:convert';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quiz_question.dart';

class QuizService {
  static QuizService? _instance;
  static QuizService get instance => _instance ??= QuizService._();
  QuizService._();

  List<QuizQuestion> _allQuestions = [];
  List<QuizQuestion> _currentSessionQuestions = [];
  int _currentQuestionIndex = 0;
  int _correctAnswers = 0;
  int _totalQuestions = 0;
  DateTime? _sessionStartTime;

  // Геттеры для статистики
  List<QuizQuestion> get allQuestions => _allQuestions;
  QuizQuestion? get currentQuestion => 
      _currentSessionQuestions.isNotEmpty && _currentQuestionIndex < _currentSessionQuestions.length
          ? _currentSessionQuestions[_currentQuestionIndex]
          : null;
  int get currentQuestionIndex => _currentQuestionIndex;
  int get correctAnswers => _correctAnswers;
  int get totalQuestions => _totalQuestions;
  int get sessionQuestionsCount => _currentSessionQuestions.length;
  double get accuracy => _totalQuestions > 0 ? (_correctAnswers / _totalQuestions) * 100 : 0.0;

  // Загрузка вопросов из JSON файла
  Future<void> loadQuestions() async {
    if (_allQuestions.isNotEmpty) return;

    try {
      final String jsonString = await rootBundle.loadString('assets/data/quiz_questions.json');
      final List<dynamic> jsonList = json.decode(jsonString);
      
      _allQuestions = jsonList.map((json) => QuizQuestion.fromJson(json)).toList();
    } catch (e) {
      // Если не удалось загрузить из файла, используем заранее подготовленные вопросы
      _allQuestions = _getMockQuestions();
    }
  }

  // Начать новую игровую сессию
  void startNewSession({
    QuizCategory? category,
    DifficultyLevel? difficulty,
    int questionCount = 20,
  }) {
    _sessionStartTime = DateTime.now();
    _currentQuestionIndex = 0;
    _correctAnswers = 0;
    _totalQuestions = 0;

    // Фильтрация вопросов по категории и сложности
    List<QuizQuestion> filteredQuestions = _allQuestions;

    if (category != null) {
      filteredQuestions = filteredQuestions.where((q) => q.category == category).toList();
    }

    if (difficulty != null) {
      filteredQuestions = filteredQuestions.where((q) => q.difficulty == difficulty).toList();
    }

    // Случайный выбор вопросов
    filteredQuestions.shuffle(Random());
    _currentSessionQuestions = filteredQuestions.take(questionCount).toList();
    
    // Перемешиваем ответы для каждого вопроса
    _shuffleAnswersInQuestions();
  }

  // Начать случайную игровую сессию
  void startRandomSession({
    int questionCount = 20,
  }) {
    _sessionStartTime = DateTime.now();
    _currentQuestionIndex = 0;
    _correctAnswers = 0;
    _totalQuestions = 0;

    // Получаем все вопросы и перемешиваем их
    List<QuizQuestion> randomQuestions = List<QuizQuestion>.from(_allQuestions);
    randomQuestions.shuffle(Random());
    _currentSessionQuestions = randomQuestions.take(questionCount).toList();
    
    // Перемешиваем ответы для каждого вопроса
    _shuffleAnswersInQuestions();
  }

  // Перемешивание ответов в каждом вопросе сессии
  void _shuffleAnswersInQuestions() {
    for (int i = 0; i < _currentSessionQuestions.length; i++) {
      final question = _currentSessionQuestions[i];
      final shuffledOptions = List<String>.from(question.options);
      shuffledOptions.shuffle(Random());
      
      // Создаем новый вопрос с перемешанными ответами
      _currentSessionQuestions[i] = QuizQuestion(
        id: question.id,
        question: question.question,
        options: shuffledOptions,
        correctAnswer: question.correctAnswer,
        category: question.category,
        difficulty: question.difficulty,
        explanation: question.explanation,
      );
    }
  }

  // Ответить на текущий вопрос
  bool answerCurrentQuestion(String answer) {
    if (currentQuestion == null) return false;

    _totalQuestions++;
    bool isCorrect = currentQuestion!.correctAnswer == answer;
    
    if (isCorrect) {
      _correctAnswers++;
    }

    return isCorrect;
  }

  // Перейти к следующему вопросу
  bool nextQuestion() {
    if (_currentQuestionIndex < _currentSessionQuestions.length - 1) {
      _currentQuestionIndex++;
      return true;
    }
    return false;
  }

  // Проверить, есть ли еще вопросы
  bool hasNextQuestion() {
    return _currentQuestionIndex < _currentSessionQuestions.length - 1;
  }

  // Получить результаты сессии
  Map<String, dynamic> getSessionResults() {
    final duration = _sessionStartTime != null 
        ? DateTime.now().difference(_sessionStartTime!)
        : Duration.zero;

    return {
      'correct_answers': _correctAnswers,
      'total_questions': _totalQuestions,
      'accuracy': accuracy,
      'duration': duration.inSeconds,
      'questions_in_session': sessionQuestionsCount,
    };
  }

  // Сохранить статистику
  Future<void> saveSessionStats() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Обновляем общую статистику
    final totalQuestions = prefs.getInt('total_questions_answered') ?? 0;
    final totalCorrect = prefs.getInt('total_correct_answers') ?? 0;
    final sessionsPlayed = prefs.getInt('sessions_played') ?? 0;
    
    await prefs.setInt('total_questions_answered', totalQuestions + _totalQuestions);
    await prefs.setInt('total_correct_answers', totalCorrect + _correctAnswers);
    await prefs.setInt('sessions_played', sessionsPlayed + 1);
    
    // Сохраняем лучший результат
    final bestAccuracy = prefs.getDouble('best_accuracy') ?? 0.0;
    if (accuracy > bestAccuracy) {
      await prefs.setDouble('best_accuracy', accuracy);
    }
  }

  // Получить общую статистику
  Future<Map<String, dynamic>> getOverallStats() async {
    final prefs = await SharedPreferences.getInstance();
    
    return {
      'total_questions_answered': prefs.getInt('total_questions_answered') ?? 0,
      'total_correct_answers': prefs.getInt('total_correct_answers') ?? 0,
      'sessions_played': prefs.getInt('sessions_played') ?? 0,
      'best_accuracy': prefs.getDouble('best_accuracy') ?? 0.0,
    };
  }

  // Получить вопросы по категории
  List<QuizQuestion> getQuestionsByCategory(QuizCategory category) {
    return _allQuestions.where((q) => q.category == category).toList();
  }

  // Получить случайные вопросы
  List<QuizQuestion> getRandomQuestions(int count) {
    final shuffled = List<QuizQuestion>.from(_allQuestions);
    shuffled.shuffle(Random());
    return shuffled.take(count).toList();
  }

  // Заранее подготовленные вопросы (на случай если не загрузится JSON файл)
  List<QuizQuestion> _getMockQuestions() {
    return [
      // Bitcoin вопросы (Easy)
      QuizQuestion(
        id: 1,
        question: "Кто является создателем Bitcoin?",
        options: ["Виталик Бутерин", "Сатоши Накамото", "Чарльз Хоскинсон", "Эрик Ворhис"],
        correctAnswer: "Сатоши Накамото",
        category: QuizCategory.bitcoin,
        difficulty: DifficultyLevel.easy,
        explanation: "Сатоши Накамото — псевдоним создателя Bitcoin, чья настоящая личность остается неизвестной.",
      ),
      QuizQuestion(
        id: 2,
        question: "Сколько максимально может быть создано Bitcoin?",
        options: ["18 миллионов", "21 миллион", "25 миллионов", "Неограниченно"],
        correctAnswer: "21 миллион",
        category: QuizCategory.bitcoin,
        difficulty: DifficultyLevel.easy,
        explanation: "Максимальное количество Bitcoin ограничено 21 миллионом монет, что обеспечивает дефицитность.",
      ),
      QuizQuestion(
        id: 3,
        question: "В каком году был создан Bitcoin?",
        options: ["2008", "2009", "2010", "2011"],
        correctAnswer: "2009",
        category: QuizCategory.bitcoin,
        difficulty: DifficultyLevel.easy,
        explanation: "Bitcoin был запущен в январе 2009 года.",
      ),
      QuizQuestion(
        id: 4,
        question: "Как называется первый блок в блокчейне Bitcoin?",
        options: ["Первичный блок", "Genesis Block", "Начальный блок", "Блок #0"],
        correctAnswer: "Genesis Block",
        category: QuizCategory.bitcoin,
        difficulty: DifficultyLevel.medium,
        explanation: "Genesis Block — это первый блок в блокчейне Bitcoin, созданный Сатоши Накамото.",
      ),
      QuizQuestion(
        id: 5,
        question: "Что такое халвинг в Bitcoin?",
        options: ["Удвоение цены", "Сокращение награды майнерам вдвое", "Разделение блокчейна", "Удвоение комиссий"],
        correctAnswer: "Сокращение награды майнерам вдвое",
        category: QuizCategory.bitcoin,
        difficulty: DifficultyLevel.medium,
        explanation: "Халвинг происходит каждые 210,000 блоков и сокращает награду майнерам вдвое.",
      ),
      
      // Ethereum вопросы
      QuizQuestion(
        id: 6,
        question: "Кто создал Ethereum?",
        options: ["Сатоши Накамото", "Виталик Бутерин", "Чарльз Хоскинсон", "Гэвин Вуд"],
        correctAnswer: "Виталик Бутерин",
        category: QuizCategory.ethereum,
        difficulty: DifficultyLevel.easy,
        explanation: "Виталик Бутерин предложил концепцию Ethereum в 2013 году.",
      ),
      QuizQuestion(
        id: 7,
        question: "Что такое газ (Gas) в Ethereum?",
        options: ["Топливо для автомобилей", "Единица измерения вычислительных усилий", "Новая криптовалюта", "Тип смарт-контракта"],
        correctAnswer: "Единица измерения вычислительных усилий",
        category: QuizCategory.ethereum,
        difficulty: DifficultyLevel.medium,
        explanation: "Газ измеряет количество вычислительных усилий, необходимых для выполнения операций в сети Ethereum.",
      ),
      QuizQuestion(
        id: 8,
        question: "Как называется обновление Ethereum 2.0?",
        options: ["Serenity", "Constantinople", "Byzantium", "London"],
        correctAnswer: "Serenity",
        category: QuizCategory.ethereum,
        difficulty: DifficultyLevel.hard,
        explanation: "Ethereum 2.0 также известен как Serenity и включает переход на Proof of Stake.",
      ),
      QuizQuestion(
        id: 9,
        question: "Что такое EVM?",
        options: ["Ethereum Virtual Machine", "Ethereum Value Market", "Ethereum Verification Method", "Ethereum Video Module"],
        correctAnswer: "Ethereum Virtual Machine",
        category: QuizCategory.ethereum,
        difficulty: DifficultyLevel.medium,
        explanation: "EVM - это виртуальная машина Ethereum, которая исполняет смарт-контракты.",
      ),
      QuizQuestion(
        id: 10,
        question: "Какой стандарт токенов наиболее популярен в Ethereum?",
        options: ["ERC-20", "ERC-721", "ERC-1155", "ERC-777"],
        correctAnswer: "ERC-20",
        category: QuizCategory.ethereum,
        difficulty: DifficultyLevel.easy,
        explanation: "ERC-20 - это стандарт для взаимозаменяемых токенов в сети Ethereum.",
      ),
      
      // Blockchain вопросы
      QuizQuestion(
        id: 11,
        question: "Что такое блокчейн?",
        options: ["Цифровая валюта", "Распределенная база данных", "Компьютерная программа", "Интернет-браузер"],
        correctAnswer: "Распределенная база данных",
        category: QuizCategory.blockchain,
        difficulty: DifficultyLevel.easy,
        explanation: "Блокчейн — это распределенная база данных, которая хранит информацию в блоках, связанных криптографически.",
      ),
      QuizQuestion(
        id: 12,
        question: "Что такое консенсус Proof of Work?",
        options: ["Доказательство работы", "Доказательство владения", "Доказательство истории", "Доказательство ставки"],
        correctAnswer: "Доказательство работы",
        category: QuizCategory.blockchain,
        difficulty: DifficultyLevel.medium,
        explanation: "Proof of Work — это алгоритм консенсуса, где майнеры решают сложные математические задачи.",
      ),
      QuizQuestion(
        id: 13,
        question: "Что означает термин 'нода' в блокчейне?",
        options: ["Узел сети", "Тип криптовалюты", "Метод шифрования", "Протокол консенсуса"],
        correctAnswer: "Узел сети",
        category: QuizCategory.blockchain,
        difficulty: DifficultyLevel.easy,
        explanation: "Нода - это компьютер, который участвует в поддержании блокчейн-сети.",
      ),
      QuizQuestion(
        id: 14,
        question: "Что такое fork в блокчейне?",
        options: ["Столовый прибор", "Разделение блокчейна", "Тип кошелька", "Протокол майнинга"],
        correctAnswer: "Разделение блокчейна",
        category: QuizCategory.blockchain,
        difficulty: DifficultyLevel.medium,
        explanation: "Fork - это изменение протокола блокчейна, которое может привести к разделению сети.",
      ),
      QuizQuestion(
        id: 15,
        question: "Что такое хэш в блокчейне?",
        options: ["Уникальный идентификатор блока", "Способ хранения данных", "Тип транзакции", "Протокол связи"],
        correctAnswer: "Уникальный идентификатор блока",
        category: QuizCategory.blockchain,
        difficulty: DifficultyLevel.medium,
        explanation: "Хэш - это уникальная цифровая подпись блока, созданная криптографической функцией.",
      ),
      
      // Trading вопросы
      QuizQuestion(
        id: 16,
        question: "Что означает аббревиатура HODL?",
        options: ["Hold On for Dear Life", "High Order Digital Logic", "Hold Original Data Long", "Hope Of Digital Luck"],
        correctAnswer: "Hold On for Dear Life",
        category: QuizCategory.trading,
        difficulty: DifficultyLevel.easy,
        explanation: "HODL означает 'Hold On for Dear Life' — стратегия долгосрочного удержания криптовалют.",
      ),
      QuizQuestion(
        id: 17,
        question: "Что такое ордер Stop-Loss?",
        options: ["Ордер на покупку", "Ордер на ограничение убытков", "Ордер на получение прибыли", "Ордер на отмену сделки"],
        correctAnswer: "Ордер на ограничение убытков",
        category: QuizCategory.trading,
        difficulty: DifficultyLevel.medium,
        explanation: "Stop-Loss автоматически продает актив при достижении определенного уровня убытка.",
      ),
      QuizQuestion(
        id: 18,
        question: "Что означает термин 'Bull Market'?",
        options: ["Падающий рынок", "Растущий рынок", "Боковой рынок", "Волатильный рынок"],
        correctAnswer: "Растущий рынок",
        category: QuizCategory.trading,
        difficulty: DifficultyLevel.easy,
        explanation: "Bull Market - это период устойчивого роста цен на рынке.",
      ),
      QuizQuestion(
        id: 19,
        question: "Что такое маржинальная торговля?",
        options: ["Торговля с заемными средствами", "Торговля без комиссий", "Торговля в выходные", "Торговля криптовалютами"],
        correctAnswer: "Торговля с заемными средствами",
        category: QuizCategory.trading,
        difficulty: DifficultyLevel.hard,
        explanation: "Маржинальная торговля позволяет торговать с заемными средствами, увеличивая потенциальную прибыль и риски.",
      ),
      QuizQuestion(
        id: 20,
        question: "Что означает термин 'Bear Market'?",
        options: ["Растущий рынок", "Падающий рынок", "Стабильный рынок", "Новый рынок"],
        correctAnswer: "Падающий рынок",
        category: QuizCategory.trading,
        difficulty: DifficultyLevel.easy,
        explanation: "Bear Market - это период устойчивого падения цен на рынке.",
      ),
      
      // DeFi вопросы
      QuizQuestion(
        id: 21,
        question: "Что означает DeFi?",
        options: ["Digital Finance", "Decentralized Finance", "Distributed Finance", "Delegated Finance"],
        correctAnswer: "Decentralized Finance",
        category: QuizCategory.defi,
        difficulty: DifficultyLevel.easy,
        explanation: "DeFi означает Decentralized Finance — децентрализованные финансовые услуги.",
      ),
      QuizQuestion(
        id: 22,
        question: "Что такое Yield Farming?",
        options: ["Выращивание криптовалют", "Получение доходности через предоставление ликвидности", "Майнинг новых токенов", "Торговля на бирже"],
        correctAnswer: "Получение доходности через предоставление ликвидности",
        category: QuizCategory.defi,
        difficulty: DifficultyLevel.medium,
        explanation: "Yield Farming — это процесс получения дохода через предоставление ликвидности в DeFi протоколах.",
      ),
      QuizQuestion(
        id: 23,
        question: "Что такое AMM?",
        options: ["Automated Market Maker", "Advanced Money Management", "Algorithmic Mining Method", "Asset Management Module"],
        correctAnswer: "Automated Market Maker",
        category: QuizCategory.defi,
        difficulty: DifficultyLevel.hard,
        explanation: "AMM - это алгоритм, который создает рынок для торговли активами без традиционной книги ордеров.",
      ),
      QuizQuestion(
        id: 24,
        question: "Что такое Impermanent Loss?",
        options: ["Временная потеря при предоставлении ликвидности", "Потеря приватных ключей", "Сбой в сети", "Хакерская атака"],
        correctAnswer: "Временная потеря при предоставлении ликвидности",
        category: QuizCategory.defi,
        difficulty: DifficultyLevel.hard,
        explanation: "Impermanent Loss - это временная потеря стоимости, которая может возникнуть при предоставлении ликвидности в пулы.",
      ),
      QuizQuestion(
        id: 25,
        question: "Что такое Flash Loan?",
        options: ["Быстрый кредит без залога", "Мгновенный обмен", "Быстрая транзакция", "Срочная продажа"],
        correctAnswer: "Быстрый кредит без залога",
        category: QuizCategory.defi,
        difficulty: DifficultyLevel.expert,
        explanation: "Flash Loan - это кредит, который должен быть взят и возвращен в рамках одной транзакции.",
      ),
      
      // Security вопросы
      QuizQuestion(
        id: 26,
        question: "Что такое приватный ключ?",
        options: ["Публичный адрес кошелька", "Секретный код для доступа к средствам", "Номер транзакции", "Идентификатор пользователя"],
        correctAnswer: "Секретный код для доступа к средствам",
        category: QuizCategory.security,
        difficulty: DifficultyLevel.easy,
        explanation: "Приватный ключ — это секретный код, который дает полный контроль над криптовалютными средствами.",
      ),
      QuizQuestion(
        id: 27,
        question: "Что такое seed-фраза?",
        options: ["Пароль от биржи", "Набор слов для восстановления кошелька", "Адрес кошелька", "Номер банковской карты"],
        correctAnswer: "Набор слов для восстановления кошелька",
        category: QuizCategory.security,
        difficulty: DifficultyLevel.medium,
        explanation: "Seed-фраза — это набор из 12-24 слов, который используется для восстановления криптокошелька.",
      ),
      QuizQuestion(
        id: 28,
        question: "Что такое двухфакторная аутентификация (2FA)?",
        options: ["Два пароля", "Дополнительный уровень безопасности", "Двойная проверка транзакций", "Два кошелька"],
        correctAnswer: "Дополнительный уровень безопасности",
        category: QuizCategory.security,
        difficulty: DifficultyLevel.easy,
        explanation: "2FA добавляет дополнительный уровень безопасности, требуя второй способ подтверждения личности.",
      ),
      QuizQuestion(
        id: 29,
        question: "Что такое холодное хранение?",
        options: ["Хранение в холодильнике", "Офлайн хранение криптовалют", "Замороженные активы", "Старые кошельки"],
        correctAnswer: "Офлайн хранение криптовалют",
        category: QuizCategory.security,
        difficulty: DifficultyLevel.medium,
        explanation: "Холодное хранение - это хранение криптовалют в офлайн-устройствах для максимальной безопасности.",
      ),
      QuizQuestion(
        id: 30,
        question: "Что такое фишинг в криптовалютах?",
        options: ["Ловля рыбы", "Мошенничество для кражи данных", "Майнинг криптовалют", "Торговая стратегия"],
        correctAnswer: "Мошенничество для кражи данных",
        category: QuizCategory.security,
        difficulty: DifficultyLevel.easy,
        explanation: "Фишинг - это мошенническая попытка украсть личные данные или приватные ключи пользователей.",
      ),
      
      // Altcoins вопросы
      QuizQuestion(
        id: 31,
        question: "Что означает термин 'альткоин'?",
        options: ["Альтернативная валюта", "Криптовалюта, отличная от Bitcoin", "Цифровая монета", "Токен на блокчейне"],
        correctAnswer: "Криптовалюта, отличная от Bitcoin",
        category: QuizCategory.altcoins,
        difficulty: DifficultyLevel.easy,
        explanation: "Альткоин — это любая криптовалюта, кроме Bitcoin.",
      ),
      QuizQuestion(
        id: 32,
        question: "Какая криптовалюта известна как 'серебро' к 'золоту' Bitcoin?",
        options: ["Ethereum", "Litecoin", "Ripple", "Cardano"],
        correctAnswer: "Litecoin",
        category: QuizCategory.altcoins,
        difficulty: DifficultyLevel.medium,
        explanation: "Litecoin часто называют 'цифровым серебром' в отличие от Bitcoin как 'цифрового золота'.",
      ),
      QuizQuestion(
        id: 33,
        question: "Какая криптовалюта использует алгоритм консенсуса Ouroboros?",
        options: ["Cardano", "Polkadot", "Solana", "Avalanche"],
        correctAnswer: "Cardano",
        category: QuizCategory.altcoins,
        difficulty: DifficultyLevel.hard,
        explanation: "Cardano использует алгоритм консенсуса Ouroboros, основанный на Proof of Stake.",
      ),
      QuizQuestion(
        id: 34,
        question: "Что такое стейблкоин?",
        options: ["Стабильная криптовалюта", "Новый Bitcoin", "Токен для игр", "Валюта банков"],
        correctAnswer: "Стабильная криптовалюта",
        category: QuizCategory.altcoins,
        difficulty: DifficultyLevel.easy,
        explanation: "Стейблкоин - это криптовалюта, стоимость которой привязана к стабильному активу.",
      ),
      QuizQuestion(
        id: 35,
        question: "Какая криптовалюта позиционируется как 'Ethereum killer'?",
        options: ["Solana", "Cardano", "Polkadot", "Все перечисленные"],
        correctAnswer: "Все перечисленные",
        category: QuizCategory.altcoins,
        difficulty: DifficultyLevel.medium,
        explanation: "Множество проектов позиционируют себя как альтернатива Ethereum с лучшими характеристиками.",
      ),
      
      // General вопросы
      QuizQuestion(
        id: 36,
        question: "Что такое криптография?",
        options: ["Наука о шифровании", "Изучение криптовалют", "Торговля на бирже", "Майнинг монет"],
        correctAnswer: "Наука о шифровании",
        category: QuizCategory.general,
        difficulty: DifficultyLevel.easy,
        explanation: "Криптография - это наука о методах обеспечения конфиденциальности и аутентичности информации.",
      ),
      QuizQuestion(
        id: 37,
        question: "Что означает термин 'дурацкое золото'?",
        options: ["Bitcoin", "Ethereum", "Litecoin", "Dogecoin"],
        correctAnswer: "Dogecoin",
        category: QuizCategory.general,
        difficulty: DifficultyLevel.easy,
        explanation: "Dogecoin часто называют 'дурацким золотом' из-за его мемного происхождения.",
      ),
      QuizQuestion(
        id: 38,
        question: "Что такое ICO?",
        options: ["Initial Coin Offering", "International Crypto Organization", "Internet Coin Operation", "Investment Crypto Option"],
        correctAnswer: "Initial Coin Offering",
        category: QuizCategory.general,
        difficulty: DifficultyLevel.medium,
        explanation: "ICO - это способ привлечения средств путем выпуска новых криптовалютных токенов.",
      ),
      QuizQuestion(
        id: 39,
        question: "Что такое airdrop?",
        options: ["Падение цены", "Бесплатная раздача токенов", "Новая биржа", "Способ майнинга"],
        correctAnswer: "Бесплатная раздача токенов",
        category: QuizCategory.general,
        difficulty: DifficultyLevel.easy,
        explanation: "Airdrop - это бесплатная раздача токенов пользователям для продвижения проекта.",
      ),
      QuizQuestion(
        id: 40,
        question: "Что означает 'FOMO' в крипто-трейдинге?",
        options: ["Fear Of Missing Out", "First Order Market Operation", "Financial Options Money Order", "Future Oriented Market Outlook"],
        correctAnswer: "Fear Of Missing Out",
        category: QuizCategory.general,
        difficulty: DifficultyLevel.easy,
        explanation: "FOMO - это страх упустить выгодную возможность, часто приводящий к импульсивным решениям.",
      ),
      
      // Technical вопросы
      QuizQuestion(
        id: 41,
        question: "Что такое Merkle Tree?",
        options: ["Дерево решений", "Структура данных для хеширования", "Алгоритм консенсуса", "Протокол сети"],
        correctAnswer: "Структура данных для хеширования",
        category: QuizCategory.technical,
        difficulty: DifficultyLevel.hard,
        explanation: "Merkle Tree - это бинарное дерево хешей, используемое для эффективной верификации данных в блоке.",
      ),
      QuizQuestion(
        id: 42,
        question: "Что такое nonce в майнинге?",
        options: ["Случайное число", "Адрес кошелька", "Размер блока", "Время создания блока"],
        correctAnswer: "Случайное число",
        category: QuizCategory.technical,
        difficulty: DifficultyLevel.hard,
        explanation: "Nonce - это число, которое майнеры изменяют для поиска правильного хеша блока.",
      ),
      QuizQuestion(
        id: 43,
        question: "Что такое UTXO?",
        options: ["Unspent Transaction Output", "Universal Token Exchange Operation", "Unified Trading Exchange Order", "Updated Transaction External Output"],
        correctAnswer: "Unspent Transaction Output",
        category: QuizCategory.technical,
        difficulty: DifficultyLevel.expert,
        explanation: "UTXO - это неизрасходованный выход транзакции, основная модель Bitcoin для отслеживания балансов.",
      ),
      QuizQuestion(
        id: 44,
        question: "Что такое Lightning Network?",
        options: ["Быстрая сеть", "Решение масштабирования для Bitcoin", "Новая криптовалюта", "Протокол майнинга"],
        correctAnswer: "Решение масштабирования для Bitcoin",
        category: QuizCategory.technical,
        difficulty: DifficultyLevel.hard,
        explanation: "Lightning Network - это сеть второго уровня для быстрых и дешевых Bitcoin-транзакций.",
      ),
      QuizQuestion(
        id: 45,
        question: "Что такое sharding в блокчейне?",
        options: ["Разделение сети на части", "Новый консенсус", "Тип кошелька", "Метод шифрования"],
        correctAnswer: "Разделение сети на части",
        category: QuizCategory.technical,
        difficulty: DifficultyLevel.expert,
        explanation: "Sharding - это метод разделения блокчейна на части для повышения производительности.",
      ),
      
      // Economics вопросы
      QuizQuestion(
        id: 46,
        question: "Что такое инфляция в криптовалютах?",
        options: ["Рост цены", "Увеличение предложения", "Рост спроса", "Новые биржи"],
        correctAnswer: "Увеличение предложения",
        category: QuizCategory.economics,
        difficulty: DifficultyLevel.medium,
        explanation: "Инфляция в криптовалютах означает увеличение общего предложения монет в обращении.",
      ),
      QuizQuestion(
        id: 47,
        question: "Что такое дефляция в экономике криптовалют?",
        options: ["Падение цены", "Уменьшение предложения", "Снижение спроса", "Закрытие бирж"],
        correctAnswer: "Уменьшение предложения",
        category: QuizCategory.economics,
        difficulty: DifficultyLevel.medium,
        explanation: "Дефляция означает уменьшение общего предложения токенов, что может привести к росту цены.",
      ),
      QuizQuestion(
        id: 48,
        question: "Что такое market cap (рыночная капитализация)?",
        options: ["Цена одной монеты", "Общая стоимость всех монет", "Объем торгов", "Количество держателей"],
        correctAnswer: "Общая стоимость всех монет",
        category: QuizCategory.economics,
        difficulty: DifficultyLevel.easy,
        explanation: "Market cap = цена монеты × общее количество монет в обращении.",
      ),
      QuizQuestion(
        id: 49,
        question: "Что влияет на цену криптовалюты?",
        options: ["Спрос и предложение", "Новости и события", "Технические факторы", "Все перечисленное"],
        correctAnswer: "Все перечисленное",
        category: QuizCategory.economics,
        difficulty: DifficultyLevel.easy,
        explanation: "Цена криптовалюты зависит от множества факторов включая спрос/предложение, новости и технические аспекты.",
      ),
      QuizQuestion(
        id: 50,
        question: "Что такое tokenomics?",
        options: ["Экономика токенов", "Технология токенов", "Торговля токенами", "Создание токенов"],
        correctAnswer: "Экономика токенов",
        category: QuizCategory.economics,
        difficulty: DifficultyLevel.medium,
        explanation: "Tokenomics изучает экономические аспекты криптовалютных токенов: эмиссию, распределение, использование.",
      ),
    ];
  }
} 