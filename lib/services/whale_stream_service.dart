import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;

/// Service that connects to the Python Telegram → WebSocket relay (whale alerts)
/// and broadcasts every raw text message it receives.
///
/// By default it connects to ws://<host>:8765 where <host> is:
///   • ******** when running on Android emulator
///   • localhost (127.0.0.1) for all other platforms
///
/// You can override the host by passing [overrideHost].
class WhaleStreamService {
  WhaleStreamService({String? overrideHost})
      : _wsUrl = _buildWsUrl(overrideHost);

  static String _buildWsUrl(String? overrideHost) {
    final host = overrideHost ?? (defaultTargetPlatform == TargetPlatform.android
        ? '********'
        : 'localhost');
    return 'ws://$host:8765';
  }

  final String _wsUrl;
  WebSocketChannel? _channel;
  late final StreamController<String> _controller =
      StreamController<String>.broadcast(onListen: _ensureConnected);

  bool _manuallyClosed = false;

  /// Public stream of all incoming messages.
  Stream<String> get stream => _controller.stream;

  void _ensureConnected() {
    if (_channel != null) return; // already connected

    try {
      debugPrint('[WhaleStream] Connecting to $_wsUrl');
      _channel = WebSocketChannel.connect(Uri.parse(_wsUrl));

      _channel!.stream.listen(
        (data) {
          if (!_controller.isClosed) {
            _controller.add(data.toString());
          }
        },
        onDone: _handleDisconnect,
        onError: (error) {
          debugPrint('[WhaleStream] WebSocket error: $error');
          _handleDisconnect();
        },
      );
    } catch (e) {
      debugPrint('[WhaleStream] Connection failed: $e');
      _scheduleReconnect();
    }
  }

  void _handleDisconnect() {
    if (_manuallyClosed) return;
    debugPrint('[WhaleStream] Disconnected – scheduling reconnect');
    _channel = null;
    _scheduleReconnect();
  }

  void _scheduleReconnect() {
    Future.delayed(const Duration(seconds: 3), () {
      if (!_manuallyClosed) {
        _ensureConnected();
      }
    });
  }

  /// Close WebSocket and stream controller.
  void dispose() {
    _manuallyClosed = true;
    _channel?.sink.close(status.normalClosure);
    _controller.close();
  }
} 