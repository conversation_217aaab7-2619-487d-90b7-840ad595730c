import 'package:flutter/material.dart';

/// Централизованная палитра для строгого нейтрального стиля карточек и ярких тегов
class NewsCardColors {
  // --- Единый чёрный фон и бордер для всех карточек ---
  static const Color cardBackground = Color(0xFF000000);
  static const Color cardBorder = Color.fromRGBO(255, 255, 255, 0.10);
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Color.fromRGBO(0, 0, 0, 0.4),
      blurRadius: 32,
      offset: Offset(0, 8),
    ),
    BoxShadow(
      color: Color.fromRGBO(255, 255, 255, 0.08),
      blurRadius: 1,
      offset: Offset(0, 1),
      spreadRadius: 0,
    ),
  ];

  // --- Текст ---
  static const Color title = Color(0xFFFFFFFF);
  static const Color description = Color(0xFFD1D5DB);
  static const Color meta = Color(0xFF9CA3AF);
  static const Color source = Color(0xFFA1A1AA);

  // --- Теги (яркие, индивидуальные) ---
  static const tagStyle = TextStyle(
    fontWeight: FontWeight.w600,
    fontSize: 11,
    letterSpacing: 0.5,
    fontFamily: 'Inter',
    textBaseline: TextBaseline.alphabetic,
  );

  static const Color tagBTCBg = Color.fromRGBO(247, 147, 26, 0.2);
  static const Color tagBTCBorder = Color.fromRGBO(247, 147, 26, 0.4);
  static const Color tagBTCText = Color(0xFFF7931A);

  static const Color tagETHBg = Color.fromRGBO(98, 126, 234, 0.2);
  static const Color tagETHBorder = Color.fromRGBO(98, 126, 234, 0.4);
  static const Color tagETHText = Color(0xFF627EEA);

  static const Color tagDeFiBg = Color.fromRGBO(139, 92, 246, 0.2);
  static const Color tagDeFiBorder = Color.fromRGBO(139, 92, 246, 0.4);
  static const Color tagDeFiText = Color(0xFF8B5CF6);

  static const Color tagAIBg = Color.fromRGBO(6, 182, 212, 0.2);
  static const Color tagAIBorder = Color.fromRGBO(6, 182, 212, 0.4);
  static const Color tagAIText = Color(0xFF06B6D4);

  static const Color tagStocksBg = Color.fromRGBO(34, 197, 94, 0.2);
  static const Color tagStocksBorder = Color.fromRGBO(34, 197, 94, 0.4);
  static const Color tagStocksText = Color(0xFF22C55E);

  static const Color tagSP500Bg = Color.fromRGBO(21, 128, 61, 0.2);
  static const Color tagSP500Border = Color.fromRGBO(21, 128, 61, 0.4);
  static const Color tagSP500Text = Color(0xFF15803D);

  static const Color tagMacroBg = Color.fromRGBO(107, 114, 128, 0.2);
  static const Color tagMacroBorder = Color.fromRGBO(107, 114, 128, 0.4);
  static const Color tagMacroText = Color(0xFF6B7280);

  static const Gradient tagCryptoBg = LinearGradient(
    colors: [Color.fromRGBO(59, 130, 246, 0.2), Color.fromRGBO(139, 92, 246, 0.2)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  static const Color tagCryptoBorder = Color.fromRGBO(99, 102, 241, 0.4);
  static const Color tagCryptoText = Color(0xFF6366F1);

  static const Color tagNasdaqBg = Color.fromRGBO(59, 130, 246, 0.2);
  static const Color tagNasdaqBorder = Color.fromRGBO(59, 130, 246, 0.4);
  static const Color tagNasdaqText = Color(0xFF3B82F6);

  static const Color tagCongressBg = Color.fromRGBO(220, 38, 38, 0.2);
  static const Color tagCongressBorder = Color.fromRGBO(220, 38, 38, 0.4);
  static const Color tagCongressText = Color(0xFFDC2626);

  // --- Дефолтный тег ---
  static const Color tagDefaultBg = Color.fromRGBO(156, 163, 175, 0.15);
  static const Color tagDefaultBorder = Color.fromRGBO(156, 163, 175, 0.3);
  static const Color tagDefaultText = Color(0xFF9CA3AF);

  // --- Тренд-индикаторы (только цвет иконки) ---
  static const Color trendUp = Color(0xFF22C55E);
  static const Color trendDown = Color(0xFFEF4444);
  static const Color trendNeutral = Color(0xFF6B7280);
} 