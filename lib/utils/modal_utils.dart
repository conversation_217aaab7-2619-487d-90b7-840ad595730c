import 'package:flutter/material.dart';
import '../models/news_item.dart';
import '../models/news_detail.dart';
import '../widgets/news_detail_modal.dart';

void showNewsDetailModal({
  required BuildContext context,
  required NewsItem newsItem,
}) async {
  // Show loading dialog first
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    },
  );

  try {
    // Generate news detail with async sentiment analysis
    final newsDetail = await NewsDetailData.fromNewsItem(newsItem);

    // Close loading dialog
    if (context.mounted) {
      Navigator.of(context).pop();
    }

    // Show the actual news detail modal
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return NewsDetailModal(
            newsDetail: newsDetail,
            onClose: () => Navigator.of(context).pop(),
          );
        },
      );
    }
  } catch (e) {
    // Close loading dialog
    if (context.mounted) {
      Navigator.of(context).pop();
    }

    // Fallback to sync method if async fails
    if (context.mounted) {
      final newsDetail = NewsDetailData.fromNewsItemSync(newsItem);
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return NewsDetailModal(
            newsDetail: newsDetail,
            onClose: () => Navigator.of(context).pop(),
          );
        },
      );
    }
  }
}