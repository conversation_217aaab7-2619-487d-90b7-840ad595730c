import 'package:finance_ai/models/news_item.dart';
import 'package:finance_ai/utils/sentiment_analyzer.dart';
import 'package:finance_ai/models/sentiment_types.dart';

class NewsContentGenerator {
  static String generateFullContent(NewsItem newsItem) {
    // ПРАВИЛЬНЫЙ приоритет ПОЛНОГО контента: rewrittenContent (AI полный) > content (оригинал) > description
    // summary НЕ используется здесь - это краткая выжимка, а не полный контент!
    if (newsItem.rewrittenContent != null && newsItem.rewrittenContent!.trim().isNotEmpty) {
      return newsItem.rewrittenContent!; // AI переписанный ПОЛНЫЙ контент
    }
    if (newsItem.content != null && newsItem.content!.trim().isNotEmpty) {
      return newsItem.content!; // Оригинальный полный контент
    }
    return newsItem.description; // Fallback к описанию
  }

  static List<String> generateRelatedLinks(NewsItem newsItem) {
    // Пример генерации связанных ссылок
    return [
      'https://coindesk.com/related-article-1',
      'https://cointelegraph.com/related-article-2',
    ];
  }

  static Map<String, dynamic> generateMetadata(NewsItem newsItem) {
    return {
      'readingTime': _calculateReadingTime(newsItem.description),
      'category': _categorizeNews(newsItem),
      'priority': _calculatePriority(newsItem),
      'keywords': _extractKeywords(newsItem),
    };
  }

  static String _generatePositiveContext(NewsItem newsItem) => 'This news is likely to have a positive impact on the market.';
  static String _generateNegativeContext(NewsItem newsItem) => 'This news may cause concern or negative sentiment among investors.';
  static String _generateNeutralContext(NewsItem newsItem) => 'This news is informational and does not strongly affect market sentiment.';

  static int _calculateReadingTime(String text) => (text.split(' ').length / 200).ceil();
  static String _categorizeNews(NewsItem newsItem) => newsItem.category.toString();
  static int _calculatePriority(NewsItem newsItem) => 1;
  static List<String> _extractKeywords(NewsItem newsItem) => newsItem.tags;
} 