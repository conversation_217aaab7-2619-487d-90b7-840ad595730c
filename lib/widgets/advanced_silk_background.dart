import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui' as ui;

class AdvancedSilkBackground extends StatefulWidget {
  final double speed;
  final double scale;
  final Color color;
  final double noiseIntensity;
  final double rotation;

  const AdvancedSilkBackground({
    super.key,
    this.speed = 1.8,
    this.scale = 0.8,
    this.color = const Color(0xFF1C1C1C),
    this.noiseIntensity = 0.5,
    this.rotation = 0.48,
  });

  @override
  State<AdvancedSilkBackground> createState() => _AdvancedSilkBackgroundState();
}

class _AdvancedSilkBackgroundState extends State<AdvancedSilkBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _timeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: (1000 / widget.speed * 10).round()),
      vsync: this,
    )..repeat();
    
    _timeAnimation = Tween<double>(
      begin: 0.0,
      end: 100.0, // Большой диапазон для непрерывной анимации
    ).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _timeAnimation,
      builder: (context, child) {
        return CustomPaint(
          painter: SilkShaderPainter(
            time: _timeAnimation.value,
            speed: widget.speed,
            scale: widget.scale,
            color: widget.color,
            noiseIntensity: widget.noiseIntensity,
            rotation: widget.rotation,
          ),
          size: Size.infinite,
        );
      },
    );
  }
}

class SilkShaderPainter extends CustomPainter {
  final double time;
  final double speed;
  final double scale;
  final Color color;
  final double noiseIntensity;
  final double rotation;

  SilkShaderPainter({
    required this.time,
    required this.speed,
    required this.scale,
    required this.color,
    required this.noiseIntensity,
    required this.rotation,
  });

  // Точная копия noise функции из оригинального шейдера
  double noise(double x, double y) {
    const double e = 2.71828182845904523536;
    final double gX = e * math.sin(e * x);
    final double gY = e * math.sin(e * y);
    return (gX * gY * (1.0 + x)).abs() % 1.0;
  }

  // Поворот UV координат как в оригинальном шейдере
  List<double> rotateUV(double u, double v, double angle) {
    final double c = math.cos(angle);
    final double s = math.sin(angle);
    return [
      u * c - v * s,
      u * s + v * c,
    ];
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    
    // Создаем изображение попиксельно для точного воспроизведения шейдера
    final int width = size.width.toInt();
    final int height = size.height.toInt();
    
    if (width <= 0 || height <= 0) return;
    
    final Uint8List pixels = Uint8List(width * height * 4);
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final int index = (y * width + x) * 4;
        
        // Нормализованные UV координаты
        final double vUvX = x / width;
        final double vUvY = y / height;
        
        // Применяем rotation и scale как в оригинальном шейдере
        final List<double> rotatedUV = rotateUV(vUvX * scale, vUvY * scale, rotation);
        final double texX = rotatedUV[0] * scale;
        final double texY = rotatedUV[1] * scale;
        
        final double tOffset = speed * time * 0.01;
        
        // Точная копия волнового паттерна из оригинального шейдера
        final double waveY = texY + 0.03 * math.sin(8.0 * texX - tOffset);
        
        final double pattern = 0.6 + 0.4 * math.sin(
          5.0 * (texX + waveY + 
                 math.cos(3.0 * texX + 5.0 * waveY) +
                 0.02 * tOffset) +
          math.sin(20.0 * (texX + waveY - 0.1 * tOffset))
        );
        
        // Добавляем шум как в оригинале
        final double rnd = noise(x.toDouble(), y.toDouble());
        final double finalPattern = pattern - rnd / 15.0 * noiseIntensity;
        final double clampedPattern = finalPattern.clamp(0.0, 1.0);
        
        // Применяем цвет
        final int r = (color.red * clampedPattern).round().clamp(0, 255);
        final int g = (color.green * clampedPattern).round().clamp(0, 255);
        final int b = (color.blue * clampedPattern).round().clamp(0, 255);
        final int a = (255 * clampedPattern).round().clamp(0, 255);
        
        pixels[index] = r;
        pixels[index + 1] = g;
        pixels[index + 2] = b;
        pixels[index + 3] = a;
      }
    }
    
    // Создаем изображение из пикселей
    ui.decodeImageFromPixels(
      pixels,
      width,
      height,
      ui.PixelFormat.rgba8888,
      (ui.Image image) {
        paint.isAntiAlias = false;
        canvas.drawImage(image, Offset.zero, paint);
      },
    );
  }

  @override
  bool shouldRepaint(covariant SilkShaderPainter oldDelegate) {
    return oldDelegate.time != time ||
           oldDelegate.speed != speed ||
           oldDelegate.scale != scale ||
           oldDelegate.color != color ||
           oldDelegate.noiseIntensity != noiseIntensity ||
           oldDelegate.rotation != rotation;
  }
} 