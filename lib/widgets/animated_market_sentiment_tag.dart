import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../models/news_item.dart';
import 'animated_downward_chart_indicator.dart';
import 'animated_upward_chart_indicator.dart';
import 'package:finance_ai/models/sentiment_types.dart';

/// Анимированный индикатор настроения рынка
/// Показывает настроение (позитивное, негативное, нейтральное) с анимацией
/// Анимация запускается каждые 15 секунд
class AnimatedMarketSentimentTag extends StatefulWidget {
  final SentimentType sentiment;
  final String text;
  final bool small;

  const AnimatedMarketSentimentTag({
    Key? key,
    required this.sentiment,
    required this.text,
    this.small = false,
  }) : super(key: key);

  @override
  _AnimatedMarketSentimentTagState createState() => _AnimatedMarketSentimentTagState();
}

class _AnimatedMarketSentimentTagState extends State<AnimatedMarketSentimentTag> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _waveAnimation;
  Timer? _animationTimer;

  @override
  void initState() {
    super.initState();

    // Настройка контроллера анимации
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Настройка анимации волны
    _waveAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    // Настройка таймера для запуска анимации каждые 15 секунд
    _animationTimer = Timer.periodic(const Duration(seconds: 15), (_) {
      if (mounted && (widget.sentiment == SentimentType.negative ||
                     widget.sentiment == SentimentType.positive)) {
        _controller.forward(from: 0.0);
      }
    });

    // Запускаем анимацию сразу для демонстрации
    if (widget.sentiment == SentimentType.negative ||
        widget.sentiment == SentimentType.positive) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _controller.forward(from: 0.0);
        }
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Получаем цвет в зависимости от типа настроения
    final Color textColor = _getSentimentColor();

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        // Применяем анимацию для негативных и положительных новостей
        final isNegative = widget.sentiment == SentimentType.negative;
        final isPositive = widget.sentiment == SentimentType.positive;
        final shouldAnimate = isNegative || isPositive;

        return Container(
          padding: EdgeInsets.symmetric(
            horizontal: widget.small ? 4.0 : 6.0,
            vertical: widget.small ? 2.0 : 3.0,
          ),
          decoration: BoxDecoration(
            // Прозрачный фон
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(4.0),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Для негативных новостей показываем график падения
              if (isNegative)
                AnimatedDownwardChartIndicator(
                  width: widget.small ? 25.0 : 30.0,
                  height: widget.small ? 25.0 : 30.0,
                ),
              
              // Для положительных новостей показываем график роста
              if (isPositive)
                AnimatedUpwardChartIndicator(
                  width: widget.small ? 25.0 : 30.0,
                  height: widget.small ? 25.0 : 30.0,
                ),

              // Для нейтральных новостей показываем текст с иконкой
              if (!shouldAnimate)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.arrow_forward,
                      color: textColor,
                      size: widget.small ? 10.0 : 12.0,
                    ),
                    const SizedBox(width: 4.0),
                    Text(
                      widget.text,
                      style: TextStyle(
                        color: textColor,
                        fontSize: widget.small ? 10.0 : 12.0,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }

  Color _getSentimentColor() {
    switch (widget.sentiment) {
      case SentimentType.negative:
        // Красный цвет, как на фотографии
        return const Color(0xFFFF4D4F);
      case SentimentType.positive:
        return const Color(0xFF52C41A);
      case SentimentType.neutral:
      default:
        return const Color(0xFF8C8C8C);
    }
  }
}

// Удалили класс WaveLinePainter, так как теперь используем AnimatedDownwardChart
