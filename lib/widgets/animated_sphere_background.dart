import 'package:flutter/material.dart';
import 'dart:math' as math;

class AnimatedSphereBackground extends StatefulWidget {
  const AnimatedSphereBackground({super.key});

  @override
  State<AnimatedSphereBackground> createState() => _AnimatedSphereBackgroundState();
}

class _AnimatedSphereBackgroundState extends State<AnimatedSphereBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 30), // Еще более медленная анимация
      vsync: this,
    )..repeat();
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: SpherePainter(
            animationValue: _animation.value,
          ),
          size: Size.infinite,
        );
      },
    );
  }
}

class SpherePainter extends CustomPainter {
  final double animationValue;

  SpherePainter({required this.animationValue});

  @override
  void paint(Canvas canvas, Size size) {
    // Темный базовый фон как на изображении
    final backgroundPaint = Paint()
      ..color = const Color(0xFF0A0A0A);
    canvas.drawRect(Offset.zero & size, backgroundPaint);

    // Добавляем очень тонкий градиентный оверлей для глубины
    final overlayGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        const Color(0xFF121212).withOpacity(0.3),
        Colors.transparent,
        const Color(0xFF0F0F0F).withOpacity(0.2),
        Colors.transparent,
      ],
    );
    
    final overlayPaint = Paint()
      ..shader = overlayGradient.createShader(Offset.zero & size);
    canvas.drawRect(Offset.zero & size, overlayPaint);

    // Создаем множество шаров разного размера
    _drawAnimatedSpheres(canvas, size);
  }

  void _drawAnimatedSpheres(Canvas canvas, Size size) {
    final random = math.Random(42); // Фиксированное семя для воспроизводимости
    
    // Создаем список шаров с различными параметрами
    final spheres = <SphereData>[];
    
    // Генерируем 30-35 шаров разного размера для максимального соответствия изображению
    for (int i = 0; i < 32; i++) {
      // Создаем несколько крупных шаров и множество мелких, как на изображении
      final isLargeSphere = i < 8; // Первые 8 - крупные
      final isMediumSphere = i >= 8 && i < 18; // Следующие 10 - средние
      // Остальные - мелкие
      
      double baseRadius;
      double maxOpacity;
      
      if (isLargeSphere) {
        baseRadius = 80 + random.nextDouble() * 150; // Крупные: 80-230px
        maxOpacity = 0.12 + random.nextDouble() * 0.18; // Более заметные
      } else if (isMediumSphere) {
        baseRadius = 40 + random.nextDouble() * 80; // Средние: 40-120px  
        maxOpacity = 0.08 + random.nextDouble() * 0.12; // Умеренно заметные
      } else {
        baseRadius = 15 + random.nextDouble() * 40; // Мелкие: 15-55px
        maxOpacity = 0.04 + random.nextDouble() * 0.08; // Едва заметные
      }
      
      spheres.add(SphereData(
        x: random.nextDouble(),
        y: random.nextDouble(),
        baseRadius: baseRadius,
        animationPhase: random.nextDouble() * 2 * math.pi,
        animationSpeed: 0.2 + random.nextDouble() * 0.6, // Очень медленно: от 0.2 до 0.8
        maxOpacity: maxOpacity,
        color: _generateSphereColor(random),
      ));
    }

    // Рисуем каждый шар
    for (final sphere in spheres) {
      _drawSphere(canvas, size, sphere);
    }
  }

  Color _generateSphereColor(math.Random random) {
    // Создаем цвета в тонах, соответствующих изображению
    final colors = [
      const Color(0xFF1A1A1A), // Очень темно-серый
      const Color(0xFF252525), // Темно-серый
      const Color(0xFF1A1A22), // Темно-синеватый
      const Color(0xFF22221A), // Темно-желтоватый
      const Color(0xFF1A2222), // Темно-зеленоватый
      const Color(0xFF2A2A2A), // Серый
      const Color(0xFF1F1F1F), // Почти черный
      const Color(0xFF2F2F2F), // Светло-серый
    ];
    return colors[random.nextInt(colors.length)];
  }

  void _drawSphere(Canvas canvas, Size size, SphereData sphere) {
    final centerX = sphere.x * size.width;
    final centerY = sphere.y * size.height;
    
    // Вычисляем анимацию "впадания" и "вылазения"
    final animationPhase = (animationValue * sphere.animationSpeed + sphere.animationPhase) % (2 * math.pi);
    
    // Используем sin для создания эффекта впадания/вылазения
    // От -1 до 1, где -1 = полностью впал, 1 = полностью вылез
    final depthFactor = math.sin(animationPhase);
    
    // Улучшенная функция видимости с более плавным переходом
    final rawVisibility = (depthFactor + 1) / 2; // От 0 до 1
    final smoothedVisibility = rawVisibility * rawVisibility * (3 - 2 * rawVisibility); // Smooth step
    final visibility = smoothedVisibility * sphere.maxOpacity;
    
    // Вычисляем размер шара с более выраженным эффектом масштабирования
    final sizeMultiplier = 0.1 + 0.9 * smoothedVisibility; // От 0.1 до 1.0
    final currentRadius = sphere.baseRadius * sizeMultiplier;
    
    if (visibility > 0.01) {
      // Создаем внешнее размытие для создания эффекта глубины  
      if (visibility > 0.02) {
        final outerGlow = Paint()
          ..color = sphere.color.withOpacity(visibility * 0.15)
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, currentRadius * 0.4);
        
        canvas.drawCircle(
          Offset(centerX, centerY),
          currentRadius * 1.3,
          outerGlow,
        );
      }
      
      // Создаем радиальный градиент для шара
      final gradient = RadialGradient(
        center: Alignment.center,
        radius: 1.0,
        colors: [
          sphere.color.withOpacity(visibility * 0.8),
          sphere.color.withOpacity(visibility * 0.6),
          sphere.color.withOpacity(visibility * 0.35),
          sphere.color.withOpacity(visibility * 0.15),
          sphere.color.withOpacity(visibility * 0.05),
          Colors.transparent,
        ],
        stops: const [0.0, 0.25, 0.5, 0.7, 0.85, 1.0],
      );
      
      final paint = Paint()
        ..shader = gradient.createShader(
          Rect.fromCircle(
            center: Offset(centerX, centerY),
            radius: currentRadius,
          ),
        )
        ..blendMode = BlendMode.screen;
      
      canvas.drawCircle(
        Offset(centerX, centerY),
        currentRadius,
        paint,
      );
      
      // Добавляем центральное ядро для большей яркости
      if (visibility > 0.1) {
        final corePaint = Paint()
          ..color = sphere.color.withOpacity(visibility * 0.4)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4.0);
        
        canvas.drawCircle(
          Offset(centerX, centerY),
          currentRadius * 0.2,
          corePaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant SpherePainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}

class SphereData {
  final double x; // Позиция X (0-1)
  final double y; // Позиция Y (0-1)
  final double baseRadius; // Базовый радиус
  final double animationPhase; // Фазовый сдвиг анимации
  final double animationSpeed; // Скорость анимации
  final double maxOpacity; // Максимальная непрозрачность
  final Color color; // Цвет шара

  SphereData({
    required this.x,
    required this.y,
    required this.baseRadius,
    required this.animationPhase,
    required this.animationSpeed,
    required this.maxOpacity,
    required this.color,
  });
} 