import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../utils/device_type.dart';

class AppBottomNavigation extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final BoxDecoration? backgroundDecoration;

  const AppBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
    this.backgroundDecoration,
  });

  @override
  State<AppBottomNavigation> createState() => _AppBottomNavigationState();
}

class _AppBottomNavigationState extends State<AppBottomNavigation>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _floatingController;
  late AnimationController _rippleController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _floatingAnimation;
  late Animation<double> _rippleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _floatingController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _floatingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));

    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeOut,
    ));

    _floatingController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _floatingController.dispose();
    _rippleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = DeviceUtils.isDesktop(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: isDesktop ? 60 : 72,
      decoration: widget.backgroundDecoration ?? BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          // Основная тень - уменьшена для лучшей интеграции с фоном
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.08), // Уменьшена с 0.4/0.15
            blurRadius: 30, // Уменьшена с 40
            spreadRadius: 0,
            offset: const Offset(0, 6), // Уменьшена с 8
          ),
          // Дополнительная ambient тень - уменьшена
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.1 : 0.025), // Уменьшена с 0.2/0.05
            blurRadius: 15, // Уменьшена с 20
            spreadRadius: -5,
            offset: const Offset(0, 3), // Уменьшена с 4
          ),
        ],
      ),
      child: widget.backgroundDecoration != null 
          ? Container(
              decoration: widget.backgroundDecoration,
              child: isDesktop
                  ? _buildDesktopNavigation(context)
                  : _buildMobileNavigation(context),
            )
          : ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 40, sigmaY: 40),
                child: Container(
                  decoration: BoxDecoration(
                    // Glass morphism с увеличенной прозрачностью для всех страниц
                    color: isDarkMode 
                        ? const Color(0xFF1C1C1E).withOpacity(0.08)
                        : const Color(0xFFF2F2F7).withOpacity(0.08),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: isDarkMode
                          ? [
                              Colors.white.withOpacity(0.02),
                              Colors.white.withOpacity(0.01),
                            ]
                          : [
                              Colors.white.withOpacity(0.1),
                              Colors.white.withOpacity(0.05),
                            ],
                    ),
                    border: Border.all(
                      color: isDarkMode
                          ? Colors.white.withOpacity(0.03)
                          : Colors.black.withOpacity(0.015),
                      width: 0.5,
                    ),
                  ),
                  child: isDesktop
                      ? _buildDesktopNavigation(context)
                      : _buildMobileNavigation(context),
                ),
              ),
            ),
    );
  }

  Widget _buildMobileNavigation(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildPremiumNavItem(
            context,
            0,
            CupertinoIcons.news_solid,
            CupertinoIcons.news,
            'News',
          ),
          _buildPremiumNavItem(
            context,
            1,
            CupertinoIcons.chart_bar_circle_fill,
            CupertinoIcons.chart_bar_circle,
            'Charts',
          ),
          _buildPremiumSinusoidNavItem(context, 2),
          _buildPremiumNavItem(
            context,
            3,
            CupertinoIcons.book_solid,
            CupertinoIcons.book,
            'Learn',
          ),
          _buildPremiumNavItem(
            context,
            4,
            CupertinoIcons.person_solid,
            CupertinoIcons.person,
            'Profile',
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumNavItem(
    BuildContext context,
    int index,
    IconData activeIcon,
    IconData inactiveIcon,
    String label,
  ) {
    final isSelected = widget.currentIndex == index;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onTap(index);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeOutCubic,
        width: 64,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 6),
            // Кнопка с эффектом тиснения и glass morphism
            AnimatedContainer(
              duration: const Duration(milliseconds: 350),
              curve: Curves.easeOutCubic,
              width: isSelected ? 64 : 44,
              height: isSelected ? 36 : 44,
              decoration: BoxDecoration(
                // Эффект тиснения (втиснутая кнопка) с красивым градиентом
                color: isSelected 
                    ? null
                    : Colors.transparent,
                gradient: isSelected ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF2D1B3D).withOpacity(0.95), // Very dark purple
                    const Color(0xFF1A0E23).withOpacity(0.98), // Almost black purple
                    const Color(0xFF0F0612).withOpacity(1.0), // Deep dark purple-black
                  ],
                  stops: const [0.0, 0.6, 1.0],
                ) : null,
                borderRadius: BorderRadius.circular(isSelected ? 18 : 22),
                border: isSelected
                    ? Border.all(
                        color: const Color(0xFF4A2C5E).withOpacity(0.9),
                        width: 2.5, // Увеличена толщина границы
                      )
                    : null,
                boxShadow: isSelected ? [
                  // Внешний glow эффект
                  BoxShadow(
                    color: const Color(0xFF4A2C5E).withOpacity(0.5),
                    blurRadius: 15,
                    spreadRadius: 1,
                    offset: const Offset(0, 0),
                  ),
                  // Усиленный эффект тиснения - очень темная тень справа снизу
                  BoxShadow(
                    color: Colors.black.withOpacity(0.95),
                    blurRadius: 12,
                    offset: const Offset(4, 4),
                  ),
                  // Светлая тень слева сверху для эффекта глубины (более контрастная)
                  BoxShadow(
                    color: const Color(0xFF6A4C7A).withOpacity(0.6),
                    blurRadius: 8,
                    offset: const Offset(-3, -3),
                  ),
                  // Дополнительная внутренняя тень для усиления эффекта вдавленности
                  BoxShadow(
                    color: Colors.black.withOpacity(0.8),
                    blurRadius: 16,
                    offset: const Offset(2, 2),
                  ),
                ] : null,
              ),
              child: isSelected ? Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      activeIcon,
                      color: Colors.white.withOpacity(0.98),
                      size: 18,
                    ),
                    if (isSelected && label.length <= 5) ...[
                      const SizedBox(width: 4),
                      Text(
                        label,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.98),
                          fontSize: 11,
                          fontWeight: FontWeight.w800,
                          letterSpacing: 0.2,
                          shadows: [
                            Shadow(
                              color: Colors.black.withOpacity(0.8),
                              offset: const Offset(0, 1),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ) : Center(
                child: Icon(
                  inactiveIcon,
                  color: isDarkMode 
                      ? const Color(0xFF8E8E93)
                      : const Color(0xFF6D6D70),
                  size: 22,
                ),
              ),
            ),
            if (!isSelected) ...[
              const SizedBox(height: 4),
              // Лейбл для неактивных элементов
              Text(
                label,
                style: TextStyle(
                  color: isDarkMode 
                      ? const Color(0xFF8E8E93)
                      : const Color(0xFF6D6D70),
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.1,
                ),
              ),
              const SizedBox(height: 4),
            ] else ...[
              const SizedBox(height: 8),
              // Премиальный индикатор активного элемента
              AnimatedContainer(
                duration: const Duration(milliseconds: 350),
                curve: Curves.easeOutCubic,
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: const Color(0xFF8A2BE2),
                  borderRadius: BorderRadius.circular(2),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF8A2BE2).withOpacity(0.8),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumSinusoidNavItem(BuildContext context, int index) {
    final isSelected = widget.currentIndex == index;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onTap(index);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeOutCubic,
        width: 64,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 6),
            // Sinusoid кнопка с эффектом тиснения
            AnimatedContainer(
              duration: const Duration(milliseconds: 350),
              curve: Curves.easeOutCubic,
              width: isSelected ? 64 : 44,
              height: isSelected ? 36 : 44,
              decoration: BoxDecoration(
                // Эффект тиснения (втиснутая кнопка) с красивым градиентом
                color: isSelected 
                    ? null
                    : Colors.transparent,
                gradient: isSelected ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF2D1B3D).withOpacity(0.95), // Very dark purple
                    const Color(0xFF1A0E23).withOpacity(0.98), // Almost black purple
                    const Color(0xFF0F0612).withOpacity(1.0), // Deep dark purple-black
                  ],
                  stops: const [0.0, 0.6, 1.0],
                ) : null,
                borderRadius: BorderRadius.circular(isSelected ? 18 : 22),
                border: isSelected
                    ? Border.all(
                        color: const Color(0xFF4A2C5E).withOpacity(0.9),
                        width: 2.5, // Увеличена толщина границы
                      )
                    : null,
                boxShadow: isSelected ? [
                  // Внешний glow эффект
                  BoxShadow(
                    color: const Color(0xFF4A2C5E).withOpacity(0.5),
                    blurRadius: 15,
                    spreadRadius: 1,
                    offset: const Offset(0, 0),
                  ),
                  // Усиленный эффект тиснения - очень темная тень справа снизу
                  BoxShadow(
                    color: Colors.black.withOpacity(0.95),
                    blurRadius: 12,
                    offset: const Offset(4, 4),
                  ),
                  // Светлая тень слева сверху для эффекта глубины (более контрастная)
                  BoxShadow(
                    color: const Color(0xFF6A4C7A).withOpacity(0.6),
                    blurRadius: 8,
                    offset: const Offset(-3, -3),
                  ),
                  // Дополнительная внутренняя тень для усиления эффекта вдавленности
                  BoxShadow(
                    color: Colors.black.withOpacity(0.8),
                    blurRadius: 16,
                    offset: const Offset(2, 2),
                  ),
                ] : null,
              ),
              child: isSelected ? Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(
                      'logo/bottom_navigation_bar/Sinusoid.png',
                      width: 18,
                      height: 18,
                      color: Colors.white.withOpacity(0.98),
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          CupertinoIcons.waveform,
                          color: Colors.white.withOpacity(0.98),
                          size: 18,
                        );
                      },
                    ),
                  ],
                ),
              ) : Center(
                child: Image.asset(
                  'logo/bottom_navigation_bar/Sinusoid.png',
                  width: 22,
                  height: 22,
                  color: isDarkMode 
                      ? const Color(0xFF8E8E93)
                      : const Color(0xFF6D6D70),
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      CupertinoIcons.waveform,
                      color: isDarkMode 
                          ? const Color(0xFF8E8E93)
                          : const Color(0xFF6D6D70),
                      size: 22,
                    );
                  },
                ),
              ),
            ),
            if (!isSelected) ...[
              const SizedBox(height: 4),
              // Лейбл для неактивной Sinusoid
              Text(
                'Sinusoid',
                style: TextStyle(
                  color: isDarkMode 
                      ? const Color(0xFF8E8E93)
                      : const Color(0xFF6D6D70),
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.1,
                ),
              ),
              const SizedBox(height: 4),
            ] else ...[
              const SizedBox(height: 8),
              // Премиальный индикатор активного элемента
              AnimatedContainer(
                duration: const Duration(milliseconds: 350),
                curve: Curves.easeOutCubic,
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: const Color(0xFF8A2BE2),
                  borderRadius: BorderRadius.circular(2),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF8A2BE2).withOpacity(0.8),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopNavigation(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Увеличенный логотип без рамки
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: Image.asset(
                  'logo/TMM/new_tmm_logo.png',
                  width: 65,
                  height: 65,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 65,
                      height: 65,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            const Color(0xFF2D1B3D).withOpacity(0.95),
                            const Color(0xFF1A0E23).withOpacity(0.98),
                            const Color(0xFF0F0612).withOpacity(1.0),
                          ],
                          stops: const [0.0, 0.6, 1.0],
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Icon(
                        CupertinoIcons.money_dollar_circle_fill,
                        color: Colors.white,
                        size: 30,
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'TMM',
                style: TextStyle(
                  color: isDarkMode 
                      ? const Color(0xFFFFFFFF)
                      : const Color(0xFF000000),
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.3,
                ),
              ),
            ],
          ),
          // Премиальные навигационные элементы iOS
          Row(
            children: [
              _buildDesktopPremiumNavItem(
                context,
                0,
                CupertinoIcons.news_solid,
                CupertinoIcons.news,
                'News',
              ),
              _buildDesktopPremiumNavItem(
                context,
                1,
                CupertinoIcons.chart_bar_circle_fill,
                CupertinoIcons.chart_bar_circle,
                'Charts',
              ),
              _buildDesktopPremiumSinusoidNavItem(context, 2),
              _buildDesktopPremiumNavItem(
                context,
                3,
                CupertinoIcons.book_solid,
                CupertinoIcons.book,
                'Learn',
              ),
              _buildDesktopPremiumNavItem(
                context,
                4,
                CupertinoIcons.person_solid,
                CupertinoIcons.person,
                'Profile',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopPremiumSinusoidNavItem(BuildContext context, int index) {
    final isSelected = widget.currentIndex == index;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onTap(index);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeOutCubic,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          // Эффект тиснения для десктопа с красивым градиентом
          color: isSelected ? null : Colors.transparent,
          gradient: isSelected ? LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF2D1B3D).withOpacity(0.95), // Very dark purple
              const Color(0xFF1A0E23).withOpacity(0.98), // Almost black purple
              const Color(0xFF0F0612).withOpacity(1.0), // Deep dark purple-black
            ],
            stops: const [0.0, 0.6, 1.0],
          ) : null,
          borderRadius: BorderRadius.circular(12),
          border: isSelected
              ? Border.all(
                  color: const Color(0xFF4A2C5E).withOpacity(0.9),
                  width: 2.5, // Увеличена толщина границы
                )
              : null,
          boxShadow: isSelected ? [
            // Внешний glow эффект
            BoxShadow(
              color: const Color(0xFF4A2C5E).withOpacity(0.5),
              blurRadius: 15,
              spreadRadius: 1,
              offset: const Offset(0, 0),
            ),
            // Усиленный эффект тиснения - очень темная тень справа снизу
            BoxShadow(
              color: Colors.black.withOpacity(0.95),
              blurRadius: 12,
              offset: const Offset(4, 4),
            ),
            // Светлая тень слева сверху для эффекта глубины (более контрастная)
            BoxShadow(
              color: const Color(0xFF6A4C7A).withOpacity(0.6),
              blurRadius: 8,
              offset: const Offset(-3, -3),
            ),
            // Дополнительная внутренняя тень для усиления эффекта вдавленности
            BoxShadow(
              color: Colors.black.withOpacity(0.8),
              blurRadius: 16,
              offset: const Offset(2, 2),
            ),
          ] : null,
        ),
        child: isSelected ? Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'logo/bottom_navigation_bar/Sinusoid.png',
              width: 20,
              height: 20,
              color: Colors.white.withOpacity(0.98),
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  CupertinoIcons.waveform,
                  color: Colors.white.withOpacity(0.98),
                  size: 20,
                );
              },
            ),
            const SizedBox(width: 8),
            Text(
              'Sinusoid',
              style: TextStyle(
                color: Colors.white.withOpacity(0.98),
                fontWeight: FontWeight.w800,
                fontSize: 14,
                letterSpacing: 0.2,
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.8),
                    offset: const Offset(0, 1),
                    blurRadius: 4,
                  ),
                ],
              ),
            ),
          ],
        ) : Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'logo/bottom_navigation_bar/Sinusoid.png',
              width: 20,
              height: 20,
              color: isDarkMode 
                  ? const Color(0xFF8E8E93)
                  : const Color(0xFF6D6D70),
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  CupertinoIcons.waveform,
                  color: isDarkMode 
                      ? const Color(0xFF8E8E93)
                      : const Color(0xFF6D6D70),
                  size: 20,
                );
              },
            ),
            const SizedBox(width: 8),
            Text(
              'Sinusoid',
              style: TextStyle(
                color: isDarkMode 
                    ? const Color(0xFF8E8E93)
                    : const Color(0xFF6D6D70),
                fontWeight: FontWeight.w500,
                fontSize: 14,
                letterSpacing: 0.1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopPremiumNavItem(
    BuildContext context,
    int index,
    IconData activeIcon,
    IconData inactiveIcon,
    String label,
  ) {
    final isSelected = widget.currentIndex == index;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onTap(index);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeOutCubic,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          // Эффект тиснения для десктопа с красивым градиентом
          color: isSelected ? null : Colors.transparent,
          gradient: isSelected ? LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF2D1B3D).withOpacity(0.95), // Very dark purple
              const Color(0xFF1A0E23).withOpacity(0.98), // Almost black purple
              const Color(0xFF0F0612).withOpacity(1.0), // Deep dark purple-black
            ],
            stops: const [0.0, 0.6, 1.0],
          ) : null,
          borderRadius: BorderRadius.circular(12),
          border: isSelected
              ? Border.all(
                  color: const Color(0xFF4A2C5E).withOpacity(0.9),
                  width: 2.5, // Увеличена толщина границы
                )
              : null,
          boxShadow: isSelected ? [
            // Внешний glow эффект
            BoxShadow(
              color: const Color(0xFF4A2C5E).withOpacity(0.5),
              blurRadius: 15,
              spreadRadius: 1,
              offset: const Offset(0, 0),
            ),
            // Усиленный эффект тиснения - очень темная тень справа снизу
            BoxShadow(
              color: Colors.black.withOpacity(0.95),
              blurRadius: 12,
              offset: const Offset(4, 4),
            ),
            // Светлая тень слева сверху для эффекта глубины (более контрастная)
            BoxShadow(
              color: const Color(0xFF6A4C7A).withOpacity(0.6),
              blurRadius: 8,
              offset: const Offset(-3, -3),
            ),
            // Дополнительная внутренняя тень для усиления эффекта вдавленности
            BoxShadow(
              color: Colors.black.withOpacity(0.8),
              blurRadius: 16,
              offset: const Offset(2, 2),
            ),
          ] : null,
        ),
        child: isSelected ? Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              activeIcon,
              color: Colors.white.withOpacity(0.98),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withOpacity(0.98),
                fontWeight: FontWeight.w800,
                fontSize: 14,
                letterSpacing: 0.2,
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.8),
                    offset: const Offset(0, 1),
                    blurRadius: 4,
                  ),
                ],
              ),
            ),
          ],
        ) : Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              inactiveIcon,
              color: isDarkMode 
                  ? const Color(0xFF8E8E93)
                  : const Color(0xFF6D6D70),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isDarkMode 
                    ? const Color(0xFF8E8E93)
                    : const Color(0xFF6D6D70),
                fontWeight: FontWeight.w500,
                fontSize: 14,
                letterSpacing: 0.1,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
