# Cosmic Background Widget Usage Guide

The `CosmicBackground` widget provides a beautiful animated star field background that can be used across different screens in your Flutter application. This guide explains how to use and customize the background.

## Basic Usage

To use the cosmic background, simply wrap your screen's content with the `CosmicBackground` widget:

```dart
class MyScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Add the cosmic background
          const CosmicBackground(),
          
          // Your screen content
          Center(
            child: Text('My Screen Content'),
          ),
        ],
      ),
    );
  }
}
```

## Customization Options

The `CosmicBackground` widget offers several customization options:

### Star Properties

- `starCount`: Number of stars to display (default: 200)
- `baseStarColor`: Base color for stars (default: Colors.white)
- `starColors`: Additional colors for star variety
- `minStarSize`: Minimum star size (default: 1.0)
- `maxStarSize`: Maximum star size (default: 3.0)

### Animation

- `animationDuration`: Duration of the star twinkling animation (default: 5000ms)

### Effects

- `enableComet`: Whether to show the comet effect (default: true)
- `backgroundColor`: Background color (default: Colors.black)
- `enableParallax`: Whether to enable parallax effect on mouse movement (default: true)
- `parallaxIntensity`: Intensity of the parallax effect (default: 30.0)

## Example with Customization

Here's an example with custom settings:

```dart
CosmicBackground(
  starCount: 300,
  baseStarColor: Colors.blue,
  starColors: [
    Colors.white,
    Colors.blue.shade300,
    Colors.blue.shade200,
  ],
  minStarSize: 1.5,
  maxStarSize: 4.0,
  animationDuration: const Duration(milliseconds: 8000),
  enableComet: true,
  backgroundColor: const Color(0xFF000033),
  enableParallax: true,
  parallaxIntensity: 40.0,
)
```

## Performance Considerations

- The widget is optimized for performance, but using a very high `starCount` (e.g., >500) may impact performance on lower-end devices.
- The parallax effect is automatically disabled on touch devices to save resources.
- The comet effect is relatively lightweight and can be disabled if needed.

## Best Practices

1. Use the `Stack` widget to layer the cosmic background behind your content
2. Consider the color scheme of your app when choosing star colors
3. Adjust the `parallaxIntensity` based on your app's style (lower values for subtle effects, higher for more dramatic movement)
4. Use the `backgroundColor` to match your app's theme
5. For login/registration screens, consider using a higher `starCount` for a more immersive experience

## Troubleshooting

If you experience performance issues:
1. Reduce the `starCount`
2. Disable the comet effect by setting `enableComet: false`
3. Disable parallax by setting `enableParallax: false`
4. Increase the `animationDuration` to reduce animation frequency

## Example Implementation

Here's a complete example of a login screen using the cosmic background:

```dart
class LoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          const CosmicBackground(
            starCount: 250,
            baseStarColor: Colors.white,
            starColors: [
              Colors.blue.shade300,
              Colors.blue.shade200,
            ],
            minStarSize: 1.0,
            maxStarSize: 3.0,
            animationDuration: Duration(milliseconds: 6000),
            enableComet: true,
            backgroundColor: Color(0xFF000033),
            enableParallax: true,
            parallaxIntensity: 35.0,
          ),
          
          // Login form
          Center(
            child: Card(
              color: Colors.white.withOpacity(0.9),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Your login form widgets
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
``` 