import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math';

/// Виджет для отображения пользовательского фона с эффектом размытия и темным оверлеем
class CustomNewsBackground extends StatefulWidget {
  final double scrollOffset;
  final AuroraSentimentType? sentiment;
  final double intensity;
  
  const CustomNewsBackground({
    Key? key,
    this.scrollOffset = 0.0,
    this.sentiment,
    this.intensity = 1.0,
  }) : super(key: key);

  @override
  State<CustomNewsBackground> createState() => _CustomNewsBackgroundState();
}

class _CustomNewsBackgroundState extends State<CustomNewsBackground> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  
  // Позиция указателя для параллакс-эффекта
  Offset _pointerPosition = Offset.zero;
  
  // Цвета северного сияния
  final Color _auroraBlue = const Color(0xFF81D4FA).withOpacity(0.4);
  final Color _auroraColor = const Color(0xFF81D4FA).withOpacity(0.2);
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 60), // Медленная анимация
    )..repeat();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onHover: (event) {
        setState(() {
          _pointerPosition = event.localPosition;
        });
      },
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Базовый фон (градиент вместо изображения)
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.black,
                  const Color(0xFF121212),
                  const Color(0xFF0A0A0A),
                  Colors.black,
                ],
              ),
            ),
          ),
          
          // Черный оверлей для улучшения читаемости
          Container(
            color: Colors.black.withOpacity(0.7),
          ),
          
          // Размытые элементы "северного сияния"
          CustomPaint(
            painter: AuroraPainter(
              animationValue: _animationController.value,
              pointerPosition: _pointerPosition,
              scrollOffset: widget.scrollOffset,
              sentiment: widget.sentiment ?? AuroraSentimentType.neutral,
              intensity: widget.intensity,
            ),
          ),
          
          // Дополнительный градиент для улучшения читаемости текста
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: const [0.0, 0.2, 0.8, 1.0],
                colors: [
                  Colors.black.withOpacity(0.5),
                  Colors.transparent,
                  Colors.transparent,
                  Colors.black.withOpacity(0.6),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Тип настроения для цветовой схемы aurora
enum AuroraSentimentType { positive, negative, neutral }

/// Пользовательский рисовальщик для эффекта северного сияния
class AuroraPainter extends CustomPainter {
  final double animationValue;
  final Offset pointerPosition;
  final double scrollOffset;
  final AuroraSentimentType sentiment;
  final double intensity;
  
  final List<AuroraPoint> _points = [];
  final int _numberOfPoints = 8; // Небольшое количество для минимализма
  
  AuroraPainter({
    required this.animationValue,
    required this.pointerPosition,
    required this.scrollOffset,
    required this.sentiment,
    required this.intensity,
  }) {
    _initPoints();
  }
  
  void _initPoints() {
    if (_points.isEmpty) {
      final random = Random(42); // Фиксированный seed для консистентности
      
      for (int i = 0; i < _numberOfPoints; i++) {
        final x = random.nextDouble();
        final y = random.nextDouble() * 0.7 + 0.15; // Размещаем в центральной области
        final size = random.nextDouble() * 100 + 100; // Большие размытые пятна
        
        _points.add(AuroraPoint(
          x: x,
          y: y,
          size: size,
        ));
      }
    }
  }
  
  Color _getAuroraColor() {
    Color baseColor;
    
    switch (sentiment) {
      case AuroraSentimentType.positive:
        baseColor = const Color(0xFF81D4FA).withOpacity(0.15); // Голубой
        break;
      case AuroraSentimentType.negative:
        baseColor = const Color(0xFF81A4FA).withOpacity(0.15); // Чуть фиолетовый
        break;
      case AuroraSentimentType.neutral:
      default:
        baseColor = const Color(0xFF81D4FA).withOpacity(0.15); // Нейтральный голубой
    }
    
    return baseColor;
  }
  
  @override
  void paint(Canvas canvas, Size size) {
    for (var point in _points) {
      // Эффект параллакса от движения курсора (очень тонкий)
      final cursorParallaxX = (pointerPosition.dx / size.width - 0.5) * 20;
      final cursorParallaxY = (pointerPosition.dy / size.height - 0.5) * 20;
      
      // Эффект параллакса от прокрутки (очень легкий)
      final scrollParallax = scrollOffset * 0.02;
      
      // Анимация медленного движения
      final timeOffset = animationValue * 2 * pi;
      final driftX = sin(timeOffset + point.x * 5) * 20;
      final driftY = cos(timeOffset + point.y * 5) * 20;
      
      // Финальное положение
      final x = point.x * size.width + cursorParallaxX + driftX;
      final y = point.y * size.height + cursorParallaxY + driftY - scrollParallax;
      
      // Рисуем размытое пятно
      final paint = Paint()
        ..color = _getAuroraColor().withOpacity(0.4 * intensity)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, point.size * 0.5);
      
      canvas.drawCircle(Offset(x, y), point.size, paint);
    }
  }
  
  @override
  bool shouldRepaint(covariant AuroraPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
           oldDelegate.pointerPosition != pointerPosition ||
           oldDelegate.scrollOffset != scrollOffset ||
           oldDelegate.intensity != intensity;
  }
}

/// Класс для представления точки северного сияния
class AuroraPoint {
  final double x;
  final double y;
  final double size;
  
  AuroraPoint({
    required this.x,
    required this.y,
    required this.size,
  });
}
