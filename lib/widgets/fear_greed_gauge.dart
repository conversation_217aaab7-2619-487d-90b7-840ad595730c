import 'package:flutter/material.dart';
import 'dart:math';

class FearGreedGauge extends StatelessWidget {
  final int value; // 0-100
  final double size;

  const FearGreedGauge({Key? key, required this.value, this.size = 60}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size * 0.8,
          child: CustomPaint(
            painter: _FearGreedGaugePainter(value),
          ),
        ),
        const SizedBox(height: 12),
        Text(
          _getLabel(value),
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: _getColor(value),
            shadows: [
              Shadow(
                blurRadius: 4,
                color: Colors.black.withOpacity(0.3),
                offset: Offset(1, 1),
              ),
            ],
          ),
        ),
      ],
    );
  }

  static String _getLabel(int v) {
    if (v < 20) return 'Extreme Fear';
    if (v < 40) return 'Fear';
    if (v < 60) return 'Neutral';
    if (v < 80) return 'Greed';
    return 'Extreme Greed';
  }

  static Color _getColor(int v) {
    if (v < 20) return Colors.red;
    if (v < 40) return Colors.orange;
    if (v < 60) return Colors.yellow.shade700;
    if (v < 80) return Colors.lightGreen;
    return Colors.green;
  }
}

class _FearGreedGaugePainter extends CustomPainter {
  final int value;
  _FearGreedGaugePainter(this.value);

  @override
  void paint(Canvas canvas, Size size) {
    // Центр строго по центру нижней части гейджа
    final center = Offset(size.width / 2, size.height * 0.95);
    final radius = size.width * 0.45;
    final startAngle = pi; // -180 градусов (слева)
    final sweepAngle = pi; // 180 градусов (до справа)

    // Сегменты
    final segments = [
      { 'color': Colors.red, 'start': 0.0, 'end': 0.2 },
      { 'color': Colors.orange, 'start': 0.2, 'end': 0.4 },
      { 'color': Colors.yellow.shade700, 'start': 0.4, 'end': 0.6 },
      { 'color': Colors.lightGreen, 'start': 0.6, 'end': 0.8 },
      { 'color': Colors.green, 'start': 0.8, 'end': 1.0 },
    ];
    final strokeWidth = 18.0;

    for (var seg in segments) {
      final segStart = startAngle + sweepAngle * (seg['start'] as double);
      final segSweep = sweepAngle * ((seg['end'] as double) - (seg['start'] as double));
      final paint = Paint()
        ..color = seg['color'] as Color
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth
        ..strokeCap = StrokeCap.round;
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        segStart,
        segSweep,
        false,
        paint,
      );
    }

    // Стрелка
    // Угол: 0 = слева, 50 = вверх, 100 = справа
    final percent = value.clamp(0, 100) / 100;
    final angle = startAngle + sweepAngle * percent;
    final pointerLength = radius - strokeWidth / 2;
    final pointerPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 5
      ..strokeCap = StrokeCap.round;
    final pointerEnd = Offset(
      center.dx + pointerLength * cos(angle),
      center.dy + pointerLength * sin(angle),
    );
    canvas.drawLine(center, pointerEnd, pointerPaint);

    // Круглая "шляпка"
    final capPaint = Paint()..color = Colors.white;
    canvas.drawCircle(pointerEnd, 8, capPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
} 