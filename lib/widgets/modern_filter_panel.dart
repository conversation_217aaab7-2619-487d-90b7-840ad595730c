import 'package:flutter/material.dart';
import '../models/news_item.dart';
import 'package:finance_ai/models/sentiment_types.dart';

class ModernFilterPanel extends StatelessWidget {
  final List<String> selectedTags;
  final SentimentType? selectedSentiment;
  final Function(String) onTagToggle;
  final Function(SentimentType?) onSentimentChanged;

  const ModernFilterPanel({
    super.key,
    required this.selectedTags,
    required this.selectedSentiment,
    required this.onTagToggle,
    required this.onSentimentChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.0,
            offset: const Offset(0, 2.0),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tags filter
            Row(
              children: [
                Icon(
                  Icons.tag,
                  size: 18,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                ),
                const SizedBox(width: 8.0),
                Text(
                  'Filter by tags',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12.0),
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                _buildTagChip('Macro', context),
                _buildTagChip('AI', context),
                _buildTagChip('BTC', context),
                _buildTagChip('Ethereum', context),
                _buildTagChip('Memes', context),
                _buildTagChip('DeFi', context),
                _buildTagChip('RWA', context),
                _buildTagChip('NFT', context),
                _buildTagChip('SEC', context),
                _buildTagChip('AirDrop', context),
              ],
            ),
            
            const SizedBox(height: 20.0),
            
            // Sentiment filter
            Row(
              children: [
                Icon(
                  Icons.mood,
                  size: 18,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                ),
                const SizedBox(width: 8.0),
                Text(
                  'Filter by sentiment',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12.0),
            Row(
              children: [
                _buildSentimentButton(null, 'All', context),
                const SizedBox(width: 8.0),
                _buildSentimentButton(SentimentType.positive, 'Positive', context),
                const SizedBox(width: 8.0),
                _buildSentimentButton(SentimentType.neutral, 'Neutral', context),
                const SizedBox(width: 8.0),
                _buildSentimentButton(SentimentType.negative, 'Negative', context),
              ],
            ),
            
            const SizedBox(height: 16.0),
            
            // Apply and Clear buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    // Clear all filters
                    onSentimentChanged(null);
                    for (final tag in List<String>.from(selectedTags)) {
                      onTagToggle(tag);
                    }
                  },
                  child: Text(
                    'Clear All',
                    style: TextStyle(
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTagChip(String tag, BuildContext context) {
    final isSelected = selectedTags.contains(tag);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        color: isSelected 
            ? Theme.of(context).primaryColor.withOpacity(isDarkMode ? 0.3 : 0.2)
            : isDarkMode ? Colors.grey[800] : Colors.grey[200],
        borderRadius: BorderRadius.circular(20.0),
        border: Border.all(
          color: isSelected 
              ? Theme.of(context).primaryColor
              : isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          width: 1.0,
        ),
      ),
      child: InkWell(
        onTap: () => onTagToggle(tag),
        borderRadius: BorderRadius.circular(20.0),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isSelected) ...[
                Icon(
                  Icons.check,
                  size: 14.0,
                  color: isDarkMode 
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 4.0),
              ],
              Text(
                tag,
                style: TextStyle(
                  color: isSelected 
                      ? isDarkMode ? Theme.of(context).primaryColor : Theme.of(context).primaryColor
                      : isDarkMode ? Colors.grey[300] : Colors.grey[800],
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 13.0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSentimentButton(SentimentType? sentiment, String label, BuildContext context) {
    final isSelected = selectedSentiment == sentiment;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    Color backgroundColor;
    Color textColor;
    Color borderColor;
    
    if (isSelected) {
      backgroundColor = _getSentimentColor(sentiment).withOpacity(isDarkMode ? 0.3 : 0.2);
      textColor = _getSentimentColor(sentiment);
      borderColor = _getSentimentColor(sentiment);
    } else {
      backgroundColor = isDarkMode ? Colors.grey[800]! : Colors.grey[200]!;
      textColor = isDarkMode ? Colors.grey[400]! : Colors.grey[700]!;
      borderColor = isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;
    }
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20.0),
        border: Border.all(
          color: borderColor,
          width: 1.0,
        ),
      ),
      child: InkWell(
        onTap: () => onSentimentChanged(sentiment),
        borderRadius: BorderRadius.circular(20.0),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getSentimentIcon(sentiment),
                size: 14.0,
                color: textColor,
              ),
              const SizedBox(width: 4.0),
              Text(
                label,
                style: TextStyle(
                  color: textColor,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 13.0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getSentimentColor(SentimentType? sentiment) {
    switch (sentiment) {
      case SentimentType.positive:
        return Colors.green;
      case SentimentType.negative:
        return Colors.red;
      case SentimentType.neutral:
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }
  
  IconData _getSentimentIcon(SentimentType? sentiment) {
    switch (sentiment) {
      case SentimentType.positive:
        return Icons.trending_up;
      case SentimentType.negative:
        return Icons.trending_down;
      case SentimentType.neutral:
        return Icons.trending_flat;
      default:
        return Icons.filter_alt;
    }
  }
}
