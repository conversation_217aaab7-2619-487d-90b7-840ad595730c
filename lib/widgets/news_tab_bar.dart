import 'package:flutter/material.dart';

class NewsTabBar extends StatefulWidget {
  final int selectedIndex;
  final ValueChanged<int> onTabChanged;
  const NewsTabBar({
    Key? key,
    required this.selectedIndex,
    required this.onTabChanged,
  }) : super(key: key);

  @override
  State<NewsTabBar> createState() => _NewsTabBarState();
}

class _NewsTabBarState extends State<NewsTabBar> with SingleTickerProviderStateMixin {
  late int _selectedIndex;
  late List<_TabData> _tabs;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.selectedIndex;
    _tabs = [
      _TabData('All', Icons.public),
      _TabData('Crypto', Icons.currency_bitcoin),
      _TabData('Stock', Icons.trending_up),
      _TabData('Whales', Icons.waves),
    ];
  }

  @override
  void didUpdateWidget(covariant NewsTabBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedIndex != _selectedIndex) {
      setState(() {
        _selectedIndex = widget.selectedIndex;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final double tabHeight = 60.0;
    final double borderRadius = 25.0;
    final double iconSize = 20.0;
    final double textSize = 16.0;
    final double horizontalPadding = 20.0;
    final double verticalPadding = 10.0;
    final Color activeTextColor = const Color(0xFF1a4d42);
    final Color inactiveTextColor = Colors.white;
    final Color activeBg = Colors.white;
    final Color inactiveBorder = Colors.white.withOpacity(0.3);
    final List<Color> gradientColors = [const Color(0xFF1a4d42), const Color(0xFF0f2f27)];

    return SafeArea(
      bottom: false,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradientColors,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(_tabs.length, (index) {
            final bool isActive = _selectedIndex == index;
            return Flexible(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: _buildTab(
                  context,
                  index,
                  isActive,
                  tabHeight,
                  borderRadius,
                  iconSize,
                  textSize,
                  horizontalPadding,
                  verticalPadding,
                  activeTextColor,
                  inactiveTextColor,
                  activeBg,
                  inactiveBorder,
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _buildTab(
    BuildContext context,
    int index,
    bool isActive,
    double tabHeight,
    double borderRadius,
    double iconSize,
    double textSize,
    double horizontalPadding,
    double verticalPadding,
    Color activeTextColor,
    Color inactiveTextColor,
    Color activeBg,
    Color inactiveBorder,
  ) {
    final tab = _tabs[index];
    return GestureDetector(
      onTap: () {
        if (_selectedIndex != index) {
          setState(() => _selectedIndex = index);
          widget.onTabChanged(index);
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        height: tabHeight,
        decoration: BoxDecoration(
          color: isActive ? activeBg : Colors.transparent,
          borderRadius: BorderRadius.circular(borderRadius),
          border: isActive
              ? null
              : Border.all(color: inactiveBorder, width: 1.5),
          boxShadow: isActive
              ? [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10,
                    spreadRadius: 2,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [],
        ),
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: verticalPadding,
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          splashColor: Colors.white.withOpacity(0.1),
          highlightColor: Colors.white.withOpacity(0.05),
          onTap: () {
            if (_selectedIndex != index) {
              setState(() => _selectedIndex = index);
              widget.onTabChanged(index);
            }
          },
          child: AnimatedScale(
            scale: isActive ? 1.08 : 1.0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  tab.icon,
                  color: isActive ? activeTextColor : inactiveTextColor,
                  size: iconSize,
                ),
                const SizedBox(width: 8),
                Text(
                  tab.label,
                  style: TextStyle(
                    fontSize: textSize,
                    fontWeight: FontWeight.w500,
                    color: isActive ? activeTextColor : inactiveTextColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _TabData {
  final String label;
  final IconData icon;
  const _TabData(this.label, this.icon);
} 