import 'package:flutter/material.dart';
import 'dart:math' as math;

class OptimizedSilkBackground extends StatefulWidget {
  final double speed;
  final double scale;
  final Color color;
  final double noiseIntensity;
  final double rotation;

  const OptimizedSilkBackground({
    super.key,
    this.speed = 1.8,
    this.scale = 0.8,
    this.color = const Color(0xFF1C1C1C),
    this.noiseIntensity = 0.5,
    this.rotation = 0.48,
  });

  @override
  State<OptimizedSilkBackground> createState() => _OptimizedSilkBackgroundState();
}

class _OptimizedSilkBackgroundState extends State<OptimizedSilkBackground>
    with TickerProviderStateMixin {
  late AnimationController _primaryController;
  late AnimationController _secondaryController;
  late Animation<double> _primaryAnimation;
  late Animation<double> _secondaryAnimation;

  @override
  void initState() {
    super.initState();
    
    // Основная анимация
    _primaryController = AnimationController(
      duration: Duration(milliseconds: (2000 / widget.speed).round()),
      vsync: this,
    )..repeat();
    
    // Вторичная анимация для сложности
    _secondaryController = AnimationController(
      duration: Duration(milliseconds: (3000 / widget.speed).round()),
      vsync: this,
    )..repeat();
    
    _primaryAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(_primaryController);
    
    _secondaryAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(_secondaryController);
  }

  @override
  void dispose() {
    _primaryController.dispose();
    _secondaryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_primaryAnimation, _secondaryAnimation]),
      builder: (context, child) {
        return CustomPaint(
          painter: OptimizedSilkPainter(
            primaryTime: _primaryAnimation.value,
            secondaryTime: _secondaryAnimation.value,
            speed: widget.speed,
            scale: widget.scale,
            color: widget.color,
            noiseIntensity: widget.noiseIntensity,
            rotation: widget.rotation,
          ),
          size: Size.infinite,
        );
      },
    );
  }
}

class OptimizedSilkPainter extends CustomPainter {
  final double primaryTime;
  final double secondaryTime;
  final double speed;
  final double scale;
  final Color color;
  final double noiseIntensity;
  final double rotation;

  OptimizedSilkPainter({
    required this.primaryTime,
    required this.secondaryTime,
    required this.speed,
    required this.scale,
    required this.color,
    required this.noiseIntensity,
    required this.rotation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Offset.zero & size;
    
    // Создаем несколько слоев градиентов для имитации шелкового эффекта
    _paintSilkLayer(canvas, size, 1.0, primaryTime);
    _paintSilkLayer(canvas, size, 0.7, primaryTime + 1.0);
    _paintSilkLayer(canvas, size, 0.4, secondaryTime);
  }

  void _paintSilkLayer(Canvas canvas, Size size, double intensity, double time) {
    final colors = <Color>[];
    final stops = <double>[];
    
    // Создаем 50 градиентных точек для плавности
    for (int i = 0; i < 50; i++) {
      final progress = i / 49.0;
      final silkValue = _calculateSilkValue(progress, time);
      
      final opacity = (silkValue * intensity).clamp(0.0, 1.0);
      colors.add(color.withOpacity(opacity * 0.7)); // Немного приглушаем
      stops.add(progress);
    }
    
    final paint = Paint()
      ..shader = LinearGradient(
        begin: _calculateGradientBegin(time),
        end: _calculateGradientEnd(time),
        colors: colors,
        stops: stops,
      ).createShader(Offset.zero & size)
      ..blendMode = BlendMode.overlay;
    
    canvas.drawRect(Offset.zero & size, paint);
  }

  double _calculateSilkValue(double progress, double time) {
    // Имитируем оригинальные формулы шейдера
    final tOffset = speed * time * 0.05;
    
    // Поворот UV координат
    final cos = math.cos(rotation);
    final sin = math.sin(rotation);
    final rotatedU = progress * scale * cos;
    final rotatedV = progress * scale * sin;
    
    final texX = rotatedU * scale;
    final texY = rotatedV * scale;
    
    // Основная волновая функция из оригинального шейдера
    final waveY = texY + 0.03 * math.sin(8.0 * texX - tOffset);
    
    final pattern = 0.6 + 0.4 * math.sin(
      5.0 * (texX + waveY + 
             math.cos(3.0 * texX + 5.0 * waveY) +
             0.02 * tOffset) +
      math.sin(20.0 * (texX + waveY - 0.1 * tOffset))
    );
    
    // Добавляем шум
    final noise = _generateNoise(texX + texY + time) * noiseIntensity;
    
    return (pattern - noise / 15.0).clamp(0.0, 1.0);
  }

  Alignment _calculateGradientBegin(double time) {
    // Создаем движение снизу вверх с легким покачиванием
    return Alignment(
      math.sin(time * 0.3 + rotation) * 0.3,
      1.0 - (time * 0.1) % 2.0, // Движение снизу вверх
    );
  }

  Alignment _calculateGradientEnd(double time) {
    return Alignment(
      -math.sin(time * 0.3 + rotation) * 0.3,
      -1.0 + (time * 0.1) % 2.0, // Движение снизу вверх
    );
  }

  double _generateNoise(double coord) {
    // Простая псевдослучайная функция
    const g = 2.71828182845904523536;
    final r = g * math.sin(g * coord);
    return (r * (1.0 + coord)).abs() % 1.0;
  }

  @override
  bool shouldRepaint(covariant OptimizedSilkPainter oldDelegate) {
    return oldDelegate.primaryTime != primaryTime ||
           oldDelegate.secondaryTime != secondaryTime ||
           oldDelegate.speed != speed ||
           oldDelegate.scale != scale ||
           oldDelegate.color != color ||
           oldDelegate.noiseIntensity != noiseIntensity ||
           oldDelegate.rotation != rotation;
  }
} 