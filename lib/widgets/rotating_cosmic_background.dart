import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'cosmic_background.dart';

class RotatingCosmicBackground extends StatefulWidget {
  const RotatingCosmicBackground({
    Key? key,
    this.starCount = 1200,
    this.minStarSize = 1.0,
    this.maxStarSize = 3.0,
    this.animationDuration = const Duration(milliseconds: 6000),
    this.backgroundColor = const Color(0xFF000011),
    this.enableParallax = true,
    this.parallaxIntensity = 17.5,
    this.rotationDuration = const Duration(minutes: 5),
  }) : super(key: key);

  final int starCount;
  final double minStarSize;
  final double maxStarSize;
  final Duration animationDuration;
  final Color backgroundColor;
  final bool enableParallax;
  final double parallaxIntensity;
  final Duration rotationDuration;

  @override
  State<RotatingCosmicBackground> createState() => _RotatingCosmicBackgroundState();
}

class _RotatingCosmicBackgroundState extends State<RotatingCosmicBackground> with SingleTickerProviderStateMixin {
  late final AnimationController _rotationController;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      vsync: this,
      duration: widget.rotationDuration,
    )..repeat();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final double maxSide = math.max(constraints.maxWidth, constraints.maxHeight);
        final double squareSide = maxSide * 3.0; // 1.5 вверх и вниз от центра

        return AnimatedBuilder(
          animation: _rotationController,
          builder: (context, child) {
            return Transform.rotate(
              angle: _rotationController.value * 2 * math.pi,
              child: Transform.scale(scale: 2.3, child: child),
            );
          },
          child: Center(
            child: SizedBox(
              width: squareSide,
              height: squareSide,
              child: CosmicBackground(
                starCount: widget.starCount,
                minStarSize: widget.minStarSize,
                maxStarSize: widget.maxStarSize,
                animationDuration: widget.animationDuration,
                enableComet: false,
                enableAsteroids: false,
                enableSatellites: false,
                backgroundColor: widget.backgroundColor,
                enableParallax: widget.enableParallax,
                parallaxIntensity: widget.parallaxIntensity,
              ),
            ),
          ),
        );
      },
    );
  }
} 