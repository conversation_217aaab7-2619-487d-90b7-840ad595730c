import 'package:flutter/material.dart';

class SideDrawerMenu extends StatelessWidget {
  final int selectedIndex;
  final ValueChanged<int> onCategorySelected;
  final VoidCallback onClose;

  const SideDrawerMenu({
    Key? key,
    required this.selectedIndex,
    required this.onCategorySelected,
    required this.onClose,
  }) : super(key: key);

  static const _categories = [
    _MenuCategory('All', Icons.public),
    _MenuCategory('Crypto', Icons.currency_bitcoin),
    _MenuCategory('Stock', Icons.trending_up),
    _MenuCategory('Whales', Icons.waves),
  ];

  @override
  Widget build(BuildContext context) {
    return Material(
      color: const Color(0xFF1a4d42),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            // Логотип или иконка приложения
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: CircleAvatar(
                radius: 24,
                backgroundColor: Colors.white.withOpacity(0.12),
                child: const Icon(Icons.eco, color: Colors.white, size: 28),
              ),
            ),
            const SizedBox(height: 32),
            // Навигационные пункты
            ...List.generate(_categories.length, (i) {
              final isActive = selectedIndex == i;
              return InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () {
                  onCategorySelected(i);
                  onClose();
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 14),
                  decoration: BoxDecoration(
                    color: isActive ? Colors.white.withOpacity(0.10) : Colors.transparent,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _categories[i].icon,
                        color: Colors.white,
                        size: 22,
                      ),
                      const SizedBox(width: 16),
                      Text(
                        _categories[i].label,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: isActive ? FontWeight.bold : FontWeight.w500,
                          fontSize: 17,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }),
            const Spacer(),
            // Можно добавить logout или другие действия
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}

class _MenuCategory {
  final String label;
  final IconData icon;
  const _MenuCategory(this.label, this.icon);
} 