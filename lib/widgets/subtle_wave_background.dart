import 'package:flutter/material.dart';
import 'dart:math' as math;

class SubtleWaveBackground extends StatefulWidget {
  const SubtleWaveBackground({super.key});

  @override
  State<SubtleWaveBackground> createState() => _SubtleWaveBackgroundState();
}

class _SubtleWaveBackgroundState extends State<SubtleWaveBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 15), // Очень медленная анимация
      vsync: this,
    )..repeat();
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: SubtleWavePainter(
            animationValue: _animation.value,
          ),
          size: Size.infinite,
        );
      },
    );
  }
}

class SubtleWavePainter extends CustomPainter {
  final double animationValue;

  SubtleWavePainter({required this.animationValue});

  @override
  void paint(Canvas canvas, Size size) {
    // Темный базовый фон как на изображении
    final backgroundPaint = Paint()
      ..color = const Color(0xFF0A0A0A);
    canvas.drawRect(Offset.zero & size, backgroundPaint);

    // Создаем множество тонких горизонтальных волнистых линий
    _drawWaveLines(canvas, size);
  }

  void _drawWaveLines(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5
      ..blendMode = BlendMode.screen;

    // Генерируем случайные паттерны волн
    final random = math.Random(42); // Фиксированное семя для воспроизводимости
    
    // Создаем много горизонтальных волнистых линий
    for (int i = 0; i < 100; i++) {
      final y = (i * size.height / 100) + (animationValue * size.height * 2) % size.height;
      
      if (y < -10 || y > size.height + 10) continue;
      
      // Рандомные параметры для каждой линии
      final amplitude = random.nextDouble() * 8 + 2; // Высота волны
      final frequency = random.nextDouble() * 0.02 + 0.005; // Частота волны
      final phase = random.nextDouble() * math.pi * 2; // Фазовый сдвиг
      final opacity = (random.nextDouble() * 0.15 + 0.05).clamp(0.0, 1.0); // Очень тонкие линии
      
      paint.color = Color.fromRGBO(255, 255, 255, opacity);
      
      final path = Path();
      bool isFirstPoint = true;
      
      // Рисуем волнистую линию по всей ширине
      for (double x = 0; x <= size.width; x += 1) {
        final waveY = y + math.sin(x * frequency + phase + animationValue * math.pi) * amplitude;
        
        if (isFirstPoint) {
          path.moveTo(x, waveY);
          isFirstPoint = false;
        } else {
          path.lineTo(x, waveY);
        }
      }
      
      canvas.drawPath(path, paint);
    }
    
    // Добавляем дополнительные более тонкие линии для глубины
    _drawSubtleLines(canvas, size);
  }

  void _drawSubtleLines(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.3
      ..blendMode = BlendMode.screen;

    final random = math.Random(123);
    
    for (int i = 0; i < 150; i++) {
      final y = (i * size.height / 150) + (animationValue * size.height * 1.5) % size.height;
      
      if (y < -5 || y > size.height + 5) continue;
      
      final amplitude = random.nextDouble() * 4 + 1;
      final frequency = random.nextDouble() * 0.015 + 0.003;
      final phase = random.nextDouble() * math.pi * 2;
      final opacity = (random.nextDouble() * 0.08 + 0.02).clamp(0.0, 1.0);
      
      paint.color = Color.fromRGBO(200, 200, 200, opacity);
      
      final path = Path();
      bool isFirstPoint = true;
      
      for (double x = 0; x <= size.width; x += 2) {
        final waveY = y + math.sin(x * frequency + phase + animationValue * math.pi * 0.7) * amplitude;
        
        if (isFirstPoint) {
          path.moveTo(x, waveY);
          isFirstPoint = false;
        } else {
          path.lineTo(x, waveY);
        }
      }
      
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant SubtleWavePainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
} 