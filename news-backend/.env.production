# Продакшн конфигурация
NODE_ENV=production
PORT=4000

# API ключи (заполните своими значениями)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
NEWS_API_KEY=your_news_api_key_here
CRYPTO_COMPARE_API_KEY=your_crypto_compare_api_key_here

# Настройки базы данных
REDIS_URL=redis://redis:6379
CACHE_TTL=3600

# Настройки логирования
LOG_LEVEL=info
LOG_FILE=/app/data/app.log

# Настройки безопасности
CORS_ORIGIN=https://your-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Настройки парсинга
PARSE_INTERVAL_MINUTES=30
MAX_NEWS_CACHE_SIZE=4000  # Увеличиваем с 1000 до 4000
AI_ANALYSIS_TIMEOUT_MS=30000

# Мониторинг
ENABLE_METRICS=true
METRICS_PORT=9091

# SSE настройки
SSE_HEARTBEAT_INTERVAL=30000
MAX_SSE_CONNECTIONS=1000
