# Периодическое обновление новостей

## Новые возможности

### 1. Автоматическое обновление каждые 10 минут
- После завершения анализа новостей автоматически планируется следующий запуск через 10 минут
- Система работает непрерывно, обеспечивая постоянный поток свежих новостей
- При ошибках система автоматически перепланирует следующий запуск

### 2. Сохранение последних 30 новостей при перезапуске
- При запуске приложения всегда загружаются последние 30 новостей из кэша
- Новости автоматически сортируются по дате публикации
- В файле кэша сохраняется до 100 новостей для истории

## Технические детали

### Backend изменения

#### `newsService.js`
- Добавлен таймер `periodicNewsTimer` для планирования обновлений
- Функция `scheduleNextNewsUpdate()` планирует следующий запуск через 10 минут
- Функция `stopPeriodicNewsUpdates()` корректно останавливает таймер
- `loadNewsFeedCache()` теперь возвращает только последние 30 новостей
- `saveNewsFeedCache()` сохраняет до 100 новостей, отсортированных по дате

#### `index.js`
- Добавлены обработчики сигналов SIGINT и SIGTERM для корректного завершения
- При запуске сервера автоматически активируется периодическое обновление

### Frontend изменения

#### `news_service.dart`
- `getAllNews()` по умолчанию запрашивает 30 новостей
- Добавлена обработка ошибок для предотвращения краша приложения

#### `news_provider.dart`
- `fetchAllNews()` загружает 30 новостей при инициализации
- Ограничение real-time новостей установлено в 50 для оптимальной производительности

## Использование

### Запуск backend'а
```bash
cd flutter/news-backend
npm run dev
```

### Тестирование периодических обновлений
```bash
node test_periodic_updates.js
```

### Мониторинг логов
Backend выводит подробные логи:
- `[SCHEDULER]` - события планировщика
- `[PROCESS]` - процесс анализа новостей
- `[DEBUG]` - отладочная информация

## Конфигурация

### Изменение интервала обновления
В файле `newsService.js` измените значение:
```javascript
}, 10 * 60 * 1000); // 10 минут в миллисекундах
```

### Изменение количества сохраняемых новостей
В функции `loadNewsFeedCache()`:
```javascript
.slice(0, 800); // Количество новостей при загрузке (увеличено с 200)
```

В функции `saveNewsFeedCache()`:
```javascript
.slice(0, 800); // Количество новостей в кэше (увеличено с 200)
```

## Мониторинг

### Проверка статуса
```bash
curl http://localhost:4000/status
```

### Ручной запуск парсинга
```bash
curl -X POST http://localhost:4000/admin/parse
```

### Очистка кэша
```bash
curl -X POST http://localhost:4000/admin/clear
```

## Логи и отладка

Система выводит подробные логи для мониторинга:
- Время следующего обновления
- Количество обработанных новостей
- Ошибки и их обработка
- Статус подключенных клиентов

## Производительность

- Новости анализируются в очереди с ограничением скорости
- Кэш автоматически ограничивается для экономии памяти
- Real-time обновления оптимизированы для быстрой доставки

## Безопасность

- Периодические обновления останавливаются при завершении сервера
- Обработка ошибок предотвращает зависание системы
- Ограничения на количество новостей предотвращают переполнение памяти
