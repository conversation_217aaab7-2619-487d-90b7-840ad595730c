// Скрипт для проверки текущей ленты новостей
const axios = require('axios');

async function checkCurrentFeed() {
  console.log('🔍 Checking Current News Feed\n');
  console.log('=' .repeat(60));

  try {
    // Проверяем API
    const response = await axios.get('http://localhost:4000/api/news?page=1&pageSize=10');
    const data = response.data;

    console.log(`📊 Total news in feed: ${data.total}`);
    console.log(`📄 Current page: ${data.page}/${data.totalPages}`);
    console.log(`📰 News on this page: ${data.news.length}\n`);

    if (data.news.length === 0) {
      console.log('❌ No news found in feed');
      return;
    }

    console.log('📰 Current News Items:');
    console.log('─'.repeat(60));

    const seenTitles = new Set();
    const seenSources = new Map();
    let duplicateCount = 0;

    data.news.forEach((news, index) => {
      const titleKey = news.title.toLowerCase().trim();
      const sourceKey = `${news.source}-${titleKey}`;
      
      let duplicateFlag = '';
      if (seenTitles.has(titleKey)) {
        duplicateFlag = ' 🔄 [POTENTIAL DUPLICATE]';
        duplicateCount++;
      }
      
      seenTitles.add(titleKey);
      
      if (seenSources.has(sourceKey)) {
        duplicateFlag += ' 🚨 [EXACT DUPLICATE]';
      }
      seenSources.set(sourceKey, true);

      console.log(`\n${index + 1}. 📰 "${news.title.slice(0, 60)}..."`);
      console.log(`   📅 ${new Date(news.publishedAt).toLocaleString()}`);
      console.log(`   📡 Source: ${news.source}`);
      console.log(`   🎯 Sentiment: ${news.sentiment || 'N/A'}`);
      console.log(`   📏 Content: ${news.rewrittenContent ? news.rewrittenContent.length : 0} chars`);
      console.log(`   📝 Summary: ${news.summary ? news.summary.length : 0} chars`);
      
      if (duplicateFlag) {
        console.log(`   ${duplicateFlag}`);
      }

      // Проверяем качество контента
      if (!news.rewrittenContent || news.rewrittenContent.length < 200) {
        console.log(`   ⚠️  [SHORT CONTENT]`);
      }
      
      if (!news.summary || news.summary.length < 50) {
        console.log(`   ⚠️  [SHORT SUMMARY]`);
      }
    });

    console.log('\n' + '=' .repeat(60));
    console.log('📊 ANALYSIS SUMMARY:');
    console.log(`   • Total news items: ${data.news.length}`);
    console.log(`   • Potential duplicates: ${duplicateCount}`);
    console.log(`   • Unique titles: ${seenTitles.size}`);
    
    // Анализ источников
    const sourceCounts = {};
    data.news.forEach(news => {
      sourceCounts[news.source] = (sourceCounts[news.source] || 0) + 1;
    });
    
    console.log(`   • Sources: ${Object.keys(sourceCounts).length}`);
    Object.entries(sourceCounts).forEach(([source, count]) => {
      console.log(`     - ${source}: ${count} news`);
    });

    // Анализ sentiment
    const sentimentCounts = {};
    data.news.forEach(news => {
      const sentiment = news.sentiment || 'unknown';
      sentimentCounts[sentiment] = (sentimentCounts[sentiment] || 0) + 1;
    });
    
    console.log(`   • Sentiment distribution:`);
    Object.entries(sentimentCounts).forEach(([sentiment, count]) => {
      console.log(`     - ${sentiment}: ${count} news`);
    });

    // Проверка качества контента
    const contentQuality = {
      hasRewrittenContent: 0,
      hasSummary: 0,
      hasAiTitle: 0,
      shortContent: 0,
      shortSummary: 0
    };

    data.news.forEach(news => {
      if (news.rewrittenContent && news.rewrittenContent.length > 0) {
        contentQuality.hasRewrittenContent++;
      }
      if (news.summary && news.summary.length > 0) {
        contentQuality.hasSummary++;
      }
      if (news.aiGeneratedTitle && news.aiGeneratedTitle.length > 0) {
        contentQuality.hasAiTitle++;
      }
      if (!news.rewrittenContent || news.rewrittenContent.length < 200) {
        contentQuality.shortContent++;
      }
      if (!news.summary || news.summary.length < 50) {
        contentQuality.shortSummary++;
      }
    });

    console.log(`   • Content quality:`);
    console.log(`     - Has rewritten content: ${contentQuality.hasRewrittenContent}/${data.news.length}`);
    console.log(`     - Has summary: ${contentQuality.hasSummary}/${data.news.length}`);
    console.log(`     - Has AI title: ${contentQuality.hasAiTitle}/${data.news.length}`);
    console.log(`     - Short content: ${contentQuality.shortContent}/${data.news.length}`);
    console.log(`     - Short summary: ${contentQuality.shortSummary}/${data.news.length}`);

    if (duplicateCount > 0) {
      console.log('\n⚠️  DUPLICATE DETECTION ISSUES FOUND!');
      console.log('   Consider running deduplication again or checking the logic.');
    } else {
      console.log('\n✅ NO DUPLICATES DETECTED!');
      console.log('   Deduplication is working correctly.');
    }

    if (contentQuality.shortContent > data.news.length * 0.3) {
      console.log('\n⚠️  CONTENT LENGTH ISSUES FOUND!');
      console.log('   Many news items have short rewritten content.');
    } else {
      console.log('\n✅ CONTENT LENGTH LOOKS GOOD!');
      console.log('   Most news items have substantial rewritten content.');
    }

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server is not running. Please start the server first:');
      console.log('   npm start');
    } else {
      console.error('❌ Error checking feed:', error.message);
    }
  }
}

// Запускаем проверку
if (require.main === module) {
  checkCurrentFeed().catch(error => {
    console.error('❌ Feed check failed:', error);
    process.exit(1);
  });
}

module.exports = {
  checkCurrentFeed
};
