#!/usr/bin/env node

const NodeCache = require('node-cache');
const fs = require('fs');
const path = require('path');

console.log('🧹 Очистка кеша...');

// Очищаем кеш новостей
const newsCache = new NodeCache();
newsCache.flushAll();
console.log('✅ Кеш новостей очищен');

// Очищаем кеш анализа
const analysisCache = new NodeCache();
analysisCache.flushAll();
console.log('✅ Кеш анализа очищен');

// Очищаем кеш лимитов источников
const sourceLimitCache = new NodeCache();
sourceLimitCache.flushAll();
console.log('✅ Кеш лимитов источников очищен');

// Очищаем временные файлы
const tempDir = path.join(__dirname, 'temp');
if (fs.existsSync(tempDir)) {
  fs.rmSync(tempDir, { recursive: true, force: true });
  console.log('✅ Временные файлы удалены');
}

console.log('🎉 Кеш полностью очищен!');
