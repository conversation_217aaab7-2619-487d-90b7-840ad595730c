{"name": "news-backend", "version": "1.0.0", "description": "Backend for news analysis system", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "prod": "NODE_ENV=production node src/index.js", "docker:build": "docker build -t news-backend .", "docker:run": "docker run -p 4000:4000 --env-file .env.production news-backend"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@mozilla/readability": "^0.6.0", "axios": "^1.6.7", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "fast-levenshtein": "^3.0.0", "helmet": "^7.1.0", "jsdom": "^26.1.0", "node-cache": "^5.1.2", "node-cron": "^4.1.0", "node-readability": "^3.0.0", "p-queue": "^7.4.1"}, "devDependencies": {"nodemon": "^3.0.3"}}