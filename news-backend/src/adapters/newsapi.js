const axios = require('axios');
const { Readability } = require('@mozilla/readability');
const { JSDOM } = require('jsdom');
const cheerio = require('cheerio');
const { getTraditionalFinanceDomains, getTraditionalFinanceKeywords } = require('../config/newsSourcesConfig');
require('dotenv').config();

const API_KEY = process.env.NEWS_API_KEY;
const BASE_URL = 'https://newsapi.org/v2/top-headlines';

async function extractFullContent(url) {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
      },
      timeout: 10000,
    });

    const $ = cheerio.load(response.data);
    
    // Удаляем ненужные элементы
    $('script, style, iframe, nav, footer, header, .ad, .advertisement, .social-share, .comments').remove();
    
    // Создаем DOM для Readability
    const dom = new JSDOM($.html());
    const reader = new Readability(dom.window.document);
    const article = reader.parse();
    
    if (article && article.textContent) {
      return {
        content: article.textContent.trim(),
        title: article.title,
        excerpt: article.excerpt,
        byline: article.byline,
        length: article.textContent.length
      };
    }
    
    // Fallback: если Readability не сработал, пробуем найти основной контент
    const mainContent = $('article, .article, .post, .content, .main-content, #content, .entry-content')
      .first()
      .text()
      .trim();
      
    if (mainContent) {
      return {
        content: mainContent,
        title: $('title').text(),
        length: mainContent.length
      };
    }
    
    return null;
  } catch (e) {
    console.error(`[NewsAPI] Ошибка извлечения контента для ${url}:`, e.message);
    return null;
  }
}

async function fetchNewsApiNews({ country = 'us', category = 'business', pageSize = 100 } = {}) {
  try {
    const response = await axios.get(BASE_URL, {
      params: {
        country,
        category,
        pageSize,
        apiKey: API_KEY,
      },
    });
    
    if (!response.data?.articles?.length) {
      console.log('[NewsAPI] Нет новостей в ответе');
      return [];
    }

    const newsPromises = response.data.articles.map(async (item, idx) => {
      try {
        // Получаем полный контент
        const fullContent = await extractFullContent(item.url);
        
        if (!fullContent) {
          console.log(`[NewsAPI] Не удалось получить полный контент для: ${item.title}`);
          return null;
        }
        
        return {
          id: item.url || `${item.title}_${idx}`,
          title: fullContent.title || item.title,
          description: item.description || '',
          publishedAt: item.publishedAt,
          source: item.source?.name || '',
          url: item.url,
          tags: [],
          content: fullContent.content,
          imageUrl: item.urlToImage,
          author: fullContent.byline || item.author,
          wordCount: fullContent.length,
          excerpt: fullContent.excerpt
        };
      } catch (error) {
        console.error(`[NewsAPI] Ошибка обработки новости "${item.title}":`, error.message);
        return null;
      }
    });

    const newsResults = await Promise.all(newsPromises);
    const validNews = newsResults.filter(item => item !== null);
    
    console.log('[NewsAPI] Статистика:', {
      получено: response.data.articles.length,
      сПолнымКонтентом: validNews.length,
      пропущено: response.data.articles.length - validNews.length
    });

    return validNews;
  } catch (e) {
    console.error('[NewsAPI] Ошибка получения новостей:', e.message);
    return [];
  }
}

// Функция для получения стоковых новостей
async function fetchStockNews({ pageSize = 50 } = {}) {
  console.log('[NewsAPI-Stocks] 📈 Fetching stock market news...');

  // Используем конфигурацию для получения ключевых слов и доменов
  const stockKeywords = getTraditionalFinanceKeywords().filter(keyword =>
    keyword.includes('stock') || keyword.includes('market') ||
    keyword.includes('earnings') || keyword.includes('IPO')
  );

  const domains = getTraditionalFinanceDomains().join(',');

  console.log(`[NewsAPI-Stocks] 🎯 Using ${stockKeywords.length} stock keywords and ${getTraditionalFinanceDomains().length} domains`);

  const allStockNews = [];

  for (const query of stockKeywords.slice(0, 4)) { // Ограничиваем количество запросов
    try {
      const response = await axios.get('https://newsapi.org/v2/everything', {
        params: {
          q: query,
          language: 'en',
          sortBy: 'publishedAt',
          pageSize: Math.floor(pageSize / 4),
          apiKey: API_KEY,
          domains: domains
        },
      });

      if (response.data?.articles?.length) {
        const processedNews = await Promise.all(
          response.data.articles.map(async (item, idx) => {
            try {
              const fullContent = await extractFullContent(item.url);
              if (!fullContent) return null;

              return {
                id: `stock_${query.replace(/\s+/g, '_')}_${idx}_${Date.now()}`,
                title: fullContent.title || item.title,
                description: item.description || '',
                publishedAt: item.publishedAt,
                source: item.source?.name || '',
                url: item.url,
                tags: ['stocks', 'finance', query.toLowerCase()],
                content: fullContent.content,
                imageUrl: item.urlToImage,
                author: fullContent.byline || item.author,
                wordCount: fullContent.length,
                category: 'stocks'
              };
            } catch (error) {
              return null;
            }
          })
        );

        allStockNews.push(...processedNews.filter(item => item !== null));
      }
    } catch (error) {
      console.error(`[NewsAPI-Stocks] Error fetching for query "${query}":`, error.message);
    }
  }

  console.log(`[NewsAPI-Stocks] Fetched ${allStockNews.length} stock news`);
  return allStockNews;
}

// Функция для получения политических новостей, влияющих на экономику
async function fetchPoliticalEconomicNews({ pageSize = 30 } = {}) {
  console.log('[NewsAPI-Politics] 🏛️ Fetching political/economic news...');

  // Используем конфигурацию для получения политических и экономических ключевых слов
  const politicalKeywords = getTraditionalFinanceKeywords().filter(keyword =>
    keyword.includes('trade') || keyword.includes('sanction') ||
    keyword.includes('policy') || keyword.includes('regulation') ||
    keyword.includes('Federal Reserve') || keyword.includes('inflation')
  );

  const domains = getTraditionalFinanceDomains().join(',');

  console.log(`[NewsAPI-Politics] 🎯 Using ${politicalKeywords.length} political/economic keywords`);

  const allPoliticalNews = [];

  for (const query of politicalKeywords.slice(0, 3)) { // Ограничиваем количество запросов
    try {
      const response = await axios.get('https://newsapi.org/v2/everything', {
        params: {
          q: query,
          language: 'en',
          sortBy: 'publishedAt',
          pageSize: Math.floor(pageSize / 3),
          apiKey: API_KEY,
          domains: domains
        },
      });

      if (response.data?.articles?.length) {
        const processedNews = await Promise.all(
          response.data.articles.map(async (item, idx) => {
            try {
              const fullContent = await extractFullContent(item.url);
              if (!fullContent) return null;

              return {
                id: `politics_${query.replace(/\s+/g, '_')}_${idx}_${Date.now()}`,
                title: fullContent.title || item.title,
                description: item.description || '',
                publishedAt: item.publishedAt,
                source: item.source?.name || '',
                url: item.url,
                tags: ['politics', 'economy', query.toLowerCase()],
                content: fullContent.content,
                imageUrl: item.urlToImage,
                author: fullContent.byline || item.author,
                wordCount: fullContent.length,
                category: 'politics'
              };
            } catch (error) {
              return null;
            }
          })
        );

        allPoliticalNews.push(...processedNews.filter(item => item !== null));
      }
    } catch (error) {
      console.error(`[NewsAPI-Politics] Error fetching for query "${query}":`, error.message);
    }
  }

  console.log(`[NewsAPI-Politics] Fetched ${allPoliticalNews.length} political/economic news`);
  return allPoliticalNews;
}

// Объединенная функция для получения всех традиционных финансовых новостей
async function fetchAllTraditionalFinanceNews() {
  console.log('[NewsAPI-Traditional] Fetching all traditional finance news...');

  const [
    businessNews,
    stockNews,
    politicalNews
  ] = await Promise.allSettled([
    fetchNewsApiNews({ category: 'business', pageSize: 40 }),
    fetchStockNews({ pageSize: 50 }),
    fetchPoliticalEconomicNews({ pageSize: 30 })
  ]);

  const allNews = [
    ...(businessNews.status === 'fulfilled' ? businessNews.value : []),
    ...(stockNews.status === 'fulfilled' ? stockNews.value : []),
    ...(politicalNews.status === 'fulfilled' ? politicalNews.value : [])
  ];

  console.log(`[NewsAPI-Traditional] Total fetched: ${allNews.length} traditional finance news`);
  return allNews;
}

module.exports = {
  fetchNewsApiNews,
  fetchStockNews,
  fetchPoliticalEconomicNews,
  fetchAllTraditionalFinanceNews
};