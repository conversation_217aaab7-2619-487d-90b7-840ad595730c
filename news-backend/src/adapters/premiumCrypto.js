const axios = require('axios');
const cheerio = require('cheerio');
const { Readability } = require('@mozilla/readability');
const { JSDOM } = require('jsdom');
require('dotenv').config();

// API Keys
const COINAPI_KEY = process.env.COINAPI_KEY;
const MESSARI_KEY = process.env.MESSARI_API_KEY;

// Функция для извлечения полного контента статьи
async function extractFullContent(url) {
  try {
    console.log(`[CONTENT] Извлекаем полный контент: ${url}`);

    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
      },
      timeout: 15000,
    });

    const $ = cheerio.load(response.data);

    // Удаляем ненужные элементы
    $('script, style, iframe, nav, footer, header, .ad, .advertisement, .social-share, .comments, .newsletter, .sidebar').remove();

    // Создаем DOM для Readability
    const dom = new JSDOM($.html());
    const reader = new Readability(dom.window.document);
    const article = reader.parse();

    if (article && article.textContent && article.textContent.length > 500) {
      console.log(`[CONTENT] ✅ Извлечен полный контент: ${article.textContent.length} символов`);
      return {
        content: article.textContent.trim(),
        title: article.title,
        excerpt: article.excerpt,
        byline: article.byline,
        length: article.textContent.length
      };
    }

    // Fallback: специфичные селекторы для разных сайтов
    let mainContent = '';

    // Для Decrypt.co
    if (url.includes('decrypt.co')) {
      mainContent = $('.post-content, .entry-content, .article-content, .content, article').first().text().trim();
    }
    // Для CoinDesk
    else if (url.includes('coindesk.com')) {
      mainContent = $('.articleBody, .at-content, .entry-content, .post-content, .article-content, article, [data-module="ArticleBody"]').first().text().trim();
    }
    // Для Cointelegraph
    else if (url.includes('cointelegraph.com')) {
      mainContent = $('.post-content, .post__content, .article-content, article').first().text().trim();
    }
    // Для Coinotag
    else if (url.includes('coinotag.com')) {
      mainContent = $('.entry-content, .post-content, .article-content, .content, article, .single-post-content').first().text().trim();
    }
    // Для TheBlock
    else if (url.includes('theblock.co')) {
      mainContent = $('.articleBody, .post-content, .entry-content, article').first().text().trim();
    }
    // Для CryptoSlate
    else if (url.includes('cryptoslate.com')) {
      mainContent = $('.post-content, .entry-content, .article-content, article').first().text().trim();
    }
    // Для Bitcoin Magazine
    else if (url.includes('bitcoinmagazine.com')) {
      mainContent = $('.post-content, .entry-content, .article-content, article').first().text().trim();
    }
    // Общий fallback
    else {
      mainContent = $('article, .article, .post, .content, .main-content, #content, .entry-content, .post-content')
        .first()
        .text()
        .trim();
    }

    if (mainContent && mainContent.length > 500) {
      console.log(`[CONTENT] ✅ Fallback контент извлечен: ${mainContent.length} символов`);
      return {
        content: mainContent,
        title: $('title').text() || '',
        excerpt: mainContent.slice(0, 300) + '...',
        byline: $('.author, .byline, .post-author').first().text() || '',
        length: mainContent.length
      };
    }

    console.log(`[CONTENT] ⚠️  Не удалось извлечь достаточно контента для: ${url}`);
    return null;

  } catch (error) {
    console.error(`[CONTENT] ❌ Ошибка извлечения контента для ${url}:`, error.message);
    return null;
  }
}

// CoinDesk RSS Feed Parser
async function fetchCoinDeskNews() {
  try {
    console.log('[CoinDesk] Начинаем загрузку новостей...');
    const response = await axios.get('https://www.coindesk.com/arc/outboundfeeds/rss/');
    const $ = cheerio.load(response.data, { xmlMode: true });

    const newsItems = [];
    $('item').each((idx, element) => {
      const $item = $(element);
      newsItems.push({
        id: `coindesk_${$item.find('guid').text()}_${idx}`,
        title: $item.find('title').text(),
        description: $item.find('description').text(),
        publishedAt: new Date($item.find('pubDate').text()).toISOString(),
        source: 'CoinDesk',
        url: $item.find('link').text(),
        tags: [],
        sourceQuality: 10,
        category: 'crypto'
      });
    });

    console.log(`[CoinDesk] Найдено ${newsItems.length} новостей, извлекаем полный контент...`);

    // Извлекаем полный контент для первых 20 новостей
    const newsPromises = newsItems.slice(0, 20).map(async (item, index) => {
      try {
        console.log(`[CoinDesk] ${index + 1}/20 Обрабатываем: ${item.title.slice(0, 50)}...`);

        const fullContent = await extractFullContent(item.url);

        if (fullContent && fullContent.content && fullContent.content.length > 500) {
          return {
            ...item,
            content: fullContent.content,
            title: fullContent.title || item.title,
            author: fullContent.byline,
            excerpt: fullContent.excerpt,
            wordCount: fullContent.length
          };
        } else {
          return {
            ...item,
            content: item.description
          };
        }
      } catch (error) {
        console.error(`[CoinDesk] Ошибка обработки ${item.title}:`, error.message);
        return {
          ...item,
          content: item.description
        };
      }
    });

    const news = await Promise.all(newsPromises);
    const validNews = news.filter(item => item !== null);

    console.log(`[CoinDesk] ✅ Обработано ${validNews.length} новостей с полным контентом`);
    return validNews;

  } catch (error) {
    console.error('[CoinDesk] Error fetching news:', error.message);
    return [];
  }
}

// Cointelegraph RSS Feed Parser
async function fetchCointelegraphNews() {
  try {
    console.log('[Cointelegraph] Начинаем загрузку новостей...');
    const response = await axios.get('https://cointelegraph.com/rss');
    const $ = cheerio.load(response.data, { xmlMode: true });

    const newsItems = [];
    $('item').each((idx, element) => {
      const $item = $(element);
      newsItems.push({
        id: `cointelegraph_${$item.find('guid').text()}_${idx}`,
        title: $item.find('title').text(),
        description: $item.find('description').text(),
        publishedAt: new Date($item.find('pubDate').text()).toISOString(),
        source: 'Cointelegraph',
        url: $item.find('link').text(),
        tags: [],
        sourceQuality: 10,
        category: 'crypto'
      });
    });

    console.log(`[Cointelegraph] Найдено ${newsItems.length} новостей, извлекаем полный контент...`);

    // Извлекаем полный контент для первых 15 новостей
    const newsPromises = newsItems.slice(0, 15).map(async (item, index) => {
      try {
        console.log(`[Cointelegraph] ${index + 1}/15 Обрабатываем: ${item.title.slice(0, 50)}...`);

        const fullContent = await extractFullContent(item.url);

        if (fullContent && fullContent.content && fullContent.content.length > 500) {
          return {
            ...item,
            content: fullContent.content,
            title: fullContent.title || item.title,
            author: fullContent.byline,
            excerpt: fullContent.excerpt,
            wordCount: fullContent.length
          };
        } else {
          return {
            ...item,
            content: item.description
          };
        }
      } catch (error) {
        console.error(`[Cointelegraph] Ошибка обработки ${item.title}:`, error.message);
        return {
          ...item,
          content: item.description
        };
      }
    });

    const processedNews = await Promise.all(newsPromises);
    console.log(`[Cointelegraph] ✅ Обработано ${processedNews.length} новостей`);
    return processedNews;

  } catch (error) {
    console.error('[Cointelegraph] Error fetching news:', error.message);
    return [];
  }
}

// The Block RSS Feed Parser
async function fetchTheBlockNews() {
  try {
    console.log('[TheBlock] Начинаем загрузку новостей...');
    const response = await axios.get('https://www.theblock.co/rss.xml');
    const $ = cheerio.load(response.data, { xmlMode: true });

    const newsItems = [];
    $('item').each((idx, element) => {
      const $item = $(element);
      newsItems.push({
        id: `theblock_${$item.find('guid').text()}_${idx}`,
        title: $item.find('title').text(),
        description: $item.find('description').text(),
        publishedAt: new Date($item.find('pubDate').text()).toISOString(),
        source: 'The Block',
        url: $item.find('link').text(),
        tags: [],
        sourceQuality: 10,
        category: 'crypto'
      });
    });

    console.log(`[TheBlock] Найдено ${newsItems.length} новостей, извлекаем полный контент...`);

    // Извлекаем полный контент для первых 15 новостей
    const newsPromises = newsItems.slice(0, 15).map(async (item, index) => {
      try {
        console.log(`[TheBlock] ${index + 1}/15 Обрабатываем: ${item.title.slice(0, 50)}...`);

        const fullContent = await extractFullContent(item.url);

        if (fullContent && fullContent.content && fullContent.content.length > 500) {
          return {
            ...item,
            content: fullContent.content,
            title: fullContent.title || item.title,
            author: fullContent.byline,
            excerpt: fullContent.excerpt,
            wordCount: fullContent.length
          };
        } else {
          return {
            ...item,
            content: item.description
          };
        }
      } catch (error) {
        console.error(`[TheBlock] Ошибка обработки ${item.title}:`, error.message);
        return {
          ...item,
          content: item.description
        };
      }
    });

    const processedNews = await Promise.all(newsPromises);
    console.log(`[TheBlock] ✅ Обработано ${processedNews.length} новостей`);
    return processedNews;

  } catch (error) {
    console.error('[TheBlock] Error fetching news:', error.message);
    return [];
  }
}

// Decrypt RSS Feed Parser
async function fetchDecryptNews() {
  try {
    console.log('[Decrypt] Начинаем загрузку новостей...');
    const response = await axios.get('https://decrypt.co/feed');
    const $ = cheerio.load(response.data, { xmlMode: true });

    const newsItems = [];
    $('item').each((idx, element) => {
      const $item = $(element);
      newsItems.push({
        id: `decrypt_${$item.find('guid').text()}_${idx}`,
        title: $item.find('title').text(),
        description: $item.find('description').text(),
        publishedAt: new Date($item.find('pubDate').text()).toISOString(),
        source: 'Decrypt',
        url: $item.find('link').text(),
        tags: [],
        sourceQuality: 10,
        category: 'crypto'
      });
    });

    console.log(`[Decrypt] Найдено ${newsItems.length} новостей, извлекаем полный контент...`);

    // Извлекаем полный контент для каждой новости
    const newsPromises = newsItems.slice(0, 15).map(async (item, index) => {
      try {
        console.log(`[Decrypt] ${index + 1}/15 Обрабатываем: ${item.title.slice(0, 50)}...`);

        // Получаем полный контент
        const fullContent = await extractFullContent(item.url);

        if (fullContent && fullContent.content && fullContent.content.length > 500) {
          return {
            ...item,
            content: fullContent.content,
            title: fullContent.title || item.title,
            author: fullContent.byline,
            excerpt: fullContent.excerpt,
            wordCount: fullContent.length
          };
        } else {
          console.log(`[Decrypt] ⚠️  Используем краткое описание для: ${item.title.slice(0, 50)}...`);
          return {
            ...item,
            content: item.description // Fallback к описанию
          };
        }
      } catch (error) {
        console.error(`[Decrypt] Ошибка обработки ${item.title}:`, error.message);
        return {
          ...item,
          content: item.description // Fallback к описанию
        };
      }
    });

    const news = await Promise.all(newsPromises);
    const validNews = news.filter(item => item !== null);

    console.log(`[Decrypt] ✅ Обработано ${validNews.length} новостей с полным контентом`);
    return validNews;

  } catch (error) {
    console.error('[Decrypt] Error fetching news:', error.message);
    return [];
  }
}

// Messari News API
async function fetchMessariNews() {
  if (!MESSARI_KEY || MESSARI_KEY === 'your_messari_key_here') {
    console.log('[Messari] API key not configured');
    return [];
  }

  try {
    const response = await axios.get('https://data.messari.io/api/v1/news', {
      headers: {
        'x-messari-api-key': MESSARI_KEY
      },
      params: {
        page: 1,
        limit: 30
      }
    });

    if (!response.data?.data?.length) {
      console.log('[Messari] No news in response');
      return [];
    }

    return response.data.data.map((item, idx) => ({
      id: `messari_${item.id}_${idx}`,
      title: item.title,
      description: item.content,
      publishedAt: item.published_at,
      source: 'Messari',
      url: item.url,
      tags: item.tags || [],
      content: item.content,
      author: item.author?.name,
      sourceQuality: 9,
      category: 'crypto'
    }));

  } catch (error) {
    console.error('[Messari] Error fetching news:', error.message);
    return [];
  }
}

// CryptoSlate RSS Feed Parser
async function fetchCryptoSlateNews() {
  try {
    console.log('[CryptoSlate] Начинаем загрузку новостей...');
    const response = await axios.get('https://cryptoslate.com/feed/');
    const $ = cheerio.load(response.data, { xmlMode: true });

    const newsItems = [];
    $('item').each((idx, element) => {
      const $item = $(element);
      newsItems.push({
        id: `cryptoslate_${$item.find('guid').text()}_${idx}`,
        title: $item.find('title').text(),
        description: $item.find('description').text(),
        publishedAt: new Date($item.find('pubDate').text()).toISOString(),
        source: 'CryptoSlate',
        url: $item.find('link').text(),
        tags: [],
        sourceQuality: 9,
        category: 'crypto'
      });
    });

    console.log(`[CryptoSlate] Найдено ${newsItems.length} новостей, извлекаем полный контент...`);

    // Извлекаем полный контент для первых 15 новостей
    const newsPromises = newsItems.slice(0, 15).map(async (item, index) => {
      try {
        console.log(`[CryptoSlate] ${index + 1}/15 Обрабатываем: ${item.title.slice(0, 50)}...`);

        const fullContent = await extractFullContent(item.url);

        if (fullContent && fullContent.content && fullContent.content.length > 500) {
          return {
            ...item,
            content: fullContent.content,
            title: fullContent.title || item.title,
            author: fullContent.byline,
            excerpt: fullContent.excerpt,
            wordCount: fullContent.length
          };
        } else {
          return {
            ...item,
            content: item.description
          };
        }
      } catch (error) {
        console.error(`[CryptoSlate] Ошибка обработки ${item.title}:`, error.message);
        return {
          ...item,
          content: item.description
        };
      }
    });

    const processedNews = await Promise.all(newsPromises);
    console.log(`[CryptoSlate] ✅ Обработано ${processedNews.length} новостей`);
    return processedNews;

  } catch (error) {
    console.error('[CryptoSlate] Error fetching news:', error.message);
    return [];
  }
}

// Bitcoin Magazine RSS Feed Parser
async function fetchBitcoinMagazineNews() {
  try {
    console.log('[BitcoinMagazine] Начинаем загрузку новостей...');
    const response = await axios.get('https://bitcoinmagazine.com/.rss/full/');
    const $ = cheerio.load(response.data, { xmlMode: true });

    const newsItems = [];
    $('item').each((idx, element) => {
      const $item = $(element);
      newsItems.push({
        id: `bitcoinmagazine_${$item.find('guid').text()}_${idx}`,
        title: $item.find('title').text(),
        description: $item.find('description').text(),
        publishedAt: new Date($item.find('pubDate').text()).toISOString(),
        source: 'Bitcoin Magazine',
        url: $item.find('link').text(),
        tags: [],
        sourceQuality: 10,
        category: 'crypto'
      });
    });

    console.log(`[BitcoinMagazine] Найдено ${newsItems.length} новостей, извлекаем полный контент...`);

    // Извлекаем полный контент для первых 10 новостей
    const newsPromises = newsItems.slice(0, 10).map(async (item, index) => {
      try {
        console.log(`[BitcoinMagazine] ${index + 1}/10 Обрабатываем: ${item.title.slice(0, 50)}...`);

        const fullContent = await extractFullContent(item.url);

        if (fullContent && fullContent.content && fullContent.content.length > 500) {
          return {
            ...item,
            content: fullContent.content,
            title: fullContent.title || item.title,
            author: fullContent.byline,
            excerpt: fullContent.excerpt,
            wordCount: fullContent.length
          };
        } else {
          return {
            ...item,
            content: item.description
          };
        }
      } catch (error) {
        console.error(`[BitcoinMagazine] Ошибка обработки ${item.title}:`, error.message);
        return {
          ...item,
          content: item.description
        };
      }
    });

    const processedNews = await Promise.all(newsPromises);
    console.log(`[BitcoinMagazine] ✅ Обработано ${processedNews.length} новостей`);
    return processedNews;

  } catch (error) {
    console.error('[BitcoinMagazine] Error fetching news:', error.message);
    return [];
  }
}

// Coinotag RSS Feed Parser
async function fetchConotagNews() {
  try {
    console.log('[Coinotag] Начинаем загрузку новостей...');
    const response = await axios.get('https://en.coinotag.com/feed/');
    const $ = cheerio.load(response.data, { xmlMode: true });

    const newsItems = [];
    $('item').each((idx, element) => {
      const $item = $(element);
      const title = $item.find('title').text();
      const description = $item.find('description').text();
      const url = $item.find('link').text();

      if (title && url && description.length > 100) {
        newsItems.push({
          id: `coinotag_${$item.find('guid').text()}_${idx}`,
          title: title,
          description: description,
          publishedAt: new Date($item.find('pubDate').text()).toISOString(),
          source: 'Coinotag',
          url: url,
          tags: [],
          sourceQuality: 8,
          category: 'crypto'
        });
      }
    });

    console.log(`[Coinotag] Найдено ${newsItems.length} новостей`);

    // Извлекаем полный контент для первых 15 новостей
    const newsPromises = newsItems.slice(0, 15).map(async (item, index) => {
      try {
        console.log(`[Coinotag] ${index + 1}/15 Обрабатываем: ${item.title.slice(0, 50)}...`);

        const fullContent = await extractFullContent(item.url);

        if (fullContent && fullContent.content && fullContent.content.length > 500) {
          return {
            ...item,
            content: fullContent.content,
            title: fullContent.title || item.title,
            author: fullContent.byline,
            excerpt: fullContent.excerpt,
            wordCount: fullContent.length
          };
        } else {
          return {
            ...item,
            content: item.description
          };
        }
      } catch (error) {
        console.error(`[Coinotag] Ошибка обработки ${item.title}:`, error.message);
        return {
          ...item,
          content: item.description
        };
      }
    });

    const processedNews = await Promise.all(newsPromises);
    console.log(`[Coinotag] ✅ Обработано ${processedNews.length} новостей`);
    return processedNews;

  } catch (error) {
    console.error('[Coinotag] Error fetching news:', error.message);
    return [];
  }
}

// Объединенная функция для получения всех премиальных крипто новостей
async function fetchAllPremiumCryptoNews() {
  console.log('[PremiumCrypto] Fetching news from premium crypto sources...');

  const [
    coinDeskNews,
    cointelegraphNews,
    theBlockNews,
    decryptNews,
    messariNews,
    cryptoSlateNews,
    bitcoinMagazineNews,
    conotagNews
  ] = await Promise.allSettled([
    fetchCoinDeskNews(),
    fetchCointelegraphNews(),
    fetchTheBlockNews(),
    fetchDecryptNews(),
    fetchMessariNews(),
    fetchCryptoSlateNews(),
    fetchBitcoinMagazineNews(),
    fetchConotagNews()
  ]);

  const allNews = [
    ...(coinDeskNews.status === 'fulfilled' ? coinDeskNews.value : []),
    ...(cointelegraphNews.status === 'fulfilled' ? cointelegraphNews.value : []),
    ...(theBlockNews.status === 'fulfilled' ? theBlockNews.value : []),
    ...(decryptNews.status === 'fulfilled' ? decryptNews.value : []),
    ...(messariNews.status === 'fulfilled' ? messariNews.value : []),
    ...(cryptoSlateNews.status === 'fulfilled' ? cryptoSlateNews.value : []),
    ...(bitcoinMagazineNews.status === 'fulfilled' ? bitcoinMagazineNews.value : []),
    ...(conotagNews.status === 'fulfilled' ? conotagNews.value : [])
  ];

  console.log(`[PremiumCrypto] Fetched ${allNews.length} premium crypto news`);
  return allNews;
}

module.exports = {
  fetchCoinDeskNews,
  fetchCointelegraphNews,
  fetchTheBlockNews,
  fetchDecryptNews,
  fetchMessariNews,
  fetchCryptoSlateNews,
  fetchBitcoinMagazineNews,
  fetchConotagNews,
  fetchAllPremiumCryptoNews,
  extractFullContent
};
