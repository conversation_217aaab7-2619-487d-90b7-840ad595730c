const axios = require('axios');
const cheerio = require('cheerio');
require('dotenv').config();

// API Keys
const ALPHA_VANTAGE_KEY = process.env.ALPHA_VANTAGE_API_KEY;
const POLYGON_KEY = process.env.POLYGON_API_KEY;
const FINNHUB_KEY = process.env.FINNHUB_API_KEY;

// Alpha Vantage News API
async function fetchAlphaVantageNews() {
  if (!ALPHA_VANTAGE_KEY || ALPHA_VANTAGE_KEY === 'your_alpha_vantage_key_here') {
    console.log('[AlphaVantage] API key not configured');
    return [];
  }

  try {
    const response = await axios.get('https://www.alphavantage.co/query', {
      params: {
        function: 'NEWS_SENTIMENT',
        topics: 'financial_markets,economy_fiscal,economy_monetary,economy_macro',
        apikey: ALPHA_VANTAGE_KEY,
        limit: 50
      }
    });

    if (!response.data?.feed?.length) {
      console.log('[AlphaVantage] No news in response');
      return [];
    }

    return response.data.feed.map((item, idx) => ({
      id: `alphavantage_${item.url}_${idx}`,
      title: item.title,
      description: item.summary,
      publishedAt: item.time_published,
      source: item.source,
      url: item.url,
      tags: item.topics?.map(topic => topic.topic) || [],
      content: item.summary,
      imageUrl: item.banner_image,
      sentiment: parseSentiment(item.overall_sentiment_score),
      relevanceScore: item.relevance_score,
      sourceQuality: 9, // Alpha Vantage is high quality
      category: 'stocks'
    })).filter(news => news.relevanceScore > 0.3); // Только релевантные новости

  } catch (error) {
    console.error('[AlphaVantage] Error fetching news:', error.message);
    return [];
  }
}

// Polygon.io News API
async function fetchPolygonNews() {
  if (!POLYGON_KEY || POLYGON_KEY === 'your_polygon_key_here') {
    console.log('[Polygon] API key not configured');
    return [];
  }

  try {
    const response = await axios.get('https://api.polygon.io/v2/reference/news', {
      params: {
        apikey: POLYGON_KEY,
        limit: 50,
        order: 'desc'
      }
    });

    if (!response.data?.results?.length) {
      console.log('[Polygon] No news in response');
      return [];
    }

    return response.data.results.map((item, idx) => ({
      id: `polygon_${item.id}_${idx}`,
      title: item.title,
      description: item.description,
      publishedAt: item.published_utc,
      source: item.publisher?.name || 'Polygon',
      url: item.article_url,
      tags: item.keywords || [],
      content: item.description,
      imageUrl: item.image_url,
      author: item.author,
      sourceQuality: 8,
      category: 'stocks',
      tickers: item.tickers || []
    }));

  } catch (error) {
    console.error('[Polygon] Error fetching news:', error.message);
    return [];
  }
}

// Finnhub News API
async function fetchFinnhubNews() {
  if (!FINNHUB_KEY || FINNHUB_KEY === 'your_finnhub_key_here') {
    console.log('[Finnhub] API key not configured');
    return [];
  }

  try {
    const response = await axios.get('https://finnhub.io/api/v1/news', {
      params: {
        category: 'general',
        token: FINNHUB_KEY
      }
    });

    if (!Array.isArray(response.data) || !response.data.length) {
      console.log('[Finnhub] No news in response');
      return [];
    }

    return response.data.map((item, idx) => ({
      id: `finnhub_${item.id}_${idx}`,
      title: item.headline,
      description: item.summary,
      publishedAt: new Date(item.datetime * 1000).toISOString(),
      source: item.source,
      url: item.url,
      tags: item.category ? [item.category] : [],
      content: item.summary,
      imageUrl: item.image,
      sourceQuality: 8,
      category: 'stocks'
    }));

  } catch (error) {
    console.error('[Finnhub] Error fetching news:', error.message);
    return [];
  }
}

// Yahoo Finance RSS Feed Parser
async function fetchYahooFinanceNews() {
  try {
    const feeds = [
      'https://feeds.finance.yahoo.com/rss/2.0/headline',
      'https://feeds.finance.yahoo.com/rss/2.0/topstories'
    ];

    const allNews = [];

    for (const feedUrl of feeds) {
      try {
        const response = await axios.get(feedUrl);
        const $ = cheerio.load(response.data, { xmlMode: true });

        $('item').each((idx, element) => {
          const $item = $(element);
          allNews.push({
            id: `yahoo_${$item.find('guid').text()}_${idx}`,
            title: $item.find('title').text(),
            description: $item.find('description').text(),
            publishedAt: new Date($item.find('pubDate').text()).toISOString(),
            source: 'Yahoo Finance',
            url: $item.find('link').text(),
            tags: [],
            content: $item.find('description').text(),
            sourceQuality: 9,
            category: 'stocks'
          });
        });
      } catch (feedError) {
        console.error(`[Yahoo] Error fetching feed ${feedUrl}:`, feedError.message);
      }
    }

    return allNews.slice(0, 30); // Ограничиваем количество

  } catch (error) {
    console.error('[Yahoo] Error fetching news:', error.message);
    return [];
  }
}

// MarketWatch RSS Feed Parser
async function fetchMarketWatchNews() {
  try {
    const response = await axios.get('http://feeds.marketwatch.com/marketwatch/topstories/');
    const $ = cheerio.load(response.data, { xmlMode: true });

    const news = [];
    $('item').each((idx, element) => {
      const $item = $(element);
      news.push({
        id: `marketwatch_${$item.find('guid').text()}_${idx}`,
        title: $item.find('title').text(),
        description: $item.find('description').text(),
        publishedAt: new Date($item.find('pubDate').text()).toISOString(),
        source: 'MarketWatch',
        url: $item.find('link').text(),
        tags: [],
        content: $item.find('description').text(),
        sourceQuality: 9,
        category: 'stocks'
      });
    });

    return news.slice(0, 20);

  } catch (error) {
    console.error('[MarketWatch] Error fetching news:', error.message);
    return [];
  }
}

// Объединенная функция для получения всех премиальных финансовых новостей
async function fetchAllPremiumFinanceNews() {
  console.log('[PremiumFinance] Fetching news from premium sources...');

  const [
    alphaVantageNews,
    polygonNews,
    finnhubNews,
    yahooNews,
    marketWatchNews
  ] = await Promise.allSettled([
    fetchAlphaVantageNews(),
    fetchPolygonNews(),
    fetchFinnhubNews(),
    fetchYahooFinanceNews(),
    fetchMarketWatchNews()
  ]);

  const allNews = [
    ...(alphaVantageNews.status === 'fulfilled' ? alphaVantageNews.value : []),
    ...(polygonNews.status === 'fulfilled' ? polygonNews.value : []),
    ...(finnhubNews.status === 'fulfilled' ? finnhubNews.value : []),
    ...(yahooNews.status === 'fulfilled' ? yahooNews.value : []),
    ...(marketWatchNews.status === 'fulfilled' ? marketWatchNews.value : [])
  ];

  console.log(`[PremiumFinance] Fetched ${allNews.length} premium finance news`);
  return allNews;
}

// Вспомогательная функция для парсинга sentiment
function parseSentiment(score) {
  if (score > 0.1) return 'positive';
  if (score < -0.1) return 'negative';
  return 'neutral';
}

module.exports = {
  fetchAlphaVantageNews,
  fetchPolygonNews,
  fetchFinnhubNews,
  fetchYahooFinanceNews,
  fetchMarketWatchNews,
  fetchAllPremiumFinanceNews
};
