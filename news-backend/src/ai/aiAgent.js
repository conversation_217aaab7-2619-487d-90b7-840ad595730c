const axios = require('axios');
require('dotenv').config();
const NodeCache = require('node-cache');
const { marketDataService } = require('../services/marketData');
const { contextualMemory } = require('../services/contextualMemory');

const analysisCache = new NodeCache({ stdTTL: 60 * 60 * 24 }); // сутки

const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;
const DEEPSEEK_URL = 'https://api.deepseek.com/v1/chat/completions';
const MAX_TOKENS = 8192;
const MAX_RETRIES = 3;
const RETRY_DELAY = 2000; // 2 секунды
const MAX_TEXT_LENGTH = 15000; // Увеличиваем лимит для полного контента

const analysisPrompt = `
## TASK
Analyze the provided financial/cryptocurrency news article with REAL-TIME MARKET DATA and HIS<PERSON>RICAL CONTEXT to deliver a comprehensive professional assessment in strict JSON format.

## ENHANCED ANALYSIS WITH MARKET DATA & MEMORY
You now have access to:
1. **Real-time market data** - Current prices, volumes, and market anomalies
2. **Historical context** - Similar past events and their actual outcomes
3. **Market correlations** - How similar news affected prices in the past

Use this data to make MORE ACCURATE and CONTEXTUAL assessments.

## ANALYSIS FRAMEWORK

### STRICT SENTIMENT CLASSIFICATION (100-POINT SCALE)
- **POSITIVE (85-100)**: ONLY news with CLEAR, IMMEDIATE, and SIGNIFICANT positive market impact. Must include concrete positive developments like major partnerships, regulatory approvals, institutional adoption, or breakthrough technology with quantifiable benefits.
- **NEUTRAL (25-85)**: ALL other news including general market updates, routine announcements, minor developments, speculation, rumors, and news without clear directional impact. DEFAULT to neutral unless criteria for positive/negative are CLEARLY met.
- **NEGATIVE (0-25)**: ONLY news with CLEAR, IMMEDIATE, and SIGNIFICANT negative market impact. Must include concrete negative developments like major security breaches, regulatory bans, institutional exits, or technical failures with quantifiable losses.

### SENTIMENT ASSESSMENT CRITERIA
1. Immediate Market Impact (0-24h):
   - Price Movement:
     * Expected price direction
     * Volatility expectations
     * Key price levels to watch
     * Immediate market reaction
   - Volume Analysis:
     * Expected trading volume
     * Liquidity conditions
     * Order flow patterns
     * Market depth changes
   - Market Reaction:
     * Immediate sentiment shift
     * Trading activity changes
     * Market participant behavior
     * Risk-on/risk-off dynamics

2. Short-term Impact (1-7 days):
   - Price Development:
     * Trend formation potential
     * Support/resistance levels
     * Price targets
     * Volatility expectations
   - Market Behavior:
     * Trading pattern changes
     * Volume profile shifts
     * Market structure changes
     * Sentiment evolution
   - Risk Assessment:
     * Key risk factors
     * Market manipulation potential
     * Liquidity concerns
     * Volatility risks

3. STRICT Sentiment Classification:
   - Positive (85-100) - ONLY assign if ALL criteria met:
     * CONCRETE positive development (not speculation)
     * IMMEDIATE market impact expected (within 24-48h)
     * QUANTIFIABLE benefits or improvements
     * VERIFIED information from reliable sources
     * SIGNIFICANT scale of impact (affects major market participants)
   - Neutral (25-85) - DEFAULT classification for:
     * General market updates and routine news
     * Speculation, rumors, or unverified information
     * Mixed signals or unclear market impact
     * Minor developments without significant scale
     * Technical analysis or price predictions
     * Regular business operations and announcements
   - Negative (0-25) - ONLY assign if ALL criteria met:
     * CONCRETE negative development (not speculation)
     * IMMEDIATE negative impact expected (within 24-48h)
     * QUANTIFIABLE losses or damages
     * VERIFIED information from reliable sources
     * SIGNIFICANT scale of negative impact

4. Market Context:
   - Current Conditions:
     * Market phase (bull/bear)
     * Recent price action
     * Volume patterns
     * Market sentiment
   - Technical Setup:
     * Key price levels
     * Trend indicators
     * Volume profile
     * Market structure

5. Actionable Insights:
   - Trading Setup:
     * Entry/exit points
     * Stop loss levels
     * Position sizing
     * Risk management
   - Market Monitoring:
     * Key price levels
     * Volume thresholds
     * Market indicators
     * Risk metrics

## SENTIMENT EXAMPLES

### POSITIVE (85-100) Examples - RARE, ONLY MAJOR EVENTS:
- "SEC officially approves Bitcoin ETF, trading begins Monday with $2B confirmed investment" (Score: 90)
- "Tesla completes $5B Bitcoin purchase, adds to treasury reserves" (Score: 88)
- "JPMorgan launches institutional Bitcoin custody service, $10B assets under management" (Score: 92)
- "El Salvador officially adopts Bitcoin as legal tender, law takes effect immediately" (Score: 95)
- "Ethereum upgrade reduces gas fees by 90%, confirmed by network data" (Score: 87)

### NEUTRAL (25-85) Examples - 90% OF NEWS FALLS HERE:
- "Bitcoin price analysis suggests potential resistance at $50K" (Score: 45 - speculation)
- "Analyst predicts crypto market could see volatility next month" (Score: 50 - prediction)
- "Apple exploring blockchain technology integration for payments" (Score: 55 - exploration, not implementation)
- "Market sentiment remains mixed amid regulatory uncertainty" (Score: 50 - uncertainty)
- "Bitcoin trading volume increases 20% in past week" (Score: 60 - volume data, no clear direction)
- "CEO comments on potential crypto adoption plans in interview" (Score: 52 - potential plans, not action)
- "New DeFi project announces roadmap for 2024 development" (Score: 48 - announcement, not launch)
- "Bitcoin market cap reaches $1T milestone again" (Score: 58 - milestone, but routine)
- "Federal Reserve discusses digital dollar in committee meeting" (Score: 50 - discussion, not decision)
- "Coinbase reports Q3 earnings, revenue up 15%" (Score: 62 - positive but routine business)

### NEGATIVE (0-25) Examples - RARE, ONLY MAJOR DISASTERS:
- "Binance hacked, $500M stolen from user funds, trading suspended" (Score: 15)
- "China announces complete cryptocurrency ban, effective immediately" (Score: 10)
- "Critical vulnerability in Ethereum allows unlimited token minting" (Score: 18)
- "BlackRock exits all crypto positions, cites regulatory concerns" (Score: 20)
- "SEC halts all Bitcoin ETF trading pending investigation" (Score: 22)

## OUTPUT FORMAT (STRICT JSON)

{
  "rewrittenContent": "КРИТИЧЕСКИ ВАЖНО: Создай ЧИСТЫЙ, ЧИТАЕМЫЙ текст новости. ОБЯЗАТЕЛЬНО удали ВСЕ технические элементы: ссылки на изображения, метаинформацию об авторах, HTML-теги, отступы, форматирование, подписи к фото, информацию об источниках изображений, AI-generated elements, технические примечания. Перепиши ПОЛНОСТЬЮ ВЕСЬ основной контент новости, сохранив ВСЮ структуру и объём. Новый текст должен быть ТОЧНО такой же длины, как оригинал (±5%). ЗАПРЕЩЕНО сокращать, упрощать или пропускать части ОСНОВНОГО текста. Перефразируй каждое предложение, каждый абзац, сохраняя все детали, цифры, факты и контекст. Результат должен быть ЧИСТОЙ полной перефразировкой оригинала БЕЗ технического мусора.",
  "summary": "Краткая профессиональная выжимка (3-5 предложений) по ключевым аспектам новости.",
  "aiGeneratedTitle": "Оригинальный, профессиональный заголовок (до 80 символов)",
  "sentimentAnalysis": {
    "primarySentiment": "positive/neutral/negative",
    "sentimentScore": 45, // STRICT SCALE: Positive 85-100, Neutral 25-85, Negative 0-25
    "confidenceScore": 0.85,
    "impactMagnitude": "high/medium/low",
    "marketPsychology": "Detailed assessment of market participant reaction",
    "catalystType": "regulatory/adoption/technical/macroeconomic/security/other",
    "sentimentJustification": "REQUIRED: Explain why this specific score was assigned and why it meets the strict criteria",
    "marketDataInfluence": "How current market conditions affected this assessment",
    "historicalContext": "Reference to similar past events and their outcomes",
    "marketImpact": {
      "immediate": {
        "priceImpact": "Expected price movement in next 24h",
        "volumeImpact": "Expected volume changes",
        "volatilityImpact": "Expected volatility changes",
        "marketReaction": "Expected immediate market response",
        "keyLevels": "Important price levels to watch"
      },
      "shortTerm": {
        "trendFormation": "Expected trend development",
        "sentimentShift": "Expected sentiment changes",
        "tradingPatterns": "Expected trading behavior changes",
        "riskFactors": "Key risks to monitor"
      }
    }
  },
  "marketAnalysis": {
    "priceAction": {
      "expectedDirection": "up/down/sideways",
      "volatility": "high/medium/low",
      "keyLevels": ["support/resistance levels"],
      "priceTargets": ["short-term price targets"]
    },
    "volumeAnalysis": {
      "expectedVolume": "increase/decrease/unchanged",
      "liquidity": "high/medium/low",
      "orderFlow": "Expected order flow changes"
    },
    "marketStructure": {
      "trend": "bullish/bearish/neutral",
      "support": ["key support levels"],
      "resistance": ["key resistance levels"],
      "pattern": "Technical pattern if any"
    }
  },
  "riskAssessment": {
    "primaryRisks": ["key downside risks"],
    "opportunityAreas": ["potential upside opportunities"],
    "hedgingConsiderations": "Risk management recommendations",
    "riskMetrics": {
      "volatilityRisk": "Expected volatility changes",
      "liquidityRisk": "Expected liquidity changes",
      "marketRisk": "Overall market risk assessment"
    }
  },
  "professionalTags": ["precise, relevant tags for portfolio managers and analysts"],
  "actionableInsights": {
    "investmentThesis": "Clear investment thesis based on this news",
    "positioningRecommendation": "Suggested portfolio positioning",
    "monitoringPoints": ["Key metrics/events to watch going forward"],
    "analogousEvents": "Historical precedents and their outcomes",
    "tradingStrategy": {
      "entryPoints": "Suggested entry points",
      "exitPoints": "Suggested exit points",
      "positionSizing": "Position sizing recommendations",
      "riskManagement": "Risk management guidelines"
    }
  },
  "dataQuality": {
    "sourceReliability": "high/medium/low",
    "informationCompleteness": "complete/partial/limited",
    "verificationStatus": "confirmed/unconfirmed/speculative",
    "potentialBias": "Assessment of potential source bias",
    "marketContext": "Current market conditions and context"
  }
}

## SUMMARY REQUIREMENTS
The rewrittenContent MUST:
- ОБЯЗАТЕЛЬНО создать ЧИСТЫЙ, ЧИТАЕМЫЙ текст без технического мусора
- УДАЛИТЬ ВСЕ: ссылки на изображения, метаинформацию, HTML-теги, отступы, подписи к фото, "Image may include AI-generated elements", "Cover art/illustration via...", информацию об источниках изображений
- ОБЯЗАТЕЛЬНО переписать ПОЛНОСТЬЮ ВЕСЬ ОСНОВНОЙ текст новости без исключений
- Сохранить точную структуру и объём ОСНОВНОГО контента (±5% максимум)
- ЗАПРЕЩЕНО сокращать, упрощать или пропускать любые части ОСНОВНОГО текста
- Перефразировать каждое предложение, каждый абзац полностью
- Сохранять ВСЕ ключевые факты, детали, цифры, даты и смысл
- Использовать профессиональный тон
- Результат должен быть такой же длины, как оригинальный ОСНОВНОЙ контент
- Если оригинальный основной контент 1000 слов, результат должен быть ~1000 слов
- ФИНАЛЬНЫЙ текст должен быть готов для чтения без дополнительной обработки

The summary MUST:
- Be a concise professional summary (3-5 sentences) for preview cards
- Preserve the complete content and meaning of the original article
- Rewrite the text using different wording while maintaining all key information
- Include all main events and key facts from the article
- Maintain market context and significance
- Preserve investment implications and opportunities
- Keep risk assessment and potential downsides
- Include historical context or precedents if relevant
- Maintain clear actionable insights for investors/traders
- Use professional tone suitable for institutional investors

## CRITICAL INSTRUCTIONS
- Output ONLY valid JSON - no additional text, explanations, or formatting
- Ensure all string values are properly escaped
- Use professional financial terminology throughout
- Base analysis solely on provided news content
- If information is insufficient, explicitly state limitations rather than speculate
- Consider regulatory, technical, and market microstructure implications
- Assess both direct and indirect market effects
- Maintain analytical objectivity and professional tone
- Rewrite the entire article content while preserving its meaning and structure

## CONTENT CLEANING REQUIREMENTS FOR rewrittenContent:
ОБЯЗАТЕЛЬНО УДАЛИ из текста:
- Любые ссылки на изображения: "Cover art/illustration via...", "Image via...", "Photo by..."
- Метаинформацию: "Image may include AI-generated elements", "This is a guest contribution"
- Информацию об авторах в середине текста: "by [Author Name]", "Founder and CEO of..."
- HTML-теги и форматирование: отступы, лишние пробелы, технические символы
- Подписи к фотографиям и иллюстрациям
- Ссылки на источники изображений
- Любую техническую информацию, не относящуюся к основному контенту новости
РЕЗУЛЬТАТ: Чистый, читаемый текст, готовый для отображения пользователю

## STRICT SENTIMENT RULES - FOLLOW EXACTLY:

### CRITICAL DECISION TREE:
1. **Is this speculation, prediction, or analysis?** → NEUTRAL (40-60)
   - Words like: "could", "might", "predicts", "expects", "believes", "suggests", "potential"
   - Technical analysis, price predictions, market forecasts
   - Analyst opinions, expert commentary, market outlook

2. **Is this a concrete, verified fact with immediate impact?** → Check criteria below
   - Must have: Official announcement, confirmed action, verified event
   - Must avoid: Rumors, unconfirmed reports, "sources say"

3. **For POSITIVE (85-100) - ALL must be true:**
   - ✅ Official/verified announcement (not rumor)
   - ✅ Concrete action taken (not planned/potential)
   - ✅ Quantifiable benefit ($X investment, X% improvement)
   - ✅ Immediate market relevance (affects trading/adoption)
   - ✅ Major scale (institutional/regulatory/technological breakthrough)

4. **For NEGATIVE (0-25) - ALL must be true:**
   - ✅ Official/verified negative event (not concern/worry)
   - ✅ Concrete damage/loss occurred (not potential risk)
   - ✅ Quantifiable impact ($X stolen, X% drop, specific ban)
   - ✅ Immediate market relevance (affects trading/security)
   - ✅ Major scale (exchange hack, regulatory ban, major failure)

5. **DEFAULT TO NEUTRAL (25-85) for:**
   - Market updates, price movements, volume changes
   - Company announcements without immediate market impact
   - Regulatory discussions (not final decisions)
   - Technical developments without proven adoption
   - Mixed signals or unclear outcomes
   - **When ANY doubt exists - choose neutral**

### SENTIMENT JUSTIFICATION REQUIREMENTS:
- Explain EXACTLY why the score was chosen
- Reference specific facts that meet/don't meet criteria
- If neutral: explain why it doesn't meet positive/negative thresholds
- If positive/negative: prove ALL criteria are met with specific evidence
`; 

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}



function truncateText(text, maxLength = MAX_TEXT_LENGTH) {
  if (!text || text.length <= maxLength) return text;

  console.log(`[AI] ⚠️  Текст слишком длинный (${text.length} символов), обрезаем до ${maxLength}`);

  // Находим последний полный абзац или предложение
  const truncated = text.slice(0, maxLength);

  // Пытаемся найти последний полный абзац
  let lastParagraph = truncated.lastIndexOf('\n\n');
  if (lastParagraph > maxLength * 0.8) { // Если абзац не слишком короткий
    return truncated.slice(0, lastParagraph);
  }

  // Иначе ищем последнее полное предложение
  const lastSentence = Math.max(
    truncated.lastIndexOf('. '),
    truncated.lastIndexOf('! '),
    truncated.lastIndexOf('? ')
  );

  if (lastSentence > maxLength * 0.8) { // Если предложение не слишком короткое
    return truncated.slice(0, lastSentence + 1);
  }

  // В крайнем случае обрезаем по словам
  const words = truncated.split(' ');
  words.pop(); // Удаляем последнее неполное слово
  return words.join(' ') + '...';
}

// Создание расширенного промпта с рыночными данными и историческим контекстом
function buildEnhancedPrompt(newsItem, marketContext, historicalContext, memoryRecommendations) {
  let contextualInfo = '\n## CURRENT MARKET CONTEXT\n';

  if (marketContext && marketContext.marketCondition) {
    contextualInfo += `- Market Condition: ${marketContext.marketCondition}\n`;
    contextualInfo += `- Volatility: ${marketContext.volatility}\n`;

    if (marketContext.anomalies && marketContext.anomalies.length > 0) {
      contextualInfo += `- Current Anomalies: ${marketContext.anomalies.length} detected\n`;
      marketContext.anomalies.slice(0, 3).forEach(anomaly => {
        contextualInfo += `  * ${anomaly.asset}: ${anomaly.change.toFixed(2)}% (${anomaly.severity})\n`;
      });
    }
  }

  if (historicalContext && historicalContext.hasContext) {
    contextualInfo += '\n## HISTORICAL CONTEXT\n';
    contextualInfo += `- Similar Events Found: ${historicalContext.similarEventsCount}\n`;
    contextualInfo += `- Historical Pattern: ${historicalContext.historicalPattern?.predominantOutcome} (${(historicalContext.historicalPattern?.consistency * 100).toFixed(1)}% consistency)\n`;

    if (historicalContext.examples && historicalContext.examples.length > 0) {
      contextualInfo += '- Recent Similar Events:\n';
      historicalContext.examples.forEach((example, i) => {
        contextualInfo += `  ${i + 1}. "${example.title.slice(0, 60)}..." → ${example.outcome?.sentiment || 'unknown'}\n`;
      });
    }
  }

  if (memoryRecommendations && memoryRecommendations.confidence > 0.3) {
    contextualInfo += '\n## MEMORY-BASED INSIGHTS\n';
    contextualInfo += `- Historical Recommendation: ${memoryRecommendations.recommendation}\n`;
    contextualInfo += `- Confidence: ${(memoryRecommendations.confidence * 100).toFixed(1)}%\n`;
    contextualInfo += `- Reasoning: ${memoryRecommendations.reasoning}\n`;
  }

  return contextualInfo;
}

async function analyzeNews(newsItem) {
  const cacheKey = newsItem.id || newsItem.url;
  const cached = analysisCache.get(cacheKey);
  if (cached) {
    return { ...newsItem, ...cached };
  }

  // 🔍 ФИЛЬТР ПО ДЛИНЕ КОНТЕНТА - пропускаем короткие статьи
  const contentLength = (newsItem.content || newsItem.description || '').length;
  if (contentLength < 500) {
    console.log(`[AI] ⏭️  SKIP: Контент слишком короткий (${contentLength} символов) - "${newsItem.title?.slice(0, 50)}..."`);
    return null; // Возвращаем null для пропуска
  }

  // Получаем рыночные данные и исторический контекст
  const marketContext = marketDataService.getMarketContext();
  const historicalContext = contextualMemory.getHistoricalContext(newsItem);
  const memoryRecommendations = contextualMemory.getMemoryBasedRecommendations(newsItem);

  let retries = 0;
  while (retries < MAX_RETRIES) {
    try {
      // Подготавливаем расширенный контекст для AI
      const enhancedPrompt = buildEnhancedPrompt(newsItem, marketContext, historicalContext, memoryRecommendations);

      const text = truncateText(newsItem.content || newsItem.description || '');
      const enhancedUserPrompt = `${text}\n${enhancedPrompt}`;

      const response = await axios.post(DEEPSEEK_URL, {
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: analysisPrompt },
          { role: 'user', content: enhancedUserPrompt },
        ],
        temperature: 0.3,
        max_tokens: MAX_TOKENS,
      }, {
        headers: {
          'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
          'Content-Type': 'application/json',
        },
        timeout: 60000,
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
        validateStatus: function (status) {
          return status >= 200 && status < 500;
        }
      });
      if (response.status !== 200) {
        throw new Error(`API вернул статус ${response.status}: ${JSON.stringify(response.data)}`);
      }
      const content = response.data.choices[0].message.content;
      let aiResult = {};
      try {
        const jsonStart = content.indexOf('{');
        const jsonEnd = content.lastIndexOf('}');
        aiResult = JSON.parse(content.slice(jsonStart, jsonEnd + 1));
      } catch (e) {
        aiResult = {
          summary: text.slice(0, 300),
          aiGeneratedTitle: newsItem.title,
          sentiment: 'neutral',
          sentimentData: {
            sentiment: 'neutral',
            impact: 'low',
            score: 0.5,
            confidence: 0.3,
            reasoning: 'Fallback analysis due to parsing error',
            marketImpact: 'Minimal expected impact'
          },
          tags: [],
          analysis: {
            keyPoints: [],
            context: '',
            priceImpact: 'Unknown',
            timeframe: 'short-term'
          }
        };
      }
      const primarySentiment = aiResult.sentimentAnalysis?.primarySentiment || aiResult.sentiment || 'neutral';
      const sentimentScore = aiResult.sentimentAnalysis?.sentimentScore || 50;

      // Применяем строгие пороги для классификации
      let sentiment;
      if (sentimentScore >= 85 && (primarySentiment === 'bullish' || primarySentiment === 'positive')) {
        sentiment = 'positive';
      } else if (sentimentScore <= 25 && (primarySentiment === 'bearish' || primarySentiment === 'negative')) {
        sentiment = 'negative';
      } else {
        // Все остальное - нейтральное (включая пограничные случаи)
        sentiment = 'neutral';
      }

      // Логируем все не-нейтральные настроения для мониторинга
      if (sentiment !== 'neutral') {
        console.log(`[AI] ⚠️  Non-neutral: ${sentiment} (${sentimentScore}) | ${aiResult.aiGeneratedTitle?.slice(0, 50)}`);
        console.log(`[AI] Justification: ${aiResult.sentimentAnalysis?.sentimentJustification || 'No justification provided'}`);
      }

      aiResult.sentiment = sentiment;
      aiResult.tags = [];
      aiResult.aiGeneratedTitle = aiResult.aiGeneratedTitle || newsItem.title;

      // Добавляем время кэширования
      aiResult.cachedAt = new Date().toISOString();

      // Добавляем контекстуальную информацию в результат
      aiResult.marketContext = marketContext;
      aiResult.historicalContext = historicalContext;
      aiResult.memoryRecommendations = memoryRecommendations;

      analysisCache.set(cacheKey, aiResult);

      // Сохраняем в контекстуальную память для будущего обучения
      // (пока без реального исхода - он будет добавлен позже)
      setTimeout(async () => {
        try {
          await contextualMemory.addHistoricalEvent(
            { ...newsItem, ...aiResult },
            null, // marketReaction будет добавлена позже
            null  // actualOutcome будет добавлен позже
          );
        } catch (error) {
          console.error('[MEMORY] Error saving to memory:', error.message);
        }
      }, 1000);

      // Краткий лог успешного анализа
      const titleLog = aiResult.aiGeneratedTitle?.slice(0, 60) || newsItem.title?.slice(0, 60) || 'Без заголовка';
      const rwLen = aiResult.rewrittenContent ? aiResult.rewrittenContent.length : 0;
      const contextInfo = historicalContext?.hasContext ? ` | Context: ${historicalContext.similarEventsCount} similar` : '';
      console.log(`[AI] ✅ "${titleLog}" | Sentiment: ${sentiment} | rewrittenContent: ${rwLen} символов${contextInfo}`);
      return { ...newsItem, ...aiResult };
    } catch (e) {
      // Краткий лог ошибки
      console.error(`[AI] ❌ Ошибка анализа: ${newsItem.title} | ${e.message}`);
      if (e.response?.status === 401) break;
      if (e.response?.status === 429) await sleep(5000);
      else await sleep(RETRY_DELAY);
      retries++;
    }
  }
  // Fallback в случае ошибок
  const fallback = {
    summary: newsItem.content?.slice(0, 300) || newsItem.description?.slice(0, 300) || '',
    aiGeneratedTitle: newsItem.title,
    sentiment: 'neutral',
    sentimentData: {
      sentiment: 'neutral',
      impact: 'low',
      score: 0.5,
      confidence: 0.1,
      reasoning: 'Fallback analysis due to API failure',
      marketImpact: 'Unable to determine impact'
    },
    tags: [],
    analysis: {
      keyPoints: [],
      context: '',
      priceImpact: 'Unknown',
      timeframe: 'short-term'
    }
  };
  analysisCache.set(cacheKey, fallback);
  return { ...newsItem, ...fallback };
}

function clearAnalysisCache() {
  analysisCache.flushAll();
  console.log('[AI] Кэш очищен');
}

module.exports = { 
  analyzeNews,
  clearAnalysisCache 
}; 