// Premium News Sources Configuration
// Высококачественные источники новостей для крипто и стоков

const PREMIUM_CRYPTO_SOURCES = {
  // Tier 1 - Топовые источники (качество 9-10)
  tier1: {
    sources: [
      'CoinDesk',
      'Cointelegraph', 
      'The Block',
      'Decrypt',
      'CryptoSlate',
      'Bitcoin Magazine',
      'Messari',
      'DeFi Pulse',
      'CoinGecko',
      'CryptoCompare'
    ],
    qualityScore: 10,
    priority: 1,
    categories: ['market_analysis', 'institutional', 'defi', 'regulation', 'technology']
  },
  
  // Tier 2 - Качественные источники (качество 7-8)
  tier2: {
    sources: [
      'CryptoNews',
      'NewsBTC',
      'U.Today',
      'BeInCrypto',
      'CryptoPotato',
      'Bitcoinist',
      'CoinJournal',
      'Crypto Briefing'
    ],
    qualityScore: 8,
    priority: 2,
    categories: ['market_news', 'price_analysis', 'altcoins', 'trading']
  },

  // Influencers & Analysis (качество 8-9)
  influencers: {
    sources: [
      'Willy Woo',
      'PlanB',
      'Raoul Pal',
      'Michael Saylor',
      'Cathie Wood',
      'Anthony Pompliano',
      'Lyn Alden',
      'Preston Pysh'
    ],
    qualityScore: 9,
    priority: 1,
    categories: ['analysis', 'predictions', 'institutional_moves']
  }
};

const PREMIUM_STOCK_SOURCES = {
  // Tier 1 - Топовые финансовые источники (качество 9-10)
  tier1: {
    sources: [
      'Bloomberg',
      'Reuters',
      'Financial Times',
      'Wall Street Journal',
      'MarketWatch',
      'Yahoo Finance',
      'CNBC',
      'Seeking Alpha',
      'Barron\'s',
      'The Motley Fool'
    ],
    qualityScore: 10,
    priority: 1,
    categories: ['earnings', 'market_analysis', 'economic_data', 'fed_policy', 'geopolitics']
  },

  // Tier 2 - Качественные источники (качество 7-8)
  tier2: {
    sources: [
      'Investopedia',
      'Zacks',
      'Morningstar',
      'TheStreet',
      'Investor\'s Business Daily',
      'Forbes Finance',
      'Fortune',
      'Business Insider'
    ],
    qualityScore: 8,
    priority: 2,
    categories: ['stock_analysis', 'sector_news', 'ipo', 'mergers']
  },

  // Economic & Policy Sources (качество 9-10)
  economic: {
    sources: [
      'Federal Reserve',
      'U.S. Treasury',
      'Bureau of Economic Analysis',
      'Bureau of Labor Statistics',
      'International Monetary Fund',
      'World Bank',
      'European Central Bank',
      'Bank of England'
    ],
    qualityScore: 10,
    priority: 1,
    categories: ['monetary_policy', 'economic_indicators', 'inflation', 'employment']
  }
};

// Ключевые слова для высококачественного контента
const PREMIUM_KEYWORDS = {
  crypto: {
    high_priority: [
      'institutional adoption', 'whale movement', 'large transaction', 'liquidation',
      'market manipulation', 'regulatory approval', 'etf approval', 'central bank',
      'fed policy', 'inflation hedge', 'store of value', 'digital gold',
      'defi protocol', 'smart contract', 'blockchain technology', 'consensus mechanism'
    ],
    analysis_terms: [
      'technical analysis', 'on-chain analysis', 'market sentiment', 'fear and greed',
      'support level', 'resistance level', 'fibonacci retracement', 'moving average',
      'rsi', 'macd', 'bollinger bands', 'volume analysis'
    ],
    market_events: [
      'halving', 'hard fork', 'soft fork', 'network upgrade', 'mining difficulty',
      'hash rate', 'staking rewards', 'yield farming', 'liquidity mining'
    ]
  },
  
  stocks: {
    high_priority: [
      'earnings report', 'quarterly results', 'revenue guidance', 'profit margin',
      'market cap', 'pe ratio', 'dividend yield', 'stock buyback',
      'merger acquisition', 'ipo filing', 'sec filing', 'insider trading'
    ],
    economic_indicators: [
      'gdp growth', 'unemployment rate', 'inflation rate', 'interest rate',
      'consumer confidence', 'retail sales', 'housing data', 'manufacturing index'
    ],
    geopolitical: [
      'trade war', 'sanctions', 'military conflict', 'election results',
      'policy change', 'regulatory approval', 'antitrust', 'tax reform'
    ]
  }
};

// Функция для определения качества источника
function getSourceQuality(sourceName, category = 'general') {
  const source = sourceName.toLowerCase();
  
  // Проверяем крипто источники
  for (const [tier, config] of Object.entries(PREMIUM_CRYPTO_SOURCES)) {
    if (config.sources.some(s => source.includes(s.toLowerCase()))) {
      return {
        quality: config.qualityScore,
        priority: config.priority,
        tier: tier,
        category: 'crypto',
        categories: config.categories
      };
    }
  }
  
  // Проверяем сток источники
  for (const [tier, config] of Object.entries(PREMIUM_STOCK_SOURCES)) {
    if (config.sources.some(s => source.includes(s.toLowerCase()))) {
      return {
        quality: config.qualityScore,
        priority: config.priority,
        tier: tier,
        category: 'stocks',
        categories: config.categories
      };
    }
  }
  
  // Источник не найден в премиум списке
  return {
    quality: 5,
    priority: 3,
    tier: 'standard',
    category: 'general',
    categories: ['general']
  };
}

// Функция для анализа качества контента
function analyzeContentQuality(title, description, content, source) {
  let qualityScore = 0;
  const text = `${title} ${description} ${content}`.toLowerCase();
  
  // Базовое качество источника
  const sourceQuality = getSourceQuality(source);
  qualityScore += sourceQuality.quality;
  
  // Анализ ключевых слов
  const cryptoKeywords = [...PREMIUM_KEYWORDS.crypto.high_priority, ...PREMIUM_KEYWORDS.crypto.analysis_terms];
  const stockKeywords = [...PREMIUM_KEYWORDS.stocks.high_priority, ...PREMIUM_KEYWORDS.stocks.economic_indicators];
  
  const cryptoMatches = cryptoKeywords.filter(keyword => text.includes(keyword)).length;
  const stockMatches = stockKeywords.filter(keyword => text.includes(keyword)).length;
  
  // Бонус за релевантные ключевые слова
  qualityScore += Math.min(cryptoMatches + stockMatches, 3);
  
  // Штраф за низкокачественные индикаторы
  const lowQualityIndicators = [
    'click here', 'breaking news', 'you won\'t believe', 'shocking',
    'must read', 'urgent', 'exclusive offer', 'limited time'
  ];
  
  const lowQualityMatches = lowQualityIndicators.filter(indicator => text.includes(indicator)).length;
  qualityScore -= lowQualityMatches * 2;
  
  // Бонус за длину и структуру контента
  if (content && content.length > 1000) qualityScore += 1;
  if (content && content.length > 2000) qualityScore += 1;
  
  return {
    score: Math.max(1, Math.min(10, qualityScore)),
    sourceQuality: sourceQuality,
    cryptoRelevance: cryptoMatches,
    stockRelevance: stockMatches,
    lowQualityFlags: lowQualityMatches
  };
}

module.exports = {
  PREMIUM_CRYPTO_SOURCES,
  PREMIUM_STOCK_SOURCES,
  PREMIUM_KEYWORDS,
  getSourceQuality,
  analyzeContentQuality
};
