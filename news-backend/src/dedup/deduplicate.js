const levenshtein = require('fast-levenshtein');
const crypto = require('crypto');

function normalizeText(text) {
  return (text || '')
    .toLowerCase()
    .replace(/[\s\W_]+/g, ' ')
    .trim();
}

function getDedupKey(news) {
  // Более строгий ключ дедупликации
  const titleNorm = normalizeText(news.title);
  const sourceNorm = normalizeText(news.source || '');
  const descNorm = normalizeText((news.description || '').slice(0, 200));
  const urlNorm = normalizeText((news.url || '').replace(/https?:\/\//, ''));

  return `${titleNorm}|${sourceNorm}|${descNorm}|${urlNorm}`;
}

// Новая функция для создания уникального хеша новости
function getUniqueNewsHash(news) {
  const crypto = require('crypto');

  // Создаем хеш на основе самых важных полей
  const urlPart = (news.url || '').trim().toLowerCase();
  const titlePart = (news.title || '').trim().toLowerCase();
  const sourcePart = (news.source || '').trim().toLowerCase();

  const uniqueString = `${urlPart}|${titlePart}|${sourcePart}`;
  return crypto.createHash('md5').update(uniqueString).digest('hex');
}

function getContentHash(news) {
  const text = normalizeText(news.content || news.description || news.title || '').slice(0, 300);
  return crypto.createHash('md5').update(text).digest('hex');
}

function isSimilar(newsA, newsB) {
  // 🔧 ОСЛАБЛЕННАЯ ПРОВЕРКА ДУБЛИКАТОВ

  // Проверяем точное совпадение заголовков
  const titleA = normalizeText(newsA.title);
  const titleB = normalizeText(newsB.title);

  // Если заголовки точно совпадают, это дубликат
  if (titleA === titleB) {
    return true;
  }

  // Проверяем схожесть заголовков (менее строго - только очень похожие)
  const titleDist = levenshtein.get(titleA, titleB);
  const titleMaxLen = Math.max(titleA.length, titleB.length);
  const titleSimilarity = titleMaxLen > 0 ? titleDist / titleMaxLen : 0;

  // 🎯 ИЗМЕНЕНО: Только если заголовки ОЧЕНЬ похожи (< 5% различий)
  if (titleSimilarity < 0.05) {
    return true;
  }

  // 🚫 УБРАЛИ проверку схожести контента - слишком строгая
  // Теперь проверяем только точные совпадения заголовков

  return false;
}

function deduplicateNews(newsList, isInitialLoad = false) {
  console.log('[DEDUP] Начало дедупликации новостей');
  console.log(`[DEDUP] 🚀 Режим: ${isInitialLoad ? 'ПЕРВАЯ ЗАГРУЗКА (дедупликация отключена)' : 'ОБЫЧНАЯ ЗАГРУЗКА'}`);

  // 🎯 ДЕДУПЛИКАЦИЯ ВКЛЮЧЕНА
  console.log('[DEDUP] ✅ ДЕДУПЛИКАЦИЯ АКТИВНА - фильтруем дубликаты');
  console.log(`[DEDUP] 📦 Обрабатываем ${newsList.length} новостей`);

  // Если это первая загрузка, применяем более мягкую дедупликацию
  if (isInitialLoad) {
    console.log('[DEDUP] 🔄 Первая загрузка - применяем базовую дедупликацию');
  }

  const seenKeys = new Set();
  const contentHashes = new Set();
  const seenUrls = new Set();
  const uniqueHashes = new Set(); // Новый набор для уникальных хешей
  const result = [];
  let duplicatesCount = 0;
  let fuzzyDuplicates = 0;

  for (const news of newsList) {
    const key = getDedupKey(news);
    const contentHash = getContentHash(news);
    const uniqueHash = getUniqueNewsHash(news); // Новый уникальный хеш
    const normalizedUrl = normalizeText((news.url || '').replace(/https?:\/\//, ''));

    // УСИЛЕННАЯ проверка на дубликаты
    if (seenKeys.has(key) ||
        contentHashes.has(contentHash) ||
        uniqueHashes.has(uniqueHash) || // Новая проверка по уникальному хешу
        (normalizedUrl && seenUrls.has(normalizedUrl))) {
      duplicatesCount++;
      console.log(`[DEDUP] 🔄 Дубликат найден: "${news.title?.slice(0, 50)}..." (${news.source})`);
      continue;
    }

    // 🎯 ОСЛАБЛЕННАЯ проверка по времени (±10 минут и точное совпадение заголовка)
    const isTimeDuplicate = result.some(existing =>
      Math.abs(new Date(existing.publishedAt) - new Date(news.publishedAt)) < 1000 * 60 * 10 &&
      (normalizeText(existing.title) === normalizeText(news.title))
    );
    if (isTimeDuplicate) {
      duplicatesCount++;
      console.log(`[DEDUP] ⏰ Временной дубликат: "${news.title?.slice(0, 50)}..."`);
      continue;
    }

    // Fuzzy check
    let isDuplicate = false;
    for (const existing of result) {
      if (isSimilar(news, existing)) {
        isDuplicate = true;
        fuzzyDuplicates++;
        break;
      }
    }

    if (!isDuplicate) {
      seenKeys.add(key);
      contentHashes.add(contentHash);
      uniqueHashes.add(uniqueHash); // Добавляем уникальный хеш
      if (normalizedUrl) seenUrls.add(normalizedUrl);
      result.push(news);
      console.log(`[DEDUP] ✅ Уникальная новость: "${news.title?.slice(0, 50)}..." (${news.source})`);
    } else {
      console.log(`[DEDUP] 🔄 Fuzzy дубликат: "${news.title?.slice(0, 50)}..." (${news.source})`);
    }
  }

  console.log('[DEDUP] Результаты дедупликации:', {
    total: newsList.length,
    unique: result.length,
    duplicates: duplicatesCount,
    fuzzyDuplicates
  });

  return result;
}

// Функция для очистки кеша от дубликатов
function cleanCacheFromDuplicates(cacheNews) {
  console.log('[CACHE-DEDUP] 🧹 Начинаем очистку кеша от дубликатов...');
  console.log(`[CACHE-DEDUP] 📦 Исходное количество новостей в кеше: ${cacheNews.length}`);

  const seenKeys = new Set();
  const seenUrls = new Set();
  const seenTitles = new Set();
  const uniqueHashes = new Set(); // Добавляем уникальные хеши
  const cleanedNews = [];
  let removedCount = 0;

  for (const news of cacheNews) {
    if (!news || !news.title) {
      removedCount++;
      console.log('[CACHE-DEDUP] 🗑️ Удалена новость без заголовка');
      continue;
    }

    const key = getDedupKey(news);
    const uniqueHash = getUniqueNewsHash(news); // Генерируем уникальный хеш
    const normalizedUrl = normalizeText((news.url || '').replace(/https?:\/\//, ''));
    const normalizedTitle = normalizeText(news.title);

    // УСИЛЕННАЯ проверка на дубликаты
    if (seenKeys.has(key) ||
        uniqueHashes.has(uniqueHash) || // Новая проверка по уникальному хешу
        (normalizedUrl && seenUrls.has(normalizedUrl)) ||
        seenTitles.has(normalizedTitle)) {
      removedCount++;
      console.log(`[CACHE-DEDUP] 🔄 Удален дубликат: "${news.title?.slice(0, 50)}..." (${news.source})`);
      continue;
    }

    // Проверяем на временные дубликаты (одинаковый заголовок в пределах 1 часа)
    const isTimeDuplicate = cleanedNews.some(existing =>
      Math.abs(new Date(existing.publishedAt) - new Date(news.publishedAt)) < 1000 * 60 * 60 && // 1 час
      normalizeText(existing.title) === normalizedTitle
    );

    if (isTimeDuplicate) {
      removedCount++;
      console.log(`[CACHE-DEDUP] ⏰ Удален временной дубликат: "${news.title?.slice(0, 50)}..."`);
      continue;
    }

    // Добавляем уникальную новость
    seenKeys.add(key);
    uniqueHashes.add(uniqueHash); // Добавляем уникальный хеш
    if (normalizedUrl) seenUrls.add(normalizedUrl);
    seenTitles.add(normalizedTitle);
    cleanedNews.push(news);
  }

  console.log(`[CACHE-DEDUP] ✅ Очистка завершена: удалено ${removedCount} дубликатов, осталось ${cleanedNews.length} уникальных новостей`);

  return cleanedNews;
}

module.exports = { deduplicateNews, cleanCacheFromDuplicates };