// Загружаем конфигурацию в зависимости от окружения
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';
require('dotenv').config({ path: envFile });

// Диагностика: выводим значения ключей
console.log('DEEPSEEK_API_KEY:', process.env.DEEPSEEK_API_KEY);
console.log('NEWS_API_KEY:', process.env.NEWS_API_KEY);

// Проверяем наличие обязательных переменных окружения
const requiredEnvVars = ['DEEPSEEK_API_KEY', 'NEWS_API_KEY'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error('❌ Отсутствуют обязательные переменные окружения:', missingEnvVars.join(', '));
  console.error('Проверьте наличие файла .env в директории news-backend');
  process.exit(1);
}

const express = require('express');
const cors = require('cors');
const { startBackgroundParseAndAnalyze, startSequentialAnalysis, loadNewsFeedCache, newsFeed, refreshNewsFeed, stopPeriodicNewsUpdates, saveNewsFeedCache, sequentialAnalyzer } = require('./services/newsService');
const { cleanCacheFromDuplicates } = require('./dedup/deduplicate');
const { clearNewsRegistry, initializeNewsRegistry } = require('./services/newsService');
const { analyzeNewsFinancially } = require('./ai/financialAnalyzer');
const { clearAnalysisCache } = require('./ai/aiAgent');
const { newsScheduler } = require('./services/newsScheduler');
const { marketDataService } = require('./services/marketData');
const { contextualMemory } = require('./services/contextualMemory');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Продакшн middleware (устанавливаем только если нужно)
let rateLimit, helmet, compression;
if (process.env.NODE_ENV === 'production') {
  try {
    rateLimit = require('express-rate-limit');
    helmet = require('helmet');
    compression = require('compression');
  } catch (e) {
    console.warn('Продакшн middleware не установлен, работаем без них');
  }
}

const app = express();
const PORT = process.env.PORT || 4000;
const NEWS_FEED_CACHE = path.join(__dirname, '../newsFeed.json');

// Хранилище для SSE соединений
const sseClients = new Set();

// Продакшн middleware
if (process.env.NODE_ENV === 'production') {
  // Безопасность
  if (helmet) {
    app.use(helmet({
      contentSecurityPolicy: false, // Отключаем для SSE
      crossOriginEmbedderPolicy: false
    }));
  }

  // Сжатие
  if (compression) {
    app.use(compression());
  }

  // Rate limiting
  if (rateLimit) {
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 минут
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
      message: {
        error: 'Слишком много запросов, попробуйте позже'
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    app.use('/api/', limiter);
  }
}

// CORS настройки
const corsOptions = {
  origin: process.env.NODE_ENV === 'production'
    ? process.env.CORS_ORIGIN || 'https://your-domain.com'
    : '*',
  credentials: true,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));
app.use(express.json({ limit: '10mb' }));

// Статический сервер для дашборда
app.use('/dashboard', express.static(path.join(__dirname, '../public')));

// Логирование запросов в продакшн
if (process.env.NODE_ENV === 'production') {
  app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path} - ${req.ip}`);
    next();
  });
}

// Функция для отправки обновлений всем подключенным клиентам
function broadcastNewsUpdate(newsItem, eventType = 'news-added') {
  const data = JSON.stringify({
    type: eventType,
    news: newsItem,
    timestamp: new Date().toISOString(),
    totalCount: newsFeed.length
  });

  sseClients.forEach(client => {
    try {
      client.write(`data: ${data}\n\n`);
    } catch (error) {
      console.error('[SSE] Ошибка отправки данных клиенту:', error.message);
      sseClients.delete(client);
    }
  });

  console.log(`[SSE] Отправлено обновление ${sseClients.size} клиентам: ${newsItem.title}`);
}

// Проверка API ключей
async function checkApiKeys() {
  console.log('Проверка API ключей...');
  
  // Проверка DeepSeek API
  try {
    const response = await axios.post('https://api.deepseek.com/v1/chat/completions', {
      model: 'deepseek-chat',
      messages: [{ role: 'user', content: 'test' }],
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      timeout: 60000, // Увеличиваем таймаут до 60 секунд
      validateStatus: () => true,
    });
    
    if (response.status === 401) {
      console.error('❌ DeepSeek API ключ недействителен');
      return false;
    }
    console.log('✅ DeepSeek API ключ валиден');
  } catch (e) {
    console.error('❌ Ошибка проверки DeepSeek API:', e.message);
    if (e.code === 'ECONNABORTED') {
      console.log('⚠️ Таймаут при проверке DeepSeek API, продолжаем работу...');
      return true; // Продолжаем работу даже при таймауте
    }
    return false;
  }
  
  // Проверка News API
  try {
    const response = await axios.get('https://newsapi.org/v2/top-headlines', {
      params: {
        country: 'us',
        apiKey: process.env.NEWS_API_KEY,
      },
      timeout: 10000, // Увеличиваем таймаут до 10 секунд
      validateStatus: () => true,
    });
    
    if (response.status === 401) {
      console.error('❌ News API ключ недействителен');
      return false;
    }
    console.log('✅ News API ключ валиден');
  } catch (e) {
    console.error('❌ Ошибка проверки News API:', e.message);
    if (e.code === 'ECONNABORTED') {
      console.log('⚠️ Таймаут при проверке News API, продолжаем работу...');
      return true; // Продолжаем работу даже при таймауте
    }
    return false;
  }
  
  return true;
}

// --- GET /news/stream (SSE endpoint) ---
app.get('/news/stream', (req, res) => {
  console.log(`[SSE] Новый запрос на подключение от ${req.ip}`);

  // Настройка SSE заголовков
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Accept, Cache-Control, Content-Type',
    'Access-Control-Allow-Methods': 'GET',
    'X-Accel-Buffering': 'no', // Отключаем буферизацию для nginx
  });

  // Добавляем клиента в список
  sseClients.add(res);
  console.log(`[SSE] Клиент подключен. Всего клиентов: ${sseClients.size}`);

  // Отправляем начальное сообщение
  try {
    res.write(`data: ${JSON.stringify({
      type: 'connected',
      message: 'Подключение к потоку новостей установлено',
      totalNews: newsFeed.length,
      timestamp: new Date().toISOString()
    })}\n\n`);

    // Отправляем heartbeat каждые 30 секунд для поддержания соединения
    const heartbeatInterval = setInterval(() => {
      if (sseClients.has(res)) {
        try {
          res.write(`data: ${JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().toISOString()
          })}\n\n`);
        } catch (error) {
          console.error('[SSE] Ошибка отправки heartbeat:', error.message);
          clearInterval(heartbeatInterval);
          sseClients.delete(res);
        }
      } else {
        clearInterval(heartbeatInterval);
      }
    }, 30000);

    // Обработка отключения клиента
    req.on('close', () => {
      clearInterval(heartbeatInterval);
      sseClients.delete(res);
      console.log(`[SSE] Клиент отключен. Осталось клиентов: ${sseClients.size}`);
    });

    req.on('error', (error) => {
      console.error('[SSE] Ошибка соединения:', error.message);
      clearInterval(heartbeatInterval);
      sseClients.delete(res);
    });

    // Обработка завершения ответа
    res.on('finish', () => {
      clearInterval(heartbeatInterval);
      sseClients.delete(res);
      console.log(`[SSE] Соединение завершено. Осталось клиентов: ${sseClients.size}`);
    });

  } catch (error) {
    console.error('[SSE] Ошибка при инициализации соединения:', error.message);
    sseClients.delete(res);
  }
});

// --- GET /news ---
app.get('/news', (req, res) => {
  const {
    page = 1,
    pageSize = 800, // 🚀 УВЕЛИЧЕНО: по умолчанию отдаем весь кэш
    sentiment,
    tags,
    search,
    category // Добавляем фильтр по категории
  } = req.query;

  // 🎯 Ограничиваем максимальный размер для безопасности
  const maxPageSize = Math.min(parseInt(pageSize), 1000);
  
  let filteredNews = [...newsFeed];
  
  // Фильтрация по тональности
  if (sentiment) {
    filteredNews = filteredNews.filter(news => news.sentiment === sentiment);
  }

  // Фильтрация по категории (Crypto/Stocks/Whales)
  if (category) {
    const categoryLower = category.toLowerCase();
    filteredNews = filteredNews.filter(news => {
      // Проверяем основную категорию
      if (news.category && news.category.toLowerCase() === categoryLower) {
        return true;
      }

      // Дополнительная проверка по тегам для обратной совместимости
      const newsTagsLower = (news.tags || []).map(tag => tag.toLowerCase());
      const classificationTagsLower = (news.classificationTags || []).map(tag => tag.toLowerCase());

      return newsTagsLower.includes(categoryLower) ||
             classificationTagsLower.includes(categoryLower) ||
             (categoryLower === 'crypto' && (newsTagsLower.includes('bitcoin') || newsTagsLower.includes('ethereum'))) ||
             (categoryLower === 'whales' && newsTagsLower.includes('whale'));
    });
  }

  // Фильтрация по тегам
  if (tags) {
    const tagList = tags.split(',');
    filteredNews = filteredNews.filter(news =>
      tagList.some(tag => news.tags.includes(tag))
    );
  }
  
  // Поиск по тексту
  if (search) {
    const searchLower = search.toLowerCase();
    filteredNews = filteredNews.filter(news => 
      (news.aiGeneratedTitle || '').toLowerCase().includes(searchLower) ||
      (news.summary || '').toLowerCase().includes(searchLower) ||
      (news.content || '').toLowerCase().includes(searchLower)
    );
  }
  
  // 📦 УЛУЧШЕННАЯ ПАГИНАЦИЯ с поддержкой больших объемов
  const start = (page - 1) * maxPageSize;
  const end = start + maxPageSize;

  // Возвращаем только aiGeneratedTitle и AI summary
  const newsWithAiTitleAndSummary = filteredNews.slice(start, end).map(news => ({
    ...news,
    title: news.aiGeneratedTitle || news.title || '',
    summary: news.summary || '',
    rewrittenContent: news.rewrittenContent || '',
    originalDescription: news.description || '',
  }));

  // 📊 Логирование для отладки
  console.log(`[API] 📰 Отдано ${newsWithAiTitleAndSummary.length} новостей из ${filteredNews.length} (страница ${page}, размер ${maxPageSize})`);

  // Дополнительная отладка первых новостей
  if (newsWithAiTitleAndSummary.length > 0) {
    console.log(`[API] 🔍 Первые 3 новости:`);
    newsWithAiTitleAndSummary.slice(0, 3).forEach((news, index) => {
      console.log(`   ${index + 1}. Title: "${news.title?.substring(0, 50)}..."`);
      console.log(`      aiGeneratedTitle: "${news.aiGeneratedTitle?.substring(0, 50)}..."`);
      console.log(`      summary: ${news.summary ? 'есть' : 'нет'}`);
      console.log(`      sentiment: ${news.sentiment?.sentiment || 'нет'}`);
    });
  }

  res.json({
    status: 'success',
    total: filteredNews.length,
    page: parseInt(page),
    pageSize: maxPageSize,
    actualReturned: newsWithAiTitleAndSummary.length,
    news: newsWithAiTitleAndSummary,
  });
});

// --- GET /news/tags ---
app.get('/news/tags', (req, res) => {
  const allTags = new Set();
  newsFeed.forEach(news => {
    news.tags.forEach(tag => allTags.add(tag));
  });
  
  res.json({
    status: 'success',
    tags: Array.from(allTags)
  });
});

// --- GET /news/sentiments ---
app.get('/news/sentiments', (req, res) => {
  const sentiments = {
    positive: newsFeed.filter(n => n.sentiment === 'positive').length,
    negative: newsFeed.filter(n => n.sentiment === 'negative').length,
    neutral: newsFeed.filter(n => n.sentiment === 'neutral').length
  };

  res.json({
    status: 'success',
    sentiments
  });
});

// --- GET /news/categories ---
app.get('/news/categories', (req, res) => {
  const categories = {
    crypto: newsFeed.filter(n => n.category === 'crypto').length,
    stocks: newsFeed.filter(n => n.category === 'stocks').length,
    whales: newsFeed.filter(n => n.category === 'whales').length,
    general: newsFeed.filter(n => n.category === 'general' || !n.category).length
  };

  // Дополнительная статистика
  const totalClassified = categories.crypto + categories.stocks + categories.whales;
  const classificationRate = newsFeed.length > 0 ? (totalClassified / newsFeed.length * 100).toFixed(1) : 0;

  res.json({
    status: 'success',
    categories,
    stats: {
      total: newsFeed.length,
      classified: totalClassified,
      classificationRate: `${classificationRate}%`
    }
  });
});

// --- POST /admin/parse ---
app.post('/admin/parse', async (req, res) => {
  try {
    const result = await startBackgroundParseAndAnalyze(broadcastNewsUpdate);
    refreshNewsFeed();
    res.json({
      status: 'ok',
      message: 'Фоновый парсинг и анализ запущен',
      count: newsFeed.length,
      totalToAnalyze: result.total
    });
  } catch (error) {
    console.error('Ошибка парсинга:', error);
    res.status(500).json({
      status: 'error',
      message: 'Ошибка при парсинге новостей',
      error: error.message
    });
  }
});

// --- POST /admin/clear ---
app.post('/admin/clear', (req, res) => {
  const previousCount = newsFeed.length;
  newsFeed.length = 0; // Очищаем массив
  saveNewsFeedCache(newsFeed);

  console.log(`[ADMIN] 🗑️  Кэш очищен. Удалено ${previousCount} новостей`);

  res.json({
    status: 'ok',
    message: `Кэш очищен. Удалено ${previousCount} новостей`,
    previousCount,
    currentCount: newsFeed.length
  });
});

// --- POST /admin/dedup ---
app.post('/admin/dedup', (req, res) => {
  const previousCount = newsFeed.length;

  console.log(`[ADMIN] 🧹 Запуск очистки кеша от дубликатов...`);

  // Очищаем кеш от дубликатов
  const cleanedNews = cleanCacheFromDuplicates(newsFeed);

  // Обновляем newsFeed
  newsFeed.length = 0;
  newsFeed.push(...cleanedNews);

  // Сохраняем очищенный кеш
  saveNewsFeedCache(newsFeed);

  const removedCount = previousCount - newsFeed.length;

  console.log(`[ADMIN] ✅ Дедупликация завершена. Удалено ${removedCount} дубликатов, осталось ${newsFeed.length} новостей`);

  res.json({
    status: 'ok',
    message: `Дедупликация завершена. Удалено ${removedCount} дубликатов`,
    previousCount,
    currentCount: newsFeed.length,
    removedDuplicates: removedCount
  });
});

// --- POST /admin/clear-registry ---
app.post('/admin/clear-registry', (req, res) => {
  console.log(`[ADMIN] 🧹 Очистка реестра уникальности...`);

  clearNewsRegistry();

  // Переинициализируем реестр из текущего кеша
  initializeNewsRegistry(newsFeed);

  console.log(`[ADMIN] ✅ Реестр уникальности очищен и переинициализирован`);

  res.json({
    status: 'ok',
    message: 'Реестр уникальности очищен и переинициализирован',
    currentCacheSize: newsFeed.length
  });
});

// --- POST /admin/clear-memory ---
app.post('/admin/clear-memory', async (req, res) => {
  try {
    // Очищаем контекстуальную память
    contextualMemory.memory = {
      historicalEvents: [],
      patterns: {},
      learningData: [],
      correlations: {}
    };

    await contextualMemory.saveMemory();

    console.log(`[ADMIN] 🧠 Контекстуальная память очищена`);

    res.json({
      status: 'ok',
      message: 'Контекстуальная память очищена'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message
    });
  }
});

// --- POST /admin/clear-ai-cache ---
app.post('/admin/clear-ai-cache', (req, res) => {
  try {
    clearAnalysisCache();

    console.log(`[ADMIN] 🤖 AI кэш анализа очищен`);

    res.json({
      status: 'ok',
      message: 'AI кэш анализа очищен - новые новости будут обрабатываться с обновленными инструкциями'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message
    });
  }
});

// --- GET /status ---
app.get('/status', (req, res) => {
  const schedulerStats = newsScheduler.getStats();
  const healthCheck = newsScheduler.healthCheck();

  res.json({
    status: 'ok',
    message: 'News backend is running',
    totalNews: newsFeed.length,
    connectedClients: sseClients.size,
    scheduler: schedulerStats,
    health: healthCheck,
    timestamp: new Date().toISOString()
  });
});

// --- GET /admin/scheduler/stats ---
app.get('/admin/scheduler/stats', (req, res) => {
  const stats = newsScheduler.getStats();
  const errors = newsScheduler.getRecentErrors(10);
  const health = newsScheduler.healthCheck();

  res.json({
    status: 'success',
    stats,
    recentErrors: errors,
    health,
    timestamp: new Date().toISOString()
  });
});

// --- POST /admin/scheduler/start ---
app.post('/admin/scheduler/start', (req, res) => {
  try {
    newsScheduler.start();
    res.json({
      status: 'success',
      message: 'News scheduler started',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// --- POST /admin/scheduler/stop ---
app.post('/admin/scheduler/stop', (req, res) => {
  try {
    newsScheduler.stop();
    res.json({
      status: 'success',
      message: 'News scheduler stopped',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// --- POST /admin/scheduler/force-update ---
app.post('/admin/scheduler/force-update', async (req, res) => {
  try {
    const result = await newsScheduler.forceUpdate();
    refreshNewsFeed();
    res.json({
      status: 'success',
      message: 'Force update completed',
      result,
      totalNews: newsFeed.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// --- GET /admin/sentiment/stats ---
app.get('/admin/sentiment/stats', (req, res) => {
  try {
    const sentimentStats = {
      positive: newsFeed.filter(n => n.sentiment?.sentiment === 'positive').length,
      neutral: newsFeed.filter(n => n.sentiment?.sentiment === 'neutral').length,
      negative: newsFeed.filter(n => n.sentiment?.sentiment === 'negative').length,
      total: newsFeed.length
    };

    const qualityStats = {
      highQuality: newsFeed.filter(n => n.qualityScore >= 8).length,
      mediumQuality: newsFeed.filter(n => n.qualityScore >= 6 && n.qualityScore < 8).length,
      lowQuality: newsFeed.filter(n => n.qualityScore < 6).length,
      averageQuality: newsFeed.length > 0 ?
        (newsFeed.reduce((sum, n) => sum + (n.qualityScore || 5), 0) / newsFeed.length).toFixed(2) : 0
    };

    const recentNews = newsFeed
      .filter(n => n.sentiment?.sentiment !== 'neutral')
      .slice(0, 10)
      .map(n => ({
        title: n.title?.slice(0, 80) + '...',
        sentiment: n.sentiment?.sentiment,
        score: n.sentiment?.sentimentScore,
        source: n.source,
        validationFlags: n.sentiment?.validationFlags || [],
        publishedAt: n.publishedAt
      }));

    res.json({
      status: 'success',
      sentimentDistribution: sentimentStats,
      qualityDistribution: qualityStats,
      recentNonNeutral: recentNews,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// --- GET /admin/market/data ---
app.get('/admin/market/data', (req, res) => {
  try {
    const marketData = marketDataService.getCurrentMarketData();
    const anomalies = marketDataService.getCurrentAnomalies();
    const context = marketDataService.getMarketContext();

    res.json({
      status: 'success',
      marketData,
      anomalies,
      context,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// --- GET /admin/memory/stats ---
app.get('/admin/memory/stats', async (req, res) => {
  try {
    // Простая статистика памяти
    const stats = {
      initialized: contextualMemory.initialized,
      memorySize: contextualMemory.memory?.historicalEvents?.length || 0,
      patterns: Object.keys(contextualMemory.memory?.patterns || {}).length,
      learningData: contextualMemory.memory?.learningData?.length || 0
    };

    res.json({
      status: 'success',
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// --- POST /admin/analysis/start-sequential ---
app.post('/admin/analysis/start-sequential', async (req, res) => {
  try {
    if (sequentialAnalyzer.isRunning) {
      return res.status(400).json({
        status: 'error',
        message: 'Sequential analysis is already running',
        currentStatus: sequentialAnalyzer.getStatus()
      });
    }

    const result = await startSequentialAnalysis(broadcastNewsUpdate);

    res.json({
      status: 'success',
      message: 'Sequential analysis started',
      result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// --- POST /admin/analysis/stop ---
app.post('/admin/analysis/stop', (req, res) => {
  try {
    sequentialAnalyzer.stop();

    res.json({
      status: 'success',
      message: 'Sequential analysis stopped',
      finalStatus: sequentialAnalyzer.getStatus(),
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// --- GET /admin/analysis/status ---
app.get('/admin/analysis/status', (req, res) => {
  try {
    const status = sequentialAnalyzer.getStatus();

    res.json({
      status: 'success',
      analysisStatus: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// --- GET /news/refresh (для принудительного обновления) ---
app.get('/news/refresh', async (req, res) => {
  try {
    console.log('[API] GET запрос на обновление новостей...');
    const result = await startBackgroundParseAndAnalyze(broadcastNewsUpdate);
    refreshNewsFeed();
    res.json({
      status: 'success',
      message: 'News refresh started via GET',
      totalNews: newsFeed.length,
      newNewsAnalyzed: result.total || 0,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('[API] Ошибка обновления новостей:', error.message);
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// --- GET /news/:id/history ---
app.get('/news/:id/history', (req, res) => {
  const newsId = req.params.id;
  const news = newsFeed.find(n => n.id === newsId || n.url === newsId);
  if (!news) {
    return res.status(404).json({ error: 'Новость не найдена' });
  }
  res.json({
    current: {
      sentiment: news.sentiment,
      aiGeneratedTitle: news.aiGeneratedTitle,
      summary: news.summary,
      rewrittenContent: news.rewrittenContent || '',
      analyzedAt: news.analyzedAt,
    },
    history: news.analysisHistory || [],
  });
});

// --- POST /news/analyze ---
app.post('/news/analyze', async (req, res) => {
  try {
    const { newsId, title, description, content, source, publishedAt } = req.body;

    if (!newsId && !title) {
      return res.status(400).json({
        status: 'error',
        message: 'Требуется newsId или title для анализа'
      });
    }

    // Создаем объект новости для анализа
    const newsItem = {
      id: newsId,
      title: title || '',
      description: description || '',
      content: content || description || '',
      source: source || 'Unknown',
      publishedAt: publishedAt || new Date().toISOString()
    };

    console.log(`[API] Запрос на финансовый анализ новости: ${newsItem.title?.slice(0, 60)}...`);

    // Выполняем финансовый анализ
    const analysisResult = await analyzeNewsFinancially(newsItem);

    res.json({
      status: 'success',
      message: 'Финансовый анализ завершен',
      analysis: analysisResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[API] Ошибка финансового анализа:', error.message);
    res.status(500).json({
      status: 'error',
      message: 'Ошибка при выполнении финансового анализа',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Обработчики для корректного завершения работы
process.on('SIGINT', () => {
  console.log('\n[SERVER] Получен сигнал SIGINT, завершаем работу...');
  console.log('[SERVER] Сохраняем кэш перед завершением...');
  saveNewsFeedCache(newsFeed);
  stopPeriodicNewsUpdates();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n[SERVER] Получен сигнал SIGTERM, завершаем работу...');
  console.log('[SERVER] Сохраняем кэш перед завершением...');
  saveNewsFeedCache(newsFeed);
  stopPeriodicNewsUpdates();
  process.exit(0);
});

// Запускаем сервер
app.listen(PORT, async () => {
  console.log(`🚀 News Backend Server запущен на порту ${PORT}`);
  console.log(`📊 Premium sources: ${process.env.ENABLE_PREMIUM_SOURCES === 'true' ? 'ENABLED' : 'DISABLED'}`);
  console.log(`🎯 Quality threshold: ${process.env.QUALITY_THRESHOLD || 7}`);

  // Проверяем API ключи
  const keysValid = await checkApiKeys();
  if (!keysValid) {
    console.error('❌ Некоторые API ключи недействительны, но сервер продолжает работу (режим диагностики)');
  }

  // Загружаем кэш
  const cachedNews = loadNewsFeedCache();
  if (cachedNews.length === 0) {
    console.log('[CACHE] Кэш пуст, запускаем первичный парсинг...');
  } else {
    console.log(`[CACHE] Загружено ${cachedNews.length} новостей из кэша`);
  }

  // Инициализируем контекстуальную память
  try {
    await contextualMemory.initialize();
    console.log('🧠 Контекстуальная память инициализирована');
  } catch (error) {
    console.error('❌ Ошибка инициализации памяти:', error.message);
  }

  // Запускаем мониторинг рыночных данных
  try {
    marketDataService.startMonitoring();
    console.log('📊 Мониторинг рыночных данных запущен');
  } catch (error) {
    console.error('❌ Ошибка запуска мониторинга рынка:', error.message);
  }

  // Настраиваем планировщик
  newsScheduler.setBroadcastCallback(broadcastNewsUpdate);

  // Запускаем планировщик
  try {
    newsScheduler.start();
    console.log('✅ Планировщик новостей запущен');
  } catch (error) {
    console.error('❌ Ошибка запуска планировщика:', error.message);
  }

  // Первичное обновление
  try {
    console.log('[STARTUP] Запускаем первичное обновление новостей...');
    await startBackgroundParseAndAnalyze(broadcastNewsUpdate);
    refreshNewsFeed();
    console.log('✅ Первичное обновление новостей завершено');
  } catch (error) {
    console.error('❌ Ошибка первичного обновления:', error.message);
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Получен сигнал SIGINT, завершаем работу...');
  newsScheduler.stop();
  marketDataService.stopMonitoring();
  stopPeriodicNewsUpdates();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Получен сигнал SIGTERM, завершаем работу...');
  newsScheduler.stop();
  marketDataService.stopMonitoring();
  stopPeriodicNewsUpdates();
  process.exit(0);
});
