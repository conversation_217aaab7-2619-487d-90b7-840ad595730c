const fs = require('fs').promises;
const path = require('path');

// База данных исторических событий и их последствий
class ContextualMemory {
  constructor() {
    this.memoryFile = path.join(__dirname, '../data/contextual_memory.json');
    this.memory = {
      historicalEvents: [],
      patterns: {},
      learningData: [],
      correlations: {}
    };
    this.maxMemorySize = 10000; // Максимум записей в памяти
    this.initialized = false;
  }

  // Инициализация памяти
  async initialize() {
    try {
      // Создаем директорию data если не существует
      const dataDir = path.dirname(this.memoryFile);
      await fs.mkdir(dataDir, { recursive: true });

      // Загружаем существующую память
      await this.loadMemory();
      this.initialized = true;
      console.log(`[MEMORY] Initialized with ${this.memory.historicalEvents.length} historical events`);
    } catch (error) {
      console.error('[MEMORY] Error initializing:', error.message);
      this.initialized = true; // Продолжаем работу с пустой памятью
    }
  }

  // Загрузка памяти из файла
  async loadMemory() {
    try {
      const data = await fs.readFile(this.memoryFile, 'utf8');
      this.memory = JSON.parse(data);
    } catch (error) {
      // Файл не существует или поврежден - создаем новую память
      console.log('[MEMORY] Creating new memory database');
      await this.saveMemory();
    }
  }

  // Сохранение памяти в файл
  async saveMemory() {
    try {
      await fs.writeFile(this.memoryFile, JSON.stringify(this.memory, null, 2));
    } catch (error) {
      console.error('[MEMORY] Error saving memory:', error.message);
    }
  }

  // Добавление исторического события
  async addHistoricalEvent(newsData, marketReaction, actualOutcome) {
    if (!this.initialized) await this.initialize();

    const event = {
      id: this.generateEventId(newsData),
      timestamp: Date.now(),
      news: {
        title: newsData.title,
        sentiment: newsData.sentiment,
        sentimentScore: newsData.sentimentAnalysis?.sentimentScore,
        source: newsData.source,
        tags: newsData.tags || [],
        category: this.categorizeNews(newsData)
      },
      marketReaction: marketReaction,
      actualOutcome: actualOutcome,
      learningScore: this.calculateLearningScore(newsData, marketReaction, actualOutcome)
    };

    this.memory.historicalEvents.push(event);

    // Ограничиваем размер памяти
    if (this.memory.historicalEvents.length > this.maxMemorySize) {
      this.memory.historicalEvents = this.memory.historicalEvents.slice(-this.maxMemorySize);
    }

    // Обновляем паттерны
    this.updatePatterns(event);

    // Сохраняем память
    await this.saveMemory();

    console.log(`[MEMORY] Added historical event: ${event.news.title.slice(0, 50)}...`);
  }

  // Поиск похожих исторических событий
  findSimilarEvents(newsData, limit = 5) {
    if (!this.initialized) return [];

    const newsCategory = this.categorizeNews(newsData);
    const newsKeywords = this.extractKeywords(newsData.title + ' ' + newsData.description);

    const similarities = this.memory.historicalEvents.map(event => {
      let similarity = 0;

      // Совпадение категории
      if (event.news.category === newsCategory) similarity += 0.3;

      // Совпадение источника
      if (event.news.source === newsData.source) similarity += 0.1;

      // Совпадение настроения
      if (event.news.sentiment === newsData.sentiment) similarity += 0.2;

      // Совпадение ключевых слов
      const eventKeywords = this.extractKeywords(event.news.title);
      const keywordOverlap = this.calculateKeywordOverlap(newsKeywords, eventKeywords);
      similarity += keywordOverlap * 0.4;

      return { event, similarity };
    });

    return similarities
      .filter(s => s.similarity > 0.3) // Минимальный порог схожести
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
      .map(s => ({
        ...s.event,
        similarity: s.similarity
      }));
  }

  // Получение исторического контекста для новости
  getHistoricalContext(newsData) {
    const similarEvents = this.findSimilarEvents(newsData);
    
    if (similarEvents.length === 0) {
      return {
        hasContext: false,
        message: "No similar historical events found",
        confidence: 0
      };
    }

    // Анализируем исходы похожих событий
    const outcomes = similarEvents.map(e => e.actualOutcome);
    const avgMarketReaction = this.calculateAverageMarketReaction(similarEvents);
    
    return {
      hasContext: true,
      similarEventsCount: similarEvents.length,
      historicalPattern: this.identifyPattern(outcomes),
      averageMarketReaction: avgMarketReaction,
      confidence: this.calculateContextConfidence(similarEvents),
      examples: similarEvents.slice(0, 3).map(e => ({
        title: e.news.title,
        outcome: e.actualOutcome,
        marketReaction: e.marketReaction,
        similarity: e.similarity
      }))
    };
  }

  // Обучение на основе реальных результатов
  async learnFromOutcome(newsId, actualMarketMovement, timeframe = '24h') {
    if (!this.initialized) await this.initialize();

    const learningData = {
      newsId,
      actualMarketMovement,
      timeframe,
      timestamp: Date.now(),
      accuracy: this.calculatePredictionAccuracy(newsId, actualMarketMovement)
    };

    this.memory.learningData.push(learningData);

    // Обновляем корреляции
    this.updateCorrelations(learningData);

    await this.saveMemory();
    console.log(`[MEMORY] Learned from outcome for news ${newsId}`);
  }

  // Получение рекомендаций на основе памяти
  getMemoryBasedRecommendations(newsData) {
    const context = this.getHistoricalContext(newsData);
    
    if (!context.hasContext) {
      return {
        recommendation: "neutral",
        confidence: 0.1,
        reasoning: "No historical data available for similar events"
      };
    }

    const pattern = context.historicalPattern;
    let recommendation = "neutral";
    let confidence = context.confidence;

    if (pattern.predominantOutcome === "positive" && pattern.consistency > 0.7) {
      recommendation = "positive";
    } else if (pattern.predominantOutcome === "negative" && pattern.consistency > 0.7) {
      recommendation = "negative";
    }

    return {
      recommendation,
      confidence,
      reasoning: `Based on ${context.similarEventsCount} similar events with ${(pattern.consistency * 100).toFixed(1)}% consistency`,
      historicalContext: context
    };
  }

  // Вспомогательные методы

  generateEventId(newsData) {
    return `${Date.now()}_${newsData.title?.slice(0, 20).replace(/\s+/g, '_')}`;
  }

  categorizeNews(newsData) {
    const title = (newsData.title || '').toLowerCase();
    const description = (newsData.description || '').toLowerCase();
    const text = title + ' ' + description;

    if (text.includes('regulation') || text.includes('sec') || text.includes('government')) {
      return 'regulatory';
    }
    if (text.includes('hack') || text.includes('security') || text.includes('breach')) {
      return 'security';
    }
    if (text.includes('partnership') || text.includes('adoption') || text.includes('integration')) {
      return 'adoption';
    }
    if (text.includes('price') || text.includes('trading') || text.includes('market')) {
      return 'market';
    }
    if (text.includes('technology') || text.includes('upgrade') || text.includes('protocol')) {
      return 'technical';
    }
    return 'general';
  }

  extractKeywords(text) {
    if (!text) return [];
    
    return text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !['this', 'that', 'with', 'from', 'they', 'have', 'been', 'will'].includes(word))
      .slice(0, 10);
  }

  calculateKeywordOverlap(keywords1, keywords2) {
    if (keywords1.length === 0 || keywords2.length === 0) return 0;
    
    const overlap = keywords1.filter(k => keywords2.includes(k)).length;
    return overlap / Math.max(keywords1.length, keywords2.length);
  }

  calculateLearningScore(newsData, marketReaction, actualOutcome) {
    // Простая оценка качества обучающих данных
    let score = 0.5;
    
    if (marketReaction && marketReaction.correlation === 'strong') score += 0.3;
    if (actualOutcome && actualOutcome.verified) score += 0.2;
    
    return Math.min(1.0, score);
  }

  updatePatterns(event) {
    const category = event.news.category;
    
    if (!this.memory.patterns[category]) {
      this.memory.patterns[category] = {
        totalEvents: 0,
        outcomes: { positive: 0, negative: 0, neutral: 0 },
        averageAccuracy: 0
      };
    }

    const pattern = this.memory.patterns[category];
    pattern.totalEvents++;
    
    if (event.actualOutcome) {
      pattern.outcomes[event.actualOutcome.sentiment || 'neutral']++;
    }
  }

  calculateAverageMarketReaction(events) {
    const reactions = events
      .map(e => e.marketReaction?.priceChange)
      .filter(r => r !== undefined);
    
    if (reactions.length === 0) return null;
    
    return reactions.reduce((sum, r) => sum + r, 0) / reactions.length;
  }

  identifyPattern(outcomes) {
    const counts = { positive: 0, negative: 0, neutral: 0 };
    
    outcomes.forEach(outcome => {
      if (outcome && outcome.sentiment) {
        counts[outcome.sentiment]++;
      }
    });

    const total = Object.values(counts).reduce((sum, c) => sum + c, 0);
    if (total === 0) return { predominantOutcome: 'neutral', consistency: 0 };

    const predominant = Object.entries(counts).reduce((a, b) => counts[a[0]] > counts[b[0]] ? a : b);
    
    return {
      predominantOutcome: predominant[0],
      consistency: predominant[1] / total,
      distribution: counts
    };
  }

  calculateContextConfidence(similarEvents) {
    if (similarEvents.length === 0) return 0;
    
    const avgSimilarity = similarEvents.reduce((sum, e) => sum + e.similarity, 0) / similarEvents.length;
    const countFactor = Math.min(similarEvents.length / 10, 1); // Больше событий = больше уверенности
    
    return avgSimilarity * countFactor;
  }

  calculatePredictionAccuracy(newsId, actualMovement) {
    // Простая реализация - можно улучшить
    return Math.random() * 0.5 + 0.5; // Заглушка
  }

  updateCorrelations(learningData) {
    // Обновляем корреляции между типами новостей и рыночными движениями
    // Простая реализация для начала
  }
}

// Создаем единственный экземпляр
const contextualMemory = new ContextualMemory();

module.exports = {
  ContextualMemory,
  contextualMemory
};
