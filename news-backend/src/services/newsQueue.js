const NodeCache = require('node-cache');
const { fetchCryptoCompareNews } = require('../adapters/cryptocompare');
const { fetchNewsApiNews, fetchAllTraditionalFinanceNews } = require('../adapters/newsapi');
const { fetchAllPremiumCryptoNews } = require('../adapters/premiumCrypto');
const { fetchAllPremiumFinanceNews } = require('../adapters/premiumFinance');
const { analyzeContentQuality } = require('../config/premiumSources');

// Кэш для лимитов (5 минут)
const sourceLimitCache = new NodeCache({ stdTTL: 300 });
const MAX_REQUESTS_PER_WINDOW = 3;

async function fetchWithLimit(sourceKey, fetchFn) {
  let count = sourceLimitCache.get(sourceKey) || 0;
  
  if (count >= MAX_REQUESTS_PER_WINDOW) {
    console.log(`[LIMIT] Превышен лимит для ${sourceKey}`);
    return [];
  }
  
  sourceLimitCache.set(sourceKey, count + 1);
  
  try {
    const news = await fetchFn();
    return news;
  } catch (e) {
    console.error(`[LIMIT] Ошибка получения новостей от ${sourceKey}:`, e.message);
    return [];
  }
}

async function fetchAllNewsWithLimits() {
  console.log('[QUEUE] Запрос новостей из источников...');

  const enablePremium = process.env.ENABLE_PREMIUM_SOURCES === 'true';
  const premiumOnly = process.env.PREMIUM_SOURCES_ONLY === 'true';

  let allNews = [];

  // Получаем премиальные источники (если включены)
  if (enablePremium) {
    console.log('[QUEUE] Fetching premium sources...');
    const [
      premiumCryptoNews,
      premiumFinanceNews
    ] = await Promise.allSettled([
      fetchWithLimit('premium-crypto', fetchAllPremiumCryptoNews),
      fetchWithLimit('premium-finance', fetchAllPremiumFinanceNews)
    ]);

    allNews.push(
      ...(premiumCryptoNews.status === 'fulfilled' ? premiumCryptoNews.value : []),
      ...(premiumFinanceNews.status === 'fulfilled' ? premiumFinanceNews.value : [])
    );
  }

  // 🎯 НОВАЯ ЛОГИКА: Получаем стандартные источники с разделением по типам
  if (!premiumOnly) {
    console.log('[QUEUE] 🚀 Fetching standard sources with improved categorization...');

    // 📈 КРИПТОВАЛЮТНЫЕ ИСТОЧНИКИ
    console.log('[QUEUE] 📈 Fetching CRYPTO sources...');
    const [
      cryptoCompareNews
    ] = await Promise.allSettled([
      fetchWithLimit('cryptocompare', fetchCryptoCompareNews)
    ]);

    // 🏛️ ТРАДИЦИОННЫЕ ФИНАНСОВЫЕ ИСТОЧНИКИ (стоки, политика, экономика)
    console.log('[QUEUE] 🏛️ Fetching TRADITIONAL FINANCE sources...');
    const [
      traditionalFinanceNews
    ] = await Promise.allSettled([
      fetchWithLimit('newsapi-traditional', fetchAllTraditionalFinanceNews)
    ]);

    allNews.push(
      // Криптовалютные новости
      ...(cryptoCompareNews.status === 'fulfilled' ? cryptoCompareNews.value : []),
      // Традиционные финансовые новости (стоки, политика, экономика)
      ...(traditionalFinanceNews.status === 'fulfilled' ? traditionalFinanceNews.value : [])
    );

    console.log('[QUEUE] 📊 Source breakdown:');
    console.log(`  📈 Crypto sources: ${(cryptoCompareNews.status === 'fulfilled' ? cryptoCompareNews.value.length : 0)}`);
    console.log(`  🏛️ Traditional finance: ${(traditionalFinanceNews.status === 'fulfilled' ? traditionalFinanceNews.value.length : 0)}`);
  }

  // Анализируем качество новостей
  const qualityThreshold = parseInt(process.env.QUALITY_THRESHOLD) || 7;
  const newsWithQuality = allNews.map(news => {
    const quality = analyzeContentQuality(
      news.title || '',
      news.description || '',
      news.content || '',
      news.source || ''
    );

    return {
      ...news,
      qualityScore: quality.score,
      sourceQuality: quality.sourceQuality,
      cryptoRelevance: quality.cryptoRelevance,
      stockRelevance: quality.stockRelevance,
      lowQualityFlags: quality.lowQualityFlags
    };
  });

  // Фильтруем по качеству
  const highQualityNews = newsWithQuality.filter(news =>
    news.qualityScore >= qualityThreshold
  );

  // Сортируем по качеству и времени
  const sortedNews = highQualityNews.sort((a, b) => {
    // Сначала по качеству (убывание)
    if (b.qualityScore !== a.qualityScore) {
      return b.qualityScore - a.qualityScore;
    }
    // Потом по времени (новые первыми)
    return new Date(b.publishedAt) - new Date(a.publishedAt);
  });

  console.log(`[QUEUE] Получено новостей: ${allNews.length}`);
  console.log(`[QUEUE] Высокого качества: ${highQualityNews.length}`);
  console.log(`[QUEUE] Средний рейтинг качества: ${(newsWithQuality.reduce((sum, n) => sum + n.qualityScore, 0) / newsWithQuality.length).toFixed(1)}`);

  return sortedNews;
}

module.exports = { fetchAllNewsWithLimits }; 