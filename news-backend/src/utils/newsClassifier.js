/**
 * Профессиональный классификатор новостей для финансовых рынков
 * Определяет категории: Crypto, Stocks, Whales на основе контента и метаданных
 */

// Ключевые слова для криптовалют (расширенный список)
const CRYPTO_KEYWORDS = {
  // Основные криптовалюты
  primary: [
    'bitcoin', 'btc', 'ethereum', 'eth', 'cryptocurrency', 'crypto', 'blockchain',
    'solana', 'sol', 'cardano', 'ada', 'polkadot', 'dot', 'chainlink', 'link',
    'litecoin', 'ltc', 'bitcoin cash', 'bch', 'stellar', 'xlm', 'monero', 'xmr',
    'ripple', 'xrp', 'dogecoin', 'doge', 'shiba inu', 'shib', 'avalanche', 'avax',
    'polygon', 'matic', 'uniswap', 'uni', 'cosmos', 'atom', 'algorand', 'algo'
  ],
  // DeFi и протоколы
  defi: [
    'defi', 'decentralized finance', 'yield farming', 'liquidity mining',
    'automated market maker', 'amm', 'dex', 'decentralized exchange',
    'compound', 'aave', 'makerdao', 'curve', 'balancer', 'sushiswap',
    'pancakeswap', 'yearn finance', 'synthetix', 'lending protocol'
  ],
  // Технические термины
  technical: [
    'smart contract', 'consensus', 'proof of stake', 'proof of work',
    'mining', 'staking', 'validator', 'node', 'fork', 'halving',
    'layer 2', 'rollup', 'sidechain', 'cross-chain', 'bridge',
    'oracle', 'governance token', 'dao', 'nft', 'metaverse'
  ],
  // Биржи и платформы
  exchanges: [
    'binance', 'coinbase', 'kraken', 'bitfinex', 'huobi', 'okex',
    'ftx', 'kucoin', 'gate.io', 'crypto.com', 'gemini', 'bitstamp'
  ]
};

// Ключевые слова для фондового рынка
const STOCK_KEYWORDS = {
  // Индексы и рынки
  indices: [
    's&p 500', 'nasdaq', 'dow jones', 'russell 2000', 'ftse', 'dax',
    'nikkei', 'hang seng', 'shanghai composite', 'tsx', 'asx'
  ],
  // Финансовые инструменты
  instruments: [
    'stock', 'share', 'equity', 'bond', 'etf', 'mutual fund',
    'reit', 'option', 'future', 'derivative', 'warrant', 'ipo'
  ],
  // Компании и секторы
  sectors: [
    'technology', 'healthcare', 'financial', 'energy', 'consumer',
    'industrial', 'materials', 'utilities', 'real estate', 'telecom'
  ],
  // Финансовые метрики
  metrics: [
    'earnings', 'revenue', 'profit', 'dividend', 'eps', 'pe ratio',
    'market cap', 'book value', 'cash flow', 'debt', 'ebitda'
  ],
  // Крупные компании
  companies: [
    'apple', 'microsoft', 'amazon', 'google', 'alphabet', 'tesla',
    'meta', 'facebook', 'netflix', 'nvidia', 'berkshire hathaway',
    'johnson & johnson', 'jpmorgan', 'visa', 'mastercard'
  ]
};

// Ключевые слова для whale активности
const WHALE_KEYWORDS = {
  // Whale индикаторы
  whale_terms: [
    'whale', 'large holder', 'institutional investor', 'major holder',
    'big player', 'large transaction', 'massive transfer', 'huge buy',
    'large sell', 'institutional buying', 'institutional selling'
  ],
  // Объемы и суммы
  volume_indicators: [
    'million', 'billion', 'large volume', 'massive volume', 'huge amount',
    'significant transfer', 'major transaction', 'large order'
  ],
  // Институциональные игроки
  institutions: [
    'blackrock', 'vanguard', 'fidelity', 'grayscale', 'microstrategy',
    'tesla', 'square', 'paypal', 'institutional', 'hedge fund',
    'pension fund', 'sovereign wealth', 'family office'
  ],
  // Действия китов
  actions: [
    'accumulating', 'dumping', 'moving funds', 'large purchase',
    'significant sale', 'portfolio rebalancing', 'strategic investment'
  ]
};

// Источники новостей по категориям
const NEWS_SOURCES = {
  crypto: [
    'coindesk', 'cointelegraph', 'decrypt', 'the block', 'coinbase',
    'binance', 'crypto news', 'bitcoinist', 'newsbtc', 'cryptoslate'
  ],
  traditional: [
    'bloomberg', 'reuters', 'wall street journal', 'financial times',
    'cnbc', 'marketwatch', 'yahoo finance', 'seeking alpha'
  ],
  whale_tracking: [
    'whale alert', 'lookonchain', 'arkham', 'nansen', 'chainalysis'
  ]
};

/**
 * Классифицирует новость по категориям
 * @param {Object} newsItem - Объект новости
 * @returns {Object} Результат классификации с весами
 */
function classifyNews(newsItem) {
  const text = `${newsItem.title || ''} ${newsItem.description || ''} ${newsItem.content || ''}`.toLowerCase();
  const source = (newsItem.source || '').toLowerCase();
  
  const scores = {
    crypto: 0,
    stocks: 0,
    whales: 0
  };

  // Анализ по ключевым словам
  scores.crypto += analyzeKeywords(text, CRYPTO_KEYWORDS, [2, 1.5, 1, 1.2]);
  scores.stocks += analyzeKeywords(text, STOCK_KEYWORDS, [2, 1.8, 1.5, 1.3, 1.6]);
  scores.whales += analyzeKeywords(text, WHALE_KEYWORDS, [3, 2, 2.5, 2.2]);

  // Анализ источника
  scores.crypto += analyzeSource(source, NEWS_SOURCES.crypto) * 1.5;
  scores.stocks += analyzeSource(source, NEWS_SOURCES.traditional) * 1.3;
  scores.whales += analyzeSource(source, NEWS_SOURCES.whale_tracking) * 2;

  // Специальная логика для whale детекции
  scores.whales += detectWhaleActivity(text);

  // Нормализация и определение основной категории
  const maxScore = Math.max(scores.crypto, scores.stocks, scores.whales);
  const threshold = 2; // Минимальный порог для классификации

  let primaryCategory = 'general';
  let confidence = 0;

  if (maxScore >= threshold) {
    if (scores.crypto === maxScore) {
      primaryCategory = 'crypto';
    } else if (scores.stocks === maxScore) {
      primaryCategory = 'stocks';
    } else if (scores.whales === maxScore) {
      primaryCategory = 'whales';
    }
    confidence = Math.min(maxScore / 10, 1); // Нормализуем до 0-1
  }

  return {
    primaryCategory,
    confidence,
    scores,
    tags: generateTags(text, scores)
  };
}

/**
 * Анализирует ключевые слова в тексте
 */
function analyzeKeywords(text, keywordGroups, weights) {
  let score = 0;
  let groupIndex = 0;

  for (const [groupName, keywords] of Object.entries(keywordGroups)) {
    const weight = weights[groupIndex] || 1;
    
    for (const keyword of keywords) {
      if (text.includes(keyword)) {
        // Бонус за точное совпадение
        const exactMatch = new RegExp(`\\b${keyword}\\b`, 'i').test(text);
        score += exactMatch ? weight * 1.5 : weight;
      }
    }
    groupIndex++;
  }

  return score;
}

/**
 * Анализирует источник новости
 */
function analyzeSource(source, sourceList) {
  for (const sourceKeyword of sourceList) {
    if (source.includes(sourceKeyword)) {
      return 2; // Высокий вес для релевантного источника
    }
  }
  return 0;
}

/**
 * Специальная детекция whale активности
 */
function detectWhaleActivity(text) {
  let whaleScore = 0;

  // Поиск числовых значений с большими суммами
  const largeAmounts = text.match(/\$?\d+(?:,\d{3})*(?:\.\d+)?\s*(?:million|billion|m|b)\b/gi);
  if (largeAmounts) {
    whaleScore += largeAmounts.length * 2;
  }

  // Поиск адресов кошельков (упрощенно)
  const walletAddresses = text.match(/\b[13][a-km-zA-HJ-NP-Z1-9]{25,34}\b/g);
  if (walletAddresses) {
    whaleScore += walletAddresses.length * 1.5;
  }

  // Поиск транзакционных хешей
  const txHashes = text.match(/\b0x[a-fA-F0-9]{64}\b/g);
  if (txHashes) {
    whaleScore += txHashes.length * 1.5;
  }

  // Ключевые фразы whale активности
  const whalePatterns = [
    /large.{0,20}transfer/i,
    /massive.{0,20}movement/i,
    /whale.{0,20}alert/i,
    /institutional.{0,20}buy/i,
    /significant.{0,20}accumulation/i
  ];

  for (const pattern of whalePatterns) {
    if (pattern.test(text)) {
      whaleScore += 2;
    }
  }

  return whaleScore;
}

/**
 * Генерирует теги на основе анализа
 */
function generateTags(text, scores) {
  const tags = [];

  // Добавляем теги на основе доминирующей категории
  if (scores.crypto > scores.stocks && scores.crypto > scores.whales) {
    tags.push('Crypto');
    if (text.includes('bitcoin') || text.includes('btc')) tags.push('Bitcoin');
    if (text.includes('ethereum') || text.includes('eth')) tags.push('Ethereum');
    if (text.includes('defi')) tags.push('DeFi');
  }

  if (scores.stocks > scores.crypto && scores.stocks > scores.whales) {
    tags.push('Stocks');
    if (text.includes('earnings')) tags.push('Earnings');
    if (text.includes('ipo')) tags.push('IPO');
    if (text.includes('dividend')) tags.push('Dividend');
  }

  if (scores.whales > scores.crypto && scores.whales > scores.stocks) {
    tags.push('Whales');
    if (text.includes('institutional')) tags.push('Institutional');
    if (text.includes('large')) tags.push('Large Volume');
  }

  return tags.slice(0, 5); // Ограничиваем количество тегов
}

/**
 * Определяет релевантность новости для финансовых рынков
 */
function isFinanciallyRelevant(newsItem) {
  const classification = classifyNews(newsItem);
  return classification.confidence > 0.3 || classification.primaryCategory !== 'general';
}

module.exports = {
  classifyNews,
  isFinanciallyRelevant,
  CRYPTO_KEYWORDS,
  STOCK_KEYWORDS,
  WHALE_KEYWORDS
};
