const { getSourceQuality, analyzeContentQuality } = require('../config/premiumSources');

const TOP_SOURCES = [
  'CoinDesk', 'Cointelegraph', 'Bloomberg', 'Reuters', 'Yahoo Finance',
  'The Block', 'Decrypt', 'Forbes', 'WSJ', 'CNBC', 'Financial Times',
  'MarketWatch', 'Investing.com', 'Barron', 'Business Insider', 'Fortune',
  'CryptoSlate', 'CryptoPotato', 'U.Today', 'NewsBTC', 'Bitcoin Magazine',
];

const KEYWORDS = [
  // Крипто
  'bitcoin','btc','ethereum','eth','solana','sol','defi','nft','blockchain','airdrop','web3','layer 2','zk','rollup','token','altcoin','stablecoin','usdt','usdc','binance','coinbase',
  // Мем-койны
  'memecoin','meme coin','doge','shiba','pepe','bonk','floki','wojak',
  // Топ-20 крипто
  'btc','eth','usdt','usdc','bnb','sol','xrp','ada','doge','ton','avax','dot','shib','trx','link','matic','wbtc','uni','ltc','dai',
  // Стоки
  'tesla','apple','microsoft','amazon','google','stock','nasdaq','s&p','dow','earnings','ipo','buyback','dividend','fed','inflation','interest rate','sec','regulation','etf','index','futures','options','short','long','bull','bear',
  // Политика
  'election','trump','biden','putin','sanctions','law','congress','parliament','court','lawsuit','investigation','scandal','war','conflict','g7','g20','opec','brics','eu','nato','china','russia','usa','ukraine',
];

function containsKeyword(text) {
  const lower = text.toLowerCase();
  return KEYWORDS.some(word => lower.includes(word));
}

function isTopSource(source) {
  return TOP_SOURCES.some(s => source.toLowerCase().includes(s.toLowerCase()));
}

function filterRelevantNews(newsList) {
  console.log('[FILTER] Начало фильтрации новостей');
  console.log('[FILTER] Входной массив:', newsList.length);

  const now = Date.now();
  const qualityThreshold = parseInt(process.env.QUALITY_THRESHOLD) || 7;
  const premiumOnly = process.env.PREMIUM_SOURCES_ONLY === 'true';

  const stats = {
    total: newsList.length,
    filtered: 0,
    byReason: {
      noDate: 0,
      tooOld: 0,
      tooShort: 0,
      lowQuality: 0,
      notPremium: 0
    }
  };

  const result = newsList.filter(news => {
    // Только свежие (за 24 часа)
    if (!news.publishedAt) {
      stats.byReason.noDate++;
      return false;
    }

    const published = new Date(news.publishedAt).getTime();
    if (isNaN(published) || now - published > 1000 * 60 * 60 * 24) {
      stats.byReason.tooOld++;
      return false;
    }

    // Минимальная длина контента
    if ((news.title || '').length < 20) {
      stats.byReason.tooShort++;
      return false;
    }
    if ((news.description || '').length < 30) {
      stats.byReason.tooShort++;
      return false;
    }

    // Анализ качества источника и контента
    const sourceQuality = getSourceQuality(news.source || '');
    const contentQuality = analyzeContentQuality(
      news.title || '',
      news.description || '',
      news.content || '',
      news.source || ''
    );

    // Если включен режим "только премиум источники"
    if (premiumOnly && sourceQuality.quality < 8) {
      stats.byReason.notPremium++;
      return false;
    }

    // Фильтрация по общему качеству
    const overallQuality = Math.max(sourceQuality.quality, contentQuality.score);
    if (overallQuality < qualityThreshold) {
      stats.byReason.lowQuality++;
      return false;
    }

    // Дополнительные проверки для качества контента
    const text = `${news.title} ${news.description}`.toLowerCase();

    // Исключаем спам и низкокачественный контент
    const spamIndicators = [
      'click here', 'limited time', 'act now', 'exclusive offer',
      'you won\'t believe', 'shocking', 'must see', 'urgent'
    ];

    const hasSpam = spamIndicators.some(indicator => text.includes(indicator));
    if (hasSpam) {
      stats.byReason.lowQuality++;
      return false;
    }

    return true;
  });
  
  stats.filtered = result.length;
  console.log('[FILTER] Результаты фильтрации:', stats);
  
  if (result.length > 0) {
    console.log('[FILTER] Примеры отфильтрованных новостей:', result.slice(0, 3).map(n => ({
      title: n.title,
      source: n.source,
      publishedAt: n.publishedAt
    })));
  }
  
  return result;
}

module.exports = { filterRelevantNews }; 