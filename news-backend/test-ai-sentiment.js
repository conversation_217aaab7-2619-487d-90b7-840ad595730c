// Тестовый скрипт для проверки улучшенного AI агента
const { analyzeNews } = require('./src/ai/aiAgent');

// Тестовые новости для проверки строгой логики
const testNews = [
  {
    id: 'test1',
    title: "Bitcoin price analysis suggests potential resistance at $50K",
    description: "Technical analysts believe Bitcoin could face resistance at the $50,000 level based on historical patterns and current market structure.",
    content: "Market experts are closely watching key resistance levels as Bitcoin approaches the psychologically important $50,000 mark. Historical data suggests this level has acted as significant resistance in previous cycles.",
    source: "CryptoAnalyst",
    expectedSentiment: "neutral",
    expectedScore: "40-60",
    reason: "Technical analysis and predictions should be neutral"
  },
  {
    id: 'test2',
    title: "SEC officially approves Bitcoin ETF, trading begins Monday",
    description: "The Securities and Exchange Commission has officially approved the first Bitcoin ETF with confirmed $2 billion initial investment from institutional investors.",
    content: "In a landmark decision, the SEC has granted approval for the first Bitcoin exchange-traded fund. The ETF will begin trading on Monday with BlackRock confirming a $2 billion initial investment. This marks a significant milestone for cryptocurrency adoption in traditional finance.",
    source: "Reuters",
    expectedSentiment: "positive",
    expectedScore: "85-100",
    reason: "Concrete regulatory approval with confirmed investment"
  },
  {
    id: 'test3',
    title: "Major crypto exchange hacked, $500M stolen from user funds",
    description: "Hackers have successfully breached the security of a major cryptocurrency exchange, stealing approximately $500 million in user funds.",
    content: "A major security breach has occurred at one of the world's largest cryptocurrency exchanges. Hackers managed to steal approximately $500 million in user funds. The exchange has suspended all trading and withdrawals while investigating the incident.",
    source: "CoinDesk",
    expectedSentiment: "negative",
    expectedScore: "0-25",
    reason: "Concrete security breach with quantified losses"
  },
  {
    id: 'test4',
    title: "Analyst predicts Bitcoin could reach $100K by end of year",
    description: "A prominent cryptocurrency analyst believes Bitcoin has the potential to reach $100,000 by the end of the year based on current market trends.",
    content: "According to a well-known market analyst, Bitcoin could potentially reach the $100,000 mark before the end of the year. The prediction is based on technical analysis and historical market patterns.",
    source: "CryptoNews",
    expectedSentiment: "neutral",
    expectedScore: "40-60",
    reason: "Speculation and predictions should be neutral"
  },
  {
    id: 'test5',
    title: "Apple exploring blockchain integration for payment systems",
    description: "Apple is reportedly exploring the integration of blockchain technology into its payment systems, according to sources familiar with the matter.",
    content: "Sources close to Apple suggest the company is investigating how blockchain technology could be integrated into Apple Pay and other payment services. However, no concrete plans or timeline have been announced.",
    source: "TechCrunch",
    expectedSentiment: "neutral",
    expectedScore: "50-65",
    reason: "Exploration and potential plans, not concrete action"
  },
  {
    id: 'test6',
    title: "Bitcoin trading volume increases 20% in past week",
    description: "Bitcoin trading volume has increased by 20% over the past week, indicating growing market activity among both retail and institutional investors.",
    content: "Data shows that Bitcoin trading volume has surged by 20% in the past week. The increase suggests renewed interest from market participants, though price action remains relatively stable.",
    source: "CoinGecko",
    expectedSentiment: "neutral",
    expectedScore: "55-65",
    reason: "Volume statistics without clear directional impact"
  }
];

async function testAISentiment() {
  console.log('🤖 Testing Enhanced AI Agent Sentiment Analysis\n');
  console.log('=' .repeat(80));

  let correctPredictions = 0;
  let totalTests = testNews.length;

  for (let i = 0; i < testNews.length; i++) {
    const news = testNews[i];
    console.log(`\n📰 Test ${i + 1}: ${news.title}`);
    console.log(`Expected: ${news.expectedSentiment.toUpperCase()} (${news.expectedScore})`);
    console.log(`Reason: ${news.reason}`);
    
    try {
      console.log('🔄 Analyzing with AI...');
      const result = await analyzeNews(news);
      
      const aiSentiment = result.sentiment || 'unknown';
      const aiScore = result.sentimentAnalysis?.sentimentScore || 0;
      const justification = result.sentimentAnalysis?.sentimentJustification || 'No justification provided';
      
      // Проверяем правильность
      const sentimentCorrect = aiSentiment === news.expectedSentiment;
      let scoreCorrect = false;
      
      if (news.expectedScore.includes('-')) {
        const [min, max] = news.expectedScore.split('-').map(Number);
        scoreCorrect = aiScore >= min && aiScore <= max;
      } else {
        scoreCorrect = aiScore == parseInt(news.expectedScore);
      }
      
      const isCorrect = sentimentCorrect && scoreCorrect;
      if (isCorrect) correctPredictions++;
      
      console.log(`🎯 AI Result: ${aiSentiment.toUpperCase()} (${aiScore}) ${isCorrect ? '✅' : '❌'}`);
      console.log(`💭 Justification: ${justification.slice(0, 100)}...`);
      
      if (!sentimentCorrect) {
        console.log(`   ❌ Sentiment mismatch: expected ${news.expectedSentiment}, got ${aiSentiment}`);
      }
      
      if (!scoreCorrect) {
        console.log(`   ❌ Score mismatch: expected ${news.expectedScore}, got ${aiScore}`);
      }
      
    } catch (error) {
      console.log(`❌ Error analyzing news: ${error.message}`);
    }
    
    // Небольшая пауза между запросами
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  console.log('\n' + '=' .repeat(80));
  console.log(`📊 Final Results: ${correctPredictions}/${totalTests} correct (${(correctPredictions / totalTests * 100).toFixed(1)}%)`);
  
  if (correctPredictions === totalTests) {
    console.log('🎉 Perfect score! AI agent is working correctly.');
  } else if (correctPredictions >= totalTests * 0.8) {
    console.log('✅ Good performance! Minor adjustments may be needed.');
  } else {
    console.log('⚠️  Performance needs improvement. Check prompts and logic.');
  }
  
  console.log('\n🎯 Key Observations:');
  console.log('- Most news should be classified as NEUTRAL (25-85)');
  console.log('- Only concrete, verified events should get extreme scores');
  console.log('- Speculation and predictions should always be neutral');
  console.log('- AI should provide clear justification for each score');
}

// Запускаем тест
testAISentiment().catch(console.error);
