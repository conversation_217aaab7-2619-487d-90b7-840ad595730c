// Простой тест для проверки CoinDesk
const { extractFullContent } = require('./src/adapters/premiumCrypto');

async function testCoinDesk() {
  console.log('🔧 Testing CoinDesk Content Extraction\n');
  console.log('=' .repeat(80));
  
  try {
    // Тестируем извлечение контента из конкретной статьи CoinDesk
    const testUrl = 'https://www.coindesk.com/markets/2025/06/15/eth-holds-strong-is-it-the-digital-oil-powering-the-global-digital-economy';
    
    console.log(`🎯 Testing CoinDesk URL: ${testUrl}`);
    
    const content = await extractFullContent(testUrl);
    
    if (content && content.content) {
      console.log(`✅ Successfully extracted content:`);
      console.log(`   • Title: "${content.title}"`);
      console.log(`   • Content length: ${content.length} chars`);
      console.log(`   • Author: ${content.byline || 'N/A'}`);
      console.log(`   • Preview: "${content.content.slice(0, 300)}..."`);
      
      if (content.length > 1000) {
        console.log(`✅ Content is substantial (${content.length} chars)`);
      } else {
        console.log(`⚠️  Content might be too short (${content.length} chars)`);
      }
    } else {
      console.log(`❌ Failed to extract content`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
  
  console.log('\n🎉 CoinDesk test completed!');
}

testCoinDesk();
