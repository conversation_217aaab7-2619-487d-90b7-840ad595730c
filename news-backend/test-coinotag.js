// Простой тест для проверки Coinotag
const { fetchConotagNews, extractFullContent } = require('./src/adapters/premiumCrypto');

async function testCoinotag() {
  console.log('🔧 Testing Coinotag Integration\n');
  console.log('=' .repeat(80));
  
  try {
    // Тестируем извлечение контента из конкретной статьи
    const testUrl = 'https://en.coinotag.com/drws-100-million-investment-in-trump-medias-bitcoin-plan-signals-growing-institutional-interest-amid-regulatory-scrutiny/';
    
    console.log(`🎯 Testing specific URL: ${testUrl}`);
    
    const content = await extractFullContent(testUrl);
    
    if (content && content.content) {
      console.log(`✅ Successfully extracted content:`);
      console.log(`   • Title: "${content.title}"`);
      console.log(`   • Content length: ${content.length} chars`);
      console.log(`   • Author: ${content.byline || 'N/A'}`);
      console.log(`   • Preview: "${content.content.slice(0, 200)}..."`);
    } else {
      console.log(`❌ Failed to extract content`);
    }
    
    console.log('\n' + '=' .repeat(80));
    
    // Тестируем RSS фид
    console.log('🎯 Testing Coinotag RSS feed...');
    const news = await fetchConotagNews();
    
    console.log(`✅ Fetched ${news.length} news items from Coinotag`);
    
    if (news.length > 0) {
      const firstNews = news[0];
      console.log(`📰 First news item:`);
      console.log(`   • Title: "${firstNews.title}"`);
      console.log(`   • Content length: ${firstNews.content?.length || 0} chars`);
      console.log(`   • URL: ${firstNews.url}`);
      console.log(`   • Source: ${firstNews.source}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
  
  console.log('\n🎉 Test completed!');
}

testCoinotag();
