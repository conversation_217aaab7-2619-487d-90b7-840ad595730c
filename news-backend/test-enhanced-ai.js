// Тестовый скрипт для проверки улучшенного AI агента с рыночными данными и памятью
const { analyzeNews } = require('./src/ai/aiAgent');
const { marketDataService } = require('./src/services/marketData');
const { contextualMemory } = require('./src/services/contextualMemory');

// Тестовые новости для проверки новых возможностей
const testNews = [
  {
    id: 'enhanced_test1',
    title: "Bitcoin breaks through $50K resistance amid institutional buying",
    description: "Bitcoin has successfully broken through the $50,000 resistance level with significant institutional buying pressure driving the move higher.",
    content: "Bitcoin's price action today shows a clear breakout above the $50,000 resistance level that has held for several weeks. The move comes amid reports of increased institutional buying from major investment firms. Trading volume has increased by 40% in the past 24 hours, suggesting strong conviction behind the move.",
    source: "CoinDesk",
    publishedAt: new Date().toISOString(),
    tags: ['bitcoin', 'price', 'institutional']
  },
  {
    id: 'enhanced_test2',
    title: "SEC announces new cryptocurrency regulations framework",
    description: "The Securities and Exchange Commission has unveiled a comprehensive regulatory framework for cryptocurrency trading and custody services.",
    content: "The SEC's new framework provides clear guidelines for cryptocurrency exchanges, custody services, and institutional trading. The regulations are designed to protect investors while fostering innovation in the digital asset space. Industry experts view this as a positive development for mainstream adoption.",
    source: "Reuters",
    publishedAt: new Date().toISOString(),
    tags: ['regulation', 'sec', 'framework']
  },
  {
    id: 'enhanced_test3',
    title: "Major DeFi protocol suffers $100M exploit",
    description: "A popular decentralized finance protocol has been exploited for approximately $100 million due to a smart contract vulnerability.",
    content: "The exploit occurred when attackers discovered a vulnerability in the protocol's smart contract code, allowing them to drain approximately $100 million from the platform. The team has paused all operations and is working with security firms to investigate the incident. This is the largest DeFi exploit this year.",
    source: "The Block",
    publishedAt: new Date().toISOString(),
    tags: ['defi', 'exploit', 'security']
  }
];

async function testEnhancedAI() {
  console.log('🚀 Testing Enhanced AI Agent with Market Data & Memory\n');
  console.log('=' .repeat(80));

  try {
    // Инициализируем сервисы
    console.log('🔧 Initializing services...');
    await contextualMemory.initialize();
    marketDataService.startMonitoring();
    
    // Ждем немного для получения рыночных данных
    console.log('📊 Waiting for market data...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    for (let i = 0; i < testNews.length; i++) {
      const news = testNews[i];
      console.log(`\n📰 Test ${i + 1}: ${news.title}`);
      console.log('─'.repeat(60));
      
      try {
        console.log('🤖 Analyzing with enhanced AI...');
        const result = await analyzeNews(news);
        
        // Выводим основные результаты
        console.log(`🎯 Sentiment: ${result.sentiment?.toUpperCase()} (${result.sentimentAnalysis?.sentimentScore})`);
        console.log(`💭 Justification: ${result.sentimentAnalysis?.sentimentJustification?.slice(0, 100)}...`);
        
        // Выводим рыночный контекст
        if (result.marketContext) {
          console.log(`📊 Market Context:`);
          console.log(`   - Condition: ${result.marketContext.marketCondition}`);
          console.log(`   - Volatility: ${result.marketContext.volatility}`);
          if (result.marketContext.anomalies?.length > 0) {
            console.log(`   - Anomalies: ${result.marketContext.anomalies.length} detected`);
          }
        }
        
        // Выводим исторический контекст
        if (result.historicalContext?.hasContext) {
          console.log(`🧠 Historical Context:`);
          console.log(`   - Similar events: ${result.historicalContext.similarEventsCount}`);
          console.log(`   - Pattern: ${result.historicalContext.historicalPattern?.predominantOutcome}`);
          console.log(`   - Confidence: ${(result.historicalContext.confidence * 100).toFixed(1)}%`);
        } else {
          console.log(`🧠 Historical Context: No similar events found`);
        }
        
        // Выводим рекомендации памяти
        if (result.memoryRecommendations?.confidence > 0.3) {
          console.log(`💡 Memory Recommendations:`);
          console.log(`   - Recommendation: ${result.memoryRecommendations.recommendation}`);
          console.log(`   - Confidence: ${(result.memoryRecommendations.confidence * 100).toFixed(1)}%`);
          console.log(`   - Reasoning: ${result.memoryRecommendations.reasoning}`);
        }
        
        // Проверяем влияние контекста на анализ
        if (result.sentimentAnalysis?.marketDataInfluence) {
          console.log(`📈 Market Data Influence: ${result.sentimentAnalysis.marketDataInfluence.slice(0, 80)}...`);
        }
        
        if (result.sentimentAnalysis?.historicalContext) {
          console.log(`📚 Historical Reference: ${result.sentimentAnalysis.historicalContext.slice(0, 80)}...`);
        }
        
        console.log(`✅ Analysis completed successfully`);
        
      } catch (error) {
        console.log(`❌ Error analyzing news: ${error.message}`);
      }
      
      // Пауза между анализами
      if (i < testNews.length - 1) {
        console.log('\n⏳ Waiting before next analysis...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    // Тестируем рыночные данные
    console.log('\n' + '=' .repeat(80));
    console.log('📊 Testing Market Data Service');
    console.log('─'.repeat(40));
    
    const marketData = marketDataService.getCurrentMarketData();
    const anomalies = marketDataService.getCurrentAnomalies();
    const context = marketDataService.getMarketContext();
    
    console.log(`Market Data Available: ${Object.keys(marketData.crypto || {}).length} crypto assets`);
    console.log(`Stock Data Available: ${Object.keys(marketData.stocks || {}).length} stock indices`);
    console.log(`Current Anomalies: ${anomalies.length}`);
    console.log(`Market Condition: ${context.marketCondition}`);
    console.log(`Volatility: ${context.volatility}`);
    
    if (anomalies.length > 0) {
      console.log('\n🚨 Current Market Anomalies:');
      anomalies.slice(0, 3).forEach(anomaly => {
        console.log(`   - ${anomaly.asset}: ${anomaly.change.toFixed(2)}% (${anomaly.severity})`);
      });
    }

    // Тестируем память
    console.log('\n' + '=' .repeat(80));
    console.log('🧠 Testing Contextual Memory');
    console.log('─'.repeat(40));
    
    console.log(`Memory Initialized: ${contextualMemory.initialized}`);
    console.log(`Historical Events: ${contextualMemory.memory?.historicalEvents?.length || 0}`);
    console.log(`Patterns: ${Object.keys(contextualMemory.memory?.patterns || {}).length}`);
    console.log(`Learning Data: ${contextualMemory.memory?.learningData?.length || 0}`);

    console.log('\n🎉 Enhanced AI testing completed!');
    console.log('\n🎯 Key Improvements:');
    console.log('✅ Real-time market data integration');
    console.log('✅ Historical context and pattern recognition');
    console.log('✅ Memory-based recommendations');
    console.log('✅ Enhanced sentiment analysis with context');
    console.log('✅ Market anomaly detection');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    // Останавливаем сервисы
    marketDataService.stopMonitoring();
    console.log('\n🛑 Services stopped');
  }
}

// Запускаем тест
testEnhancedAI().catch(console.error);
