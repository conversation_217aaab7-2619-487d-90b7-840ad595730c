// Тест улучшений: фильтр по длине, сортировка, время кэша
const { analyzeNews } = require('./src/ai/aiAgent');
const { sequentialAnalyzer } = require('./src/services/sequentialAnalyzer');

async function testImprovements() {
  console.log('🧪 Testing News Processing Improvements\n');
  console.log('=' .repeat(80));
  
  // Тестовые новости с разной длиной контента
  const testNews = [
    {
      id: 'test_1',
      title: 'Short News Item',
      description: 'Very short description.',
      content: 'This is a very short content that should be skipped.',
      publishedAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 мин назад
      source: 'Test Source',
      url: 'https://test.com/1'
    },
    {
      id: 'test_2', 
      title: 'Long News Item About Bitcoin Market Analysis',
      description: 'Detailed analysis of Bitcoin market trends and institutional adoption.',
      content: 'This is a much longer content that should pass the 500 character filter. '.repeat(10) + 
               'Bitcoin has been showing strong institutional adoption with major companies adding it to their treasury reserves. ' +
               'The market sentiment remains positive despite short-term volatility. Technical analysis suggests potential for further upward movement.',
      publishedAt: new Date(Date.now() - 1000 * 60 * 10).toISOString(), // 10 мин назад
      source: 'Test Source',
      url: 'https://test.com/2'
    },
    {
      id: 'test_3',
      title: 'Medium Length News',
      description: 'Medium length description with some details.',
      content: 'This content is of medium length and should also pass the filter. '.repeat(8) +
               'It contains enough information to be considered valuable for analysis.',
      publishedAt: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 мин назад
      source: 'Test Source', 
      url: 'https://test.com/3'
    }
  ];

  console.log('🎯 Testing Content Length Filter...\n');
  
  for (let i = 0; i < testNews.length; i++) {
    const news = testNews[i];
    const contentLength = (news.content || '').length;
    
    console.log(`📰 News ${i + 1}: "${news.title}"`);
    console.log(`   📏 Content length: ${contentLength} characters`);
    console.log(`   ⏰ Published: ${new Date(news.publishedAt).toLocaleString()}`);
    
    try {
      const result = await analyzeNews(news);
      
      if (result === null) {
        console.log(`   ⏭️  SKIPPED: Content too short (< 500 chars)`);
      } else {
        console.log(`   ✅ PROCESSED: Analysis completed`);
        console.log(`   🎭 Sentiment: ${result.sentiment}`);
        console.log(`   ⏰ Cached at: ${result.cachedAt || 'Not set'}`);
      }
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
    }
    
    console.log('');
  }

  console.log('=' .repeat(80));
  console.log('📊 Test Summary:');
  console.log('   • Short content (< 500 chars) should be skipped');
  console.log('   • Long content (> 500 chars) should be processed');
  console.log('   • cachedAt timestamp should be added to processed news');
  console.log('   • News should be sorted newest to oldest');
  console.log('=' .repeat(80));
  
  console.log('\n🎉 Content filter test completed!');
}

testImprovements().catch(console.error);
