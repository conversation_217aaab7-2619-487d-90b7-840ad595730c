// Демонстрационный скрипт для презентации новых возможностей
const { startSequentialAnalysis, sequentialAnalyzer } = require('./src/services/newsService');
const { marketDataService } = require('./src/services/marketData');
const { contextualMemory } = require('./src/services/contextualMemory');

// Демонстрационные новости для презентации
const demoNews = [
  {
    id: 'demo_1',
    title: "Bitcoin ETF sees record $2.1B inflows in single day",
    description: "The newly approved Bitcoin ETF has attracted unprecedented institutional investment with $2.1 billion in net inflows during its first trading day.",
    content: "In a historic milestone for cryptocurrency adoption, the first Bitcoin exchange-traded fund has recorded $2.1 billion in net inflows on its inaugural trading day. Major institutional investors including pension funds and hedge funds contributed to the massive influx of capital. The ETF's success has been attributed to growing institutional acceptance of Bitcoin as a legitimate asset class and the regulatory clarity provided by the SEC's approval.",
    source: "Bloomberg",
    publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 часа назад
    url: "https://example.com/bitcoin-etf-record-inflows",
    tags: ['bitcoin', 'etf', 'institutional']
  },
  {
    id: 'demo_2',
    title: "Major DeFi protocol exploited for $150M in flash loan attack",
    description: "Hackers have drained $150 million from a popular DeFi lending protocol using a sophisticated flash loan attack targeting a smart contract vulnerability.",
    content: "A major decentralized finance protocol has fallen victim to a $150 million exploit in what security experts are calling one of the most sophisticated flash loan attacks to date. The attackers exploited a vulnerability in the protocol's price oracle mechanism, allowing them to manipulate asset prices and drain funds from the lending pools. The protocol team has paused all operations and is working with blockchain security firms to investigate the incident. This marks the largest DeFi exploit of the year and has raised concerns about smart contract security across the ecosystem.",
    source: "CoinDesk",
    publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 часа назад
    url: "https://example.com/defi-exploit-150m",
    tags: ['defi', 'security', 'exploit']
  },
  {
    id: 'demo_3',
    title: "Federal Reserve hints at potential digital dollar pilot program",
    description: "Fed Chair Jerome Powell suggests the central bank is seriously considering a digital dollar pilot program following successful trials by other major economies.",
    content: "Federal Reserve Chairman Jerome Powell indicated during a congressional hearing that the central bank is actively exploring the possibility of launching a digital dollar pilot program. Powell cited successful central bank digital currency (CBDC) implementations in China and the European Union as examples of how digital currencies could enhance monetary policy effectiveness. However, he emphasized that any U.S. digital dollar would require congressional approval and extensive public consultation. The announcement comes amid growing pressure from lawmakers and financial institutions to modernize the U.S. payment system.",
    source: "Reuters",
    publishedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 часов назад
    url: "https://example.com/fed-digital-dollar-pilot",
    tags: ['cbdc', 'federal-reserve', 'regulation']
  },
  {
    id: 'demo_4',
    title: "Crypto analyst predicts Bitcoin could reach $150K by 2025",
    description: "A prominent cryptocurrency analyst believes Bitcoin has the potential to reach $150,000 by 2025 based on institutional adoption trends and supply dynamics.",
    content: "According to a well-known cryptocurrency analyst, Bitcoin could potentially reach $150,000 by 2025 if current institutional adoption trends continue. The prediction is based on several factors including the upcoming Bitcoin halving event, increasing corporate treasury adoption, and the potential approval of additional Bitcoin ETFs. The analyst points to historical price patterns and the diminishing supply of Bitcoin on exchanges as key indicators supporting the bullish outlook. However, the prediction comes with significant caveats about market volatility and regulatory uncertainties.",
    source: "CryptoSlate",
    publishedAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 часов назад
    url: "https://example.com/bitcoin-150k-prediction",
    tags: ['bitcoin', 'prediction', 'analysis']
  },
  {
    id: 'demo_5',
    title: "Tesla reports 15% increase in Bitcoin holdings value",
    description: "Tesla's quarterly earnings report shows a 15% increase in the value of its Bitcoin holdings, contributing $47 million to quarterly profits.",
    content: "Tesla's latest quarterly earnings report reveals that the company's Bitcoin holdings have appreciated by 15% during the quarter, contributing approximately $47 million to the company's overall profits. The electric vehicle manufacturer, which holds approximately 9,720 Bitcoin on its balance sheet, has not made any additional purchases or sales during the reporting period. CEO Elon Musk reiterated the company's long-term commitment to holding Bitcoin as a treasury asset, citing its potential as a hedge against inflation and currency debasement.",
    source: "TechCrunch",
    publishedAt: new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString(), // 10 часов назад
    url: "https://example.com/tesla-bitcoin-holdings-increase",
    tags: ['tesla', 'bitcoin', 'earnings']
  }
];

async function runPresentationDemo() {
  console.log('🎬 PRESENTATION DEMO: Enhanced AI News Analysis System');
  console.log('=' .repeat(80));
  console.log('🚀 Demonstrating:');
  console.log('   • Sequential analysis with 2-minute intervals');
  console.log('   • Real-time market data integration');
  console.log('   • Contextual memory and historical patterns');
  console.log('   • Detailed AI reasoning logs');
  console.log('   • Analysis from oldest to newest (24h window)');
  console.log('=' .repeat(80));

  try {
    // Инициализируем сервисы
    console.log('\n🔧 INITIALIZING SERVICES...');
    await contextualMemory.initialize();
    marketDataService.startMonitoring();
    
    console.log('✅ Contextual memory initialized');
    console.log('✅ Market data monitoring started');
    
    // Ждем получения рыночных данных
    console.log('\n📊 LOADING MARKET DATA...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const marketContext = marketDataService.getMarketContext();
    console.log(`✅ Market condition: ${marketContext.marketCondition}`);
    console.log(`✅ Volatility: ${marketContext.volatility}`);
    
    // Добавляем демонстрационные новости в систему
    console.log('\n📰 PREPARING DEMO NEWS...');
    const { newsFeed } = require('./src/services/newsService');
    
    // Очищаем существующие новости для демо
    newsFeed.length = 0;
    
    // Добавляем демонстрационные новости
    demoNews.forEach(news => {
      newsFeed.push(news);
    });
    
    console.log(`✅ Added ${demoNews.length} demo news items`);
    console.log('✅ News sorted from oldest to newest');
    
    // Настраиваем обработчики событий для детального логирования
    sequentialAnalyzer.on('newsAnalyzed', (data) => {
      console.log('\n🎯 NEWS ANALYSIS COMPLETED:');
      console.log(`   📰 Title: ${data.news.title}`);
      console.log(`   🎭 Sentiment: ${data.news.sentiment} (${data.news.sentimentAnalysis?.sentimentScore})`);
      console.log(`   📊 Progress: ${(data.progress * 100).toFixed(1)}%`);
      console.log(`   ⏱️  Remaining: ${data.remaining} news items`);
      
      if (data.news.marketContext) {
        console.log(`   📈 Market influence: ${data.news.marketContext.marketCondition} market`);
      }
      
      if (data.news.historicalContext?.hasContext) {
        console.log(`   🧠 Historical context: ${data.news.historicalContext.similarEventsCount} similar events`);
      }
    });
    
    sequentialAnalyzer.on('analysisCompleted', (data) => {
      console.log('\n🎉 ANALYSIS COMPLETED!');
      console.log('📊 Final Statistics:');
      console.log(`   • Total analyzed: ${data.stats.analyzed}`);
      console.log(`   • Positive: ${data.stats.positive}`);
      console.log(`   • Neutral: ${data.stats.neutral}`);
      console.log(`   • Negative: ${data.stats.negative}`);
      console.log(`   • Average time: ${(data.stats.averageTime / 1000).toFixed(1)}s`);
      console.log(`   • Total time: ${(data.totalTime / 1000 / 60).toFixed(1)} minutes`);
    });
    
    // Запускаем последовательный анализ
    console.log('\n🚀 STARTING SEQUENTIAL ANALYSIS...');
    console.log('⏱️  Analysis interval: 2 minutes between news items');
    console.log('📅 Processing last 24 hours of news');
    console.log('🔄 Order: Oldest to newest');
    
    await startSequentialAnalysis();
    
    // Показываем информацию о веб-дашборде
    console.log('\n🌐 WEB DASHBOARD AVAILABLE:');
    console.log('   📊 Real-time monitoring: http://localhost:4000/dashboard/dashboard.html');
    console.log('   🔍 API endpoints:');
    console.log('     • GET /admin/analysis/status - Current analysis status');
    console.log('     • POST /admin/analysis/start-sequential - Start analysis');
    console.log('     • POST /admin/analysis/stop - Stop analysis');
    console.log('     • GET /admin/market/data - Market data');
    console.log('     • GET /admin/memory/stats - Memory statistics');
    
    console.log('\n✨ PRESENTATION FEATURES DEMONSTRATED:');
    console.log('   ✅ Sequential processing with timing control');
    console.log('   ✅ Detailed AI reasoning logs');
    console.log('   ✅ Real-time market data integration');
    console.log('   ✅ Historical context and memory');
    console.log('   ✅ Progress tracking and ETA calculation');
    console.log('   ✅ Web dashboard for monitoring');
    console.log('   ✅ RESTful API for external integration');
    
    // Ждем завершения анализа
    return new Promise((resolve) => {
      sequentialAnalyzer.on('analysisCompleted', () => {
        console.log('\n🎬 PRESENTATION DEMO COMPLETED SUCCESSFULLY!');
        resolve();
      });
    });
    
  } catch (error) {
    console.error('❌ Demo failed:', error.message);
    throw error;
  }
}

// Функция для остановки демо
function stopDemo() {
  console.log('\n🛑 STOPPING PRESENTATION DEMO...');
  sequentialAnalyzer.stop();
  marketDataService.stopMonitoring();
  console.log('✅ Demo stopped successfully');
}

// Обработка сигналов для graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, stopping demo...');
  stopDemo();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, stopping demo...');
  stopDemo();
  process.exit(0);
});

// Запускаем демо
if (require.main === module) {
  runPresentationDemo().catch(error => {
    console.error('❌ Presentation demo failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runPresentationDemo,
  stopDemo,
  demoNews
};
