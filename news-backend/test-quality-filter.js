// Тестовый скрипт для проверки фильтрации по качеству контента
const { startSequentialAnalysis, sequentialAnalyzer } = require('./src/services/newsService');

// Функция для подсчета слов (копия из newsService)
function countWords(text) {
  if (!text || typeof text !== 'string') return 0;
  
  const cleanText = text
    .replace(/<[^>]*>/g, ' ')
    .replace(/[^\w\s]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
  
  if (!cleanText) return 0;
  
  return cleanText.split(' ').filter(word => word.length > 0).length;
}

// Тестовые новости с разным количеством слов
const testNews = [
  {
    id: 'short_news_1',
    title: "Bitcoin price rises",
    description: "Bitcoin went up today.",
    content: "Bitcoin price increased by 5% today. This is good news for investors. The market is bullish.",
    source: "TestSource",
    publishedAt: new Date().toISOString(),
    url: "https://test.com/short1"
  },
  {
    id: 'medium_news_1',
    title: "Ethereum network upgrade shows promising results",
    description: "The latest Ethereum network upgrade has demonstrated significant improvements in transaction throughput and gas fee reduction.",
    content: `The Ethereum network's latest upgrade, implemented last week, has shown remarkable improvements across multiple metrics. Transaction throughput has increased by approximately 40%, while average gas fees have decreased by 25%. The upgrade, which was months in development, introduces several key optimizations to the network's consensus mechanism.

Network validators have reported smoother operations and reduced computational overhead. The upgrade includes improvements to the proof-of-stake consensus algorithm, enhanced smart contract execution efficiency, and better resource allocation across the network. These changes are expected to make Ethereum more competitive with other blockchain platforms.

Industry experts have praised the upgrade's implementation, noting the seamless transition without any major network disruptions. Several major decentralized applications have already reported improved performance metrics. The upgrade represents a significant step forward in Ethereum's roadmap toward greater scalability and efficiency.

Market reaction has been positive, with ETH price showing steady gains following the upgrade announcement. Trading volume has increased as investors show renewed confidence in the platform's technical capabilities. The successful implementation has also boosted sentiment around upcoming planned upgrades.

Looking ahead, the Ethereum development team has outlined plans for additional improvements scheduled for the next quarter. These future upgrades will focus on further scalability enhancements and cross-chain interoperability features.`,
    source: "CryptoNews",
    publishedAt: new Date().toISOString(),
    url: "https://test.com/medium1"
  },
  {
    id: 'quality_news_1',
    title: "Federal Reserve announces comprehensive digital dollar framework",
    description: "The Federal Reserve has unveiled a detailed framework for the potential implementation of a central bank digital currency (CBDC), marking a significant milestone in the evolution of digital payments in the United States.",
    content: `The Federal Reserve has released a comprehensive 85-page framework document outlining the potential implementation of a central bank digital currency (CBDC) for the United States. This landmark announcement represents the culmination of three years of intensive research, stakeholder consultations, and pilot program testing conducted in collaboration with major financial institutions and technology partners.

The proposed digital dollar framework addresses key concerns around privacy, security, and monetary policy implementation while maintaining the Federal Reserve's dual mandate of price stability and full employment. The document outlines a two-tier system where the Federal Reserve would issue digital currency to authorized financial institutions, which would then distribute it to consumers and businesses through existing banking infrastructure.

Key features of the proposed system include offline transaction capabilities, programmable money features for government disbursements, and enhanced anti-money laundering controls. The framework emphasizes that any digital dollar would complement, not replace, physical cash and existing payment systems. Privacy protections would be built into the system's architecture, with transaction data encrypted and access strictly limited to authorized parties for legitimate purposes.

The announcement comes as other major economies accelerate their own CBDC development programs. China's digital yuan has already been deployed in several major cities, while the European Central Bank continues testing its digital euro prototype. The Federal Reserve's framework acknowledges this global context while emphasizing the unique requirements of the U.S. financial system.

Implementation would occur in phases, beginning with wholesale CBDC transactions between financial institutions before expanding to retail applications. The Federal Reserve estimates that full deployment could take 5-7 years, pending Congressional approval and successful completion of extensive testing phases. The framework includes provisions for interoperability with existing payment rails and international CBDC systems.

Industry reaction has been mixed, with traditional banks expressing concerns about disintermediation while fintech companies welcome the innovation potential. Consumer advocacy groups have praised the privacy protections while calling for additional safeguards against government surveillance. The framework includes provisions for public comment periods and ongoing stakeholder engagement throughout the development process.

The economic implications are significant, with potential impacts on monetary policy transmission, financial stability, and the international role of the dollar. The Federal Reserve's analysis suggests that a well-designed CBDC could enhance payment system efficiency, reduce settlement risks, and provide new tools for monetary policy implementation during crisis periods.

Technical specifications outlined in the framework include quantum-resistant cryptography, distributed ledger technology, and advanced privacy-preserving techniques. The system would be designed to handle peak transaction volumes exceeding current payment system capacities while maintaining sub-second settlement times for most transactions.

The announcement has prompted immediate responses from Congressional leaders, with several committees announcing plans for hearings on CBDC implementation. International partners have expressed interest in collaboration on cross-border payment solutions and regulatory harmonization efforts.

Looking ahead, the Federal Reserve has outlined a detailed timeline for public consultation, technical development, and pilot program expansion. The next phase will include broader stakeholder engagement and more extensive testing with retail partners. Success will depend on maintaining public trust while delivering tangible benefits over existing payment systems.`,
    source: "Federal Reserve",
    publishedAt: new Date().toISOString(),
    url: "https://test.com/quality1"
  }
];

async function testQualityFilter() {
  console.log('🔍 Testing Quality Content Filter (500+ words minimum)\n');
  console.log('=' .repeat(80));

  // Анализируем тестовые новости
  console.log('📊 Analyzing test news word counts:\n');
  
  testNews.forEach((news, index) => {
    const wordCount = countWords(news.content);
    const status = wordCount >= 500 ? '✅ PASS' : '❌ FAIL';
    
    console.log(`📰 Test ${index + 1}: ${news.title}`);
    console.log(`   📏 Word count: ${wordCount} words`);
    console.log(`   🎯 Status: ${status} (minimum: 500 words)`);
    console.log(`   📝 Content preview: ${news.content.slice(0, 100)}...`);
    console.log('');
  });

  console.log('=' .repeat(80));
  console.log('🧪 Expected Results:');
  console.log('   • Test 1 (Short): Should be FILTERED OUT (< 500 words)');
  console.log('   • Test 2 (Medium): Should be FILTERED OUT (< 500 words)');
  console.log('   • Test 3 (Quality): Should PASS FILTER (500+ words)');
  console.log('=' .repeat(80));

  try {
    // Очищаем кэш перед тестом
    console.log('\n🗑️  Clearing cache before test...');
    const { newsFeed } = require('./src/services/newsService');
    newsFeed.length = 0;
    
    // Добавляем тестовые новости в систему
    console.log('\n📰 Adding test news to system...');
    testNews.forEach(news => {
      newsFeed.push(news);
    });
    
    console.log(`✅ Added ${testNews.length} test news items to system`);
    
    // Запускаем последовательный анализ
    console.log('\n🚀 Starting sequential analysis with quality filter...');
    console.log('⏱️  Note: Only news with 500+ words should be processed\n');
    
    // Настраиваем обработчики событий
    let processedCount = 0;
    let filteredCount = 0;
    
    sequentialAnalyzer.on('newsAnalyzed', (data) => {
      processedCount++;
      console.log(`\n✅ NEWS PROCESSED ${processedCount}:`);
      console.log(`   📰 Title: ${data.news.title}`);
      console.log(`   🎭 Sentiment: ${data.news.sentiment}`);
      console.log(`   📏 Word count: ${countWords(data.news.content)} words`);
      console.log(`   📊 Progress: ${(data.progress * 100).toFixed(1)}%`);
    });
    
    sequentialAnalyzer.on('analysisCompleted', (data) => {
      console.log('\n🎉 ANALYSIS COMPLETED!');
      console.log('📊 Final Results:');
      console.log(`   • Total processed: ${processedCount}`);
      console.log(`   • Expected processed: 1 (only quality news)`);
      console.log(`   • Filter working: ${processedCount === 1 ? '✅ YES' : '❌ NO'}`);
      
      if (processedCount === 1) {
        console.log('\n🎯 SUCCESS: Quality filter is working correctly!');
        console.log('   Only news with 500+ words was processed.');
      } else {
        console.log('\n⚠️  WARNING: Quality filter may not be working as expected.');
      }
    });
    
    // Запускаем анализ
    await startSequentialAnalysis();
    
    // Ждем завершения
    return new Promise((resolve) => {
      sequentialAnalyzer.on('analysisCompleted', () => {
        console.log('\n🎬 Quality filter test completed!');
        resolve();
      });
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  }
}

// Функция для остановки теста
function stopTest() {
  console.log('\n🛑 Stopping quality filter test...');
  sequentialAnalyzer.stop();
  console.log('✅ Test stopped successfully');
}

// Обработка сигналов для graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, stopping test...');
  stopTest();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, stopping test...');
  stopTest();
  process.exit(0);
});

// Запускаем тест
if (require.main === module) {
  testQualityFilter().catch(error => {
    console.error('❌ Quality filter test failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testQualityFilter,
  stopTest,
  testNews,
  countWords
};
