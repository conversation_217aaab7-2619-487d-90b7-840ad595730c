const { loadNewsFeedCache } = require('./src/services/newsService');

async function testRewrittenContent() {
  console.log('🔍 ТЕСТИРОВАНИЕ REWRITTEN CONTENT В КЭШЕ\n');
  
  try {
    // Загружаем кэш
    const newsFeed = loadNewsFeedCache();
    console.log(`📰 Всего новостей в кэше: ${newsFeed.length}`);
    
    if (newsFeed.length === 0) {
      console.log('❌ Кэш пуст! Запустите сервер и дождитесь анализа новостей.');
      return;
    }
    
    let hasRewrittenContent = 0;
    let hasNoRewrittenContent = 0;
    let totalAnalyzed = 0;
    
    console.log('\n📋 АНАЛИЗ ПЕРВЫХ 10 НОВОСТЕЙ:');
    console.log('=' .repeat(80));
    
    newsFeed.slice(0, 10).forEach((news, index) => {
      const hasRewritten = news.rewrittenContent && news.rewrittenContent.trim().length > 0;
      const hasSentiment = news.sentiment && news.sentiment !== 'unknown';
      const hasAiTitle = news.aiGeneratedTitle && news.aiGeneratedTitle.trim().length > 0;
      const hasSummary = news.summary && news.summary.trim().length > 0;
      
      if (hasSentiment) totalAnalyzed++;
      if (hasRewritten) hasRewrittenContent++;
      else hasNoRewrittenContent++;
      
      console.log(`\n${index + 1}. "${news.title?.slice(0, 60)}..."`);
      console.log(`   🎯 Sentiment: ${news.sentiment || 'none'}`);
      console.log(`   📝 AI Title: ${hasAiTitle ? '✅' : '❌'} ${hasAiTitle ? `(${news.aiGeneratedTitle?.length} chars)` : ''}`);
      console.log(`   📄 Summary: ${hasSummary ? '✅' : '❌'} ${hasSummary ? `(${news.summary?.length} chars)` : ''}`);
      console.log(`   ✍️  Rewritten: ${hasRewritten ? '✅' : '❌'} ${hasRewritten ? `(${news.rewrittenContent?.length} chars)` : ''}`);
      console.log(`   📅 Cached: ${news.cachedAt || 'N/A'}`);
      console.log(`   🔗 URL: ${news.url?.slice(0, 50)}...`);
      
      if (hasRewritten && news.rewrittenContent) {
        console.log(`   📖 Preview: "${news.rewrittenContent.slice(0, 100)}..."`);
      }
    });
    
    console.log('\n\n📊 СТАТИСТИКА:');
    console.log('=' .repeat(50));
    console.log(`📰 Всего новостей: ${newsFeed.length}`);
    console.log(`🤖 Проанализировано AI: ${totalAnalyzed}`);
    console.log(`✅ С rewrittenContent: ${hasRewrittenContent}`);
    console.log(`❌ Без rewrittenContent: ${hasNoRewrittenContent}`);
    console.log(`📈 Процент с rewritten: ${totalAnalyzed > 0 ? Math.round((hasRewrittenContent / totalAnalyzed) * 100) : 0}%`);
    
    if (hasRewrittenContent === 0) {
      console.log('\n🚨 ПРОБЛЕМА: Ни одна новость не содержит rewrittenContent!');
      console.log('💡 Возможные причины:');
      console.log('   - AI не генерирует rewrittenContent');
      console.log('   - Ошибка в промпте AI');
      console.log('   - Проблема с сохранением в кэш');
      console.log('   - Новости еще не проанализированы');
    } else {
      console.log(`\n✅ Найдено ${hasRewrittenContent} новостей с rewrittenContent`);
    }
    
    // Проверяем API endpoint
    console.log('\n\n🌐 ТЕСТИРОВАНИЕ API ENDPOINT:');
    console.log('=' .repeat(50));
    
    const axios = require('axios');
    try {
      const response = await axios.get('http://localhost:4000/news?limit=3');
      const apiNews = response.data.news;
      
      console.log(`📡 API вернул ${apiNews.length} новостей`);
      
      apiNews.forEach((news, index) => {
        const hasRewritten = news.rewrittenContent && news.rewrittenContent.trim().length > 0;
        console.log(`\n${index + 1}. "${news.title?.slice(0, 60)}..."`);
        console.log(`   ✍️  Rewritten в API: ${hasRewritten ? '✅' : '❌'} ${hasRewritten ? `(${news.rewrittenContent?.length} chars)` : ''}`);
        
        if (hasRewritten) {
          console.log(`   📖 API Preview: "${news.rewrittenContent.slice(0, 100)}..."`);
        }
      });
      
    } catch (error) {
      console.log(`❌ Ошибка API: ${error.message}`);
      console.log('💡 Убедитесь, что сервер запущен на порту 4000');
    }
    
  } catch (error) {
    console.error('❌ Ошибка:', error.message);
  }
}

testRewrittenContent().catch(console.error);
