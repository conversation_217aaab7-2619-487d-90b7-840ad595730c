const axios = require('axios');

async function testBackend() {
  const baseUrl = 'http://localhost:4000';
  
  console.log('🧪 Тестирование бэкенда...\n');
  
  try {
    // Тест 1: Проверка статуса
    console.log('1. Проверка статуса сервера...');
    const statusResponse = await axios.get(`${baseUrl}/status`);
    console.log('✅ Статус:', statusResponse.data);
    
    // Тест 2: Получение новостей
    console.log('\n2. Получение новостей...');
    const newsResponse = await axios.get(`${baseUrl}/news?pageSize=5`);
    console.log('✅ Получено новостей:', newsResponse.data.news?.length || 0);
    
    if (newsResponse.data.news && newsResponse.data.news.length > 0) {
      const firstNews = newsResponse.data.news[0];
      console.log('\n📰 Пример новости:');
      console.log('Заголовок:', firstNews.title);
      console.log('Настроение:', firstNews.sentiment);
      console.log('Данные настроения:', firstNews.sentimentData);
      console.log('Теги:', firstNews.tags);
    }
    
    // Тест 3: Фильтрация по настроению
    console.log('\n3. Тест фильтрации по настроению...');
    const positiveNews = await axios.get(`${baseUrl}/news?sentiment=positive&pageSize=3`);
    console.log('✅ Позитивных новостей:', positiveNews.data.news?.length || 0);
    
    // Тест 4: Проверка SSE endpoint
    console.log('\n4. Проверка SSE endpoint...');
    try {
      const sseResponse = await axios.get(`${baseUrl}/news/stream`, { timeout: 2000 });
      console.log('✅ SSE endpoint доступен');
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        console.log('✅ SSE endpoint работает (таймаут ожидаем для потока)');
      } else {
        console.log('❌ SSE endpoint недоступен:', error.message);
      }
    }

    // Тест 5: Запуск парсинга
    console.log('\n5. Запуск парсинга новостей...');
    const parseResponse = await axios.post(`${baseUrl}/admin/parse`);
    console.log('✅ Парсинг запущен:', parseResponse.data);

    console.log('\n🎉 Все тесты прошли успешно!');
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
    if (error.response) {
      console.error('Статус:', error.response.status);
      console.error('Данные:', error.response.data);
    }
  }
}

// Запуск тестов
testBackend();
