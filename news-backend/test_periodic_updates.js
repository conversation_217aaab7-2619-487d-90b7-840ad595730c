const axios = require('axios');

const BASE_URL = 'http://localhost:4000';

async function testBackend() {
  console.log('🧪 Тестирование backend новостей...\n');

  try {
    // 1. Проверяем статус сервера
    console.log('1. Проверка статуса сервера...');
    const statusResponse = await axios.get(`${BASE_URL}/status`);
    console.log('✅ Статус:', statusResponse.data);
    console.log(`📊 Всего новостей в кэше: ${statusResponse.data.totalNews}`);
    console.log(`👥 Подключенных клиентов: ${statusResponse.data.connectedClients}\n`);

    // 2. Получаем текущие новости
    console.log('2. Получение текущих новостей...');
    const newsResponse = await axios.get(`${BASE_URL}/news?pageSize=5`);
    console.log('✅ Получено новостей:', newsResponse.data.news.length);
    
    if (newsResponse.data.news.length > 0) {
      console.log('📰 Последние новости:');
      newsResponse.data.news.slice(0, 3).forEach((news, index) => {
        console.log(`   ${index + 1}. ${news.title.substring(0, 80)}...`);
        console.log(`      📅 ${news.publishedAt}`);
        console.log(`      😊 Тональность: ${news.sentiment || 'не определена'}`);
        console.log(`      🏷️ Теги: ${news.tags?.join(', ') || 'нет'}\n`);
      });
    }

    // 3. Запускаем ручной парсинг для тестирования
    console.log('3. Запуск ручного парсинга новостей...');
    const parseResponse = await axios.post(`${BASE_URL}/admin/parse`);
    console.log('✅ Парсинг запущен:', parseResponse.data);
    console.log(`📈 Новостей к анализу: ${parseResponse.data.totalToAnalyze}\n`);

    // 4. Ждем немного и проверяем обновления
    console.log('4. Ожидание обновлений (30 секунд)...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    const updatedStatusResponse = await axios.get(`${BASE_URL}/status`);
    console.log('✅ Обновленный статус:', updatedStatusResponse.data);
    console.log(`📊 Всего новостей после обновления: ${updatedStatusResponse.data.totalNews}\n`);

    console.log('🎉 Тестирование завершено успешно!');
    console.log('⏰ Периодические обновления будут происходить каждые 10 минут');

  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
    if (error.response) {
      console.error('📄 Ответ сервера:', error.response.data);
    }
  }
}

// Запускаем тест
testBackend();
