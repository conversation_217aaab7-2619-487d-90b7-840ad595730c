// Тест для проверки разделения источников новостей

const { fetchAllTraditionalFinanceNews } = require('./src/adapters/newsapi');
const { fetchCryptoCompareNews } = require('./src/adapters/cryptocompare');
const { categorizeNewsContent } = require('./src/config/newsSourcesConfig');

async function testSourcesSeparation() {
  console.log('🧪 ТЕСТИРОВАНИЕ РАЗДЕЛЕНИЯ ИСТОЧНИКОВ НОВОСТЕЙ');
  console.log('=' .repeat(60));
  
  try {
    // Тест 1: Получение традиционных финансовых новостей
    console.log('\n📈 Тест 1: Традиционные финансовые новости (стоки, политика, экономика)');
    console.log('-'.repeat(40));
    
    const traditionalNews = await fetchAllTraditionalFinanceNews();
    console.log(`✅ Получено ${traditionalNews.length} традиционных финансовых новостей`);
    
    if (traditionalNews.length > 0) {
      console.log('\n📋 Примеры традиционных новостей:');
      traditionalNews.slice(0, 3).forEach((news, idx) => {
        console.log(`${idx + 1}. "${news.title?.slice(0, 80)}..." (${news.source})`);
        console.log(`   Категория: ${news.category || 'не определена'}`);
        console.log(`   Теги: ${news.tags?.join(', ') || 'нет тегов'}`);
      });
    }
    
    // Тест 2: Получение криптовалютных новостей
    console.log('\n\n🪙 Тест 2: Криптовалютные новости');
    console.log('-'.repeat(40));
    
    const cryptoNews = await fetchCryptoCompareNews();
    console.log(`✅ Получено ${cryptoNews.length} криптовалютных новостей`);
    
    if (cryptoNews.length > 0) {
      console.log('\n📋 Примеры криптовалютных новостей:');
      cryptoNews.slice(0, 3).forEach((news, idx) => {
        console.log(`${idx + 1}. "${news.title?.slice(0, 80)}..." (${news.source})`);
        console.log(`   Теги: ${news.tags?.join(', ') || 'нет тегов'}`);
      });
    }
    
    // Тест 3: Автоматическая категоризация
    console.log('\n\n🤖 Тест 3: Автоматическая категоризация новостей');
    console.log('-'.repeat(40));
    
    const testNews = [
      {
        title: 'Bitcoin reaches new all-time high amid institutional adoption',
        content: 'Bitcoin price surged to new heights as major institutions continue to adopt cryptocurrency...'
      },
      {
        title: 'Federal Reserve announces interest rate decision',
        content: 'The Federal Reserve announced its latest interest rate decision affecting stock markets...'
      },
      {
        title: 'Apple reports quarterly earnings beating expectations',
        content: 'Apple Inc. reported strong quarterly earnings with iPhone sales exceeding analyst expectations...'
      }
    ];
    
    testNews.forEach((news, idx) => {
      const category = categorizeNewsContent(news.title, news.content);
      console.log(`${idx + 1}. "${news.title}"`);
      console.log(`   Категория: ${category.type} (уверенность: ${(category.confidence * 100).toFixed(1)}%)`);
      console.log(`   Совпадения: ${category.matches.join(', ')}`);
      console.log('');
    });
    
    // Статистика
    console.log('\n📊 СТАТИСТИКА РАЗДЕЛЕНИЯ:');
    console.log('=' .repeat(40));
    console.log(`🏛️ Традиционные финансы: ${traditionalNews.length} новостей`);
    console.log(`🪙 Криптовалюты: ${cryptoNews.length} новостей`);
    console.log(`📈 Общее количество: ${traditionalNews.length + cryptoNews.length} новостей`);
    
    const traditionalPercentage = ((traditionalNews.length / (traditionalNews.length + cryptoNews.length)) * 100).toFixed(1);
    const cryptoPercentage = ((cryptoNews.length / (traditionalNews.length + cryptoNews.length)) * 100).toFixed(1);
    
    console.log(`📊 Соотношение: ${traditionalPercentage}% традиционные / ${cryptoPercentage}% крипто`);
    
    console.log('\n✅ ТЕСТ ЗАВЕРШЕН УСПЕШНО!');
    
  } catch (error) {
    console.error('❌ ОШИБКА ПРИ ТЕСТИРОВАНИИ:', error.message);
    console.error(error.stack);
  }
}

// Запуск теста
if (require.main === module) {
  testSourcesSeparation();
}

module.exports = { testSourcesSeparation };
