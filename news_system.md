# Система Новостей

## 1. Д<PERSON><PERSON><PERSON><PERSON><PERSON> и Интерфейс

### 1.1 Карточки Новостей
- **Адаптивный дизайн**: Поддержка мобильных и десктопных устройств
- **Типы карточек**:
  - Compact (компактная)
  - Standard (стандартная)
  - Large (большая)
  - Tall (высокая)
- **Элементы карточки**:
  - Изображение новости
  - Заголовок
  - Описание
  - Источник
  - Дата публикации
  - Индикатор настроения (sentiment)
  - Теги

#### 1.1.1 Размеры и Типы Карточек
```dart
enum CardSize {
  compact,  // 280x180
  standard, // 360x240
  large,    // 480x320
  tall      // 360x480
}

class CardDimensions {
  static const Map<CardSize, Size> sizes = {
    CardSize.compact: Size(280, 180),
    CardSize.standard: Size(360, 240),
    CardSize.large: Size(480, 320),
    CardSize.tall: Size(360, 480),
  };

  static const Map<CardSize, double> imageHeights = {
    CardSize.compact: 100,
    CardSize.standard: 140,
    CardSize.large: 200,
    CardSize.tall: 280,
  };
}
```

#### 1.1.2 Базовая Структура Карточки
```dart
class UltraGradientNewsCard extends StatefulWidget {
  final NewsItem newsItem;
  final CardSize size;
  final VoidCallback onTap;

  const UltraGradientNewsCard({
    Key? key,
    required this.newsItem,
    required this.size,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: CardDimensions.sizes[size]!.width,
      height: CardDimensions.sizes[size]!.height,
      decoration: BoxDecoration(
        gradient: _getCardGradient(),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: _buildCardContent(),
        ),
      ),
    );
  }
}
```

#### 1.1.3 Компоненты Карточки
```dart
Widget _buildCardContent() {
  return Stack(
    children: [
      // Фоновое изображение
      _buildBackgroundImage(),
      
      // Градиентный оверлей
      _buildGradientOverlay(),
      
      // Контент
      Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 8),
            _buildTitle(),
            const SizedBox(height: 8),
            _buildDescription(),
            const Spacer(),
            _buildFooter(),
          ],
        ),
      ),
      
      // Индикатор настроения
      Positioned(
        top: 12,
        right: 12,
        child: _buildSentimentIndicator(),
      ),
    ],
  );
}

Widget _buildHeader() {
  return Row(
    children: [
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          newsItem.source,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      const SizedBox(width: 8),
      Text(
        _formatDate(newsItem.publishedAt),
        style: const TextStyle(
          color: Colors.white70,
          fontSize: 12,
        ),
      ),
    ],
  );
}
```

#### 1.1.4 Анимации и Эффекты
```dart
class _UltraGradientNewsCardState extends State<UltraGradientNewsCard> 
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnim;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _scaleAnim = Tween<double>(
      begin: 1.0,
      end: 0.97,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
  }

  Widget _buildAnimatedCard() {
    return MouseRegion(
      onEnter: (_) {
        setState(() => _isHovered = true);
        _controller.forward();
      },
      onExit: (_) {
        setState(() => _isHovered = false);
        _controller.reverse();
      },
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnim.value,
            child: child,
          );
        },
        child: _buildCardContent(),
      ),
    );
  }
}
```

#### 1.1.5 Градиенты и Цвета
```dart
LinearGradient _getCardGradient() {
  final baseColors = [
    const Color(0xFF1A1A1A),
    const Color(0xFF2D2D2D),
  ];

  switch (newsItem.sentiment) {
    case SentimentType.positive:
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          ...baseColors,
          Colors.green.withOpacity(0.1),
        ],
      );
    case SentimentType.negative:
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          ...baseColors,
          Colors.red.withOpacity(0.1),
        ],
      );
    default:
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: baseColors,
      );
  }
}
```

#### 1.1.6 Индикаторы Настроения
```dart
Widget _buildSentimentIndicator() {
  final sentiment = newsItem.sentiment;
  return Container(
    padding: const EdgeInsets.all(8),
    decoration: BoxDecoration(
      color: Colors.black.withOpacity(0.5),
      borderRadius: BorderRadius.circular(8),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          sentiment == SentimentType.positive
              ? Icons.trending_up
              : sentiment == SentimentType.negative
                  ? Icons.trending_down
                  : Icons.trending_flat,
          color: sentiment == SentimentType.positive
              ? Colors.green
              : sentiment == SentimentType.negative
                  ? Colors.red
                  : Colors.blue,
          size: 16,
        ),
        const SizedBox(width: 4),
        Text(
          sentiment.toString().split('.').last,
          style: TextStyle(
            color: sentiment == SentimentType.positive
                ? Colors.green
                : sentiment == SentimentType.negative
                    ? Colors.red
                    : Colors.blue,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    ),
  ).animate()
    .fadeIn(duration: 300.ms)
    .scale(begin: const Offset(0.8, 0.8), end: const Offset(1, 1));
}
```

#### 1.1.7 Теги и Метаданные
```dart
Widget _buildTags() {
  return Wrap(
    spacing: 8,
    runSpacing: 8,
    children: newsItem.tags.map((tag) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          tag,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      );
    }).toList(),
  );
}
```

### 1.2 Новостной Экран

#### 1.2.1 Структура Экрана

##### Основной Контейнер
```dart
class NewsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xFF000000), // глубокий черный
              Color(0xFF181818), // чуть светлее, но все еще черный
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            // Боковая панель навигации
            SideNavigationPanel(
              currentIndex: 0,
              onTap: (index) {
                // Обработка навигации
              },
            ),
            // Основной контент
            Expanded(
              child: _buildNewsContent(),
            ),
          ],
        ),
      ),
    );
  }
}
```

##### Основной Контент
```dart
Widget _buildNewsContent() {
  return Column(
    children: [
      // Верхняя панель с фильтрами
      _buildFilterPanel(),
      // Основная сетка новостей
      Expanded(
        child: _buildNewsGrid(),
      ),
    ],
  );
}

Widget _buildFilterPanel() {
  return Container(
    padding: const EdgeInsets.all(16),
    child: Row(
      children: [
        // Поиск
        Expanded(
          child: TextField(
            decoration: InputDecoration(
              hintText: 'Поиск новостей...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        // Фильтры по категориям
        _buildCategoryFilter(),
        const SizedBox(width: 16),
        // Фильтры по тегам
        _buildTagFilter(),
      ],
    ),
  );
}
```

#### 1.2.2 Распределение Размеров Карточек

##### Алгоритм Распределения
```dart
// Контролируемое распределение размеров
int cardCount = filteredNews.length;
int largeCount = (cardCount * 0.25).round();    // 25% больших карточек
int compactCount = (cardCount * 0.3).round();   // 30% компактных карточек
int standardCount = cardCount - largeCount - compactCount; // Остальные стандартные

List<String> distribution = [];
distribution.addAll(List.filled(largeCount, 'large'));
distribution.addAll(List.filled(standardCount, 'standard'));
distribution.addAll(List.filled(compactCount, 'compact'));
distribution.shuffle(Random(cardCount));
```

##### Размеры Карточек
```dart
class CardDimensions {
  // Высота карточек (оптимизированы для лучшего заполнения контентом)
  static const Map<CardSize, double> heights = {
    CardSize.compact: 150,  // Компактная (было 180)
    CardSize.standard: 200, // Стандартная (было 240)
    CardSize.large: 280,    // Большая (было 320)
    CardSize.tall: 380,     // Высокая (было 400)
  };

  // Высота изображений
  static const Map<CardSize, double> imageHeights = {
    CardSize.compact: 80,   // Компактная (было 120)
    CardSize.standard: 120, // Стандартная (было 160)
    CardSize.large: 180,    // Большая (было 240)
    CardSize.tall: 250,     // Высокая (было 300)
  };

  // Отступы
  static const double padding = 16.0;
  static const double spacing = 16.0;
}
```

#### 1.2.3 Адаптивная Сетка

##### Определение Размеров
```dart
// Определяем количество колонок и размеры по ширине экрана
int columns = 3;
double largeH = 280, standardH = 200, compactH = 150; // обновлены размеры
double width = MediaQuery.of(context).size.width;

if (width < 768) {
  columns = 2;
  largeH = standardH = 200; // обновлен размер
  compactH = 150; // обновлен размер
}
```

##### Реализация Сетки
```dart
return MasonryGridView.count(
  crossAxisCount: columns,
  mainAxisSpacing: 16,
  crossAxisSpacing: 16,
  itemCount: filteredNews.length,
  itemBuilder: (context, index) {
    final newsItem = filteredNews[index];
    final sizeType = distribution[index];
    CardSize cardSize;
    double cardHeight;
    
    switch (sizeType) {
      case 'large':
        cardSize = CardSize.large;
        cardHeight = largeH;
        break;
      case 'compact':
        cardSize = CardSize.compact;
        cardHeight = compactH;
        break;
      default:
        cardSize = CardSize.standard;
        cardHeight = standardH;
    }
    
    return SizedBox(
      height: cardHeight,
      child: UltraGradientNewsCard(
        newsItem: newsItem,
        size: cardSize,
        onTap: () {
          showNewsDetailModal(
            context: context,
            newsItem: newsItem,
          );
        },
      ),
    );
  },
);
```

#### 1.2.4 Фильтрация и Категории

##### Категории Новостей
```dart
enum NewsCategory {
  crypto,  // Криптовалюты
  stocks,  // Акции
  whales,  // Крупные игроки
  general  // Общие новости
}
```

##### Фильтрация по Категориям
```dart
// Фильтрация новостей по выбранной категории
List<NewsItem> filteredNews = newsProvider.filteredNews;
switch (selectedCategory) {
  case 1:
    filteredNews = filteredNews.where((n) => n.category == NewsCategory.crypto).toList();
    break;
  case 2:
    filteredNews = filteredNews.where((n) => n.category == NewsCategory.stocks).toList();
    break;
  case 3:
    filteredNews = filteredNews.where((n) => n.category == NewsCategory.whales).toList();
    break;
  default:
    break;
}
```

##### Фильтрация по Тегам
```dart
// Собираем уникальные теги из всех новостей
final tags = newsProvider.filteredNews
    .expand((item) => item.tags)
    .toSet()
    .toList();

// Фильтрация по выбранным тегам
if (selectedTags.isNotEmpty) {
  filteredNews = filteredNews.where((news) =>
    news.tags.any((tag) => selectedTags.contains(tag))
  ).toList();
}
```

##### Поиск по Тексту
```dart
// Поиск по заголовку и описанию
if (searchQuery.isNotEmpty) {
  filteredNews = filteredNews.where((news) =>
    news.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
    news.description.toLowerCase().contains(searchQuery.toLowerCase())
  ).toList();
}
```

#### 1.2.5 Особенности Реализации

##### Адаптивность
- Автоматическое определение размера экрана
- Переключение между 3 и 2 колонками
- Корректировка размеров карточек
- Оптимизация отступов и интервалов

##### Анимации
```dart
// Анимация появления карточек
Widget _buildAnimatedCard(NewsItem newsItem, CardSize size) {
  return AnimatedContainer(
    duration: const Duration(milliseconds: 300),
    curve: Curves.easeInOut,
    child: UltraGradientNewsCard(
      newsItem: newsItem,
      size: size,
      onTap: () => _showNewsDetail(newsItem),
    ),
  );
}

// Анимация при наведении
Widget _buildHoverEffect(Widget child) {
  return MouseRegion(
    onEnter: (_) => _controller.forward(),
    onExit: (_) => _controller.reverse(),
    child: ScaleTransition(
      scale: _scaleAnim,
      child: child,
    ),
  );
}
```

##### Оптимизация Производительности
- Кэширование изображений
- Ленивая загрузка контента
- Виртуализация списка
- Оптимизация перестроений

##### Обработка Ошибок
```dart
// Обработка ошибок загрузки изображений
Widget _buildNewsImage(String imageUrl) {
  return Image.network(
    imageUrl,
    fit: BoxFit.cover,
    errorBuilder: (context, error, stackTrace) {
      return Container(
        color: Colors.grey[900],
        child: const Center(
          child: Icon(Icons.image_not_supported, size: 50, color: Colors.white),
        ),
      );
    },
  );
}

// Обработка ошибок загрузки данных
Widget _buildErrorState() {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.error_outline, size: 50, color: Colors.red),
        const SizedBox(height: 16),
        const Text('Ошибка загрузки новостей'),
        const SizedBox(height: 8),
        ElevatedButton(
          onPressed: () => _loadNews(),
          child: const Text('Повторить'),
        ),
      ],
    ),
  );
}
```

##### Состояния Загрузки
```dart
Widget _buildLoadingState() {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const CircularProgressIndicator(),
        const SizedBox(height: 16),
        const Text('Загрузка новостей...'),
      ],
    ),
  );
}

Widget _buildEmptyState() {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.newspaper, size: 50, color: Colors.grey),
        const SizedBox(height: 16),
        const Text('Новости не найдены'),
        const SizedBox(height: 8),
        Text(
          'Попробуйте изменить параметры поиска',
          style: TextStyle(color: Colors.grey[600]),
        ),
      ],
    ),
  );
}
```

### 1.3 Детальный Экран Новости (NewsDetailModal)

#### 1.3.1 Структура Модального Окна
```dart
class NewsDetailModal extends StatefulWidget {
  final NewsDetailData newsDetail;
  final VoidCallback onClose;

  const NewsDetailModal({
    Key? key,
    required this.newsDetail,
    required this.onClose,
  }) : super(key: key);
}
```

#### 1.3.2 Дизайн и Анимации
```dart
Widget _buildModalContent() {
  return GestureDetector(
    onPanUpdate: (details) {
      if (details.delta.dy > 0) {
        _swipeProgress += details.delta.dy / 300;
        setState(() {});
      }
    },
    onPanEnd: (details) {
      if (_swipeProgress > 0.3) {
        _closeModal();
      } else {
        setState(() => _swipeProgress = 0);
      }
    },
    child: AnimatedContainer(
      duration: const Duration(milliseconds: 350),
      curve: Curves.easeOutCubic,
      width: screenWidth * 0.95,
      constraints: BoxConstraints(
        maxWidth: 600,
        maxHeight: screenHeight * 0.85,
      ),
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF232A34), // серый
            Color(0xFF3A5F68), // серо-берюзовый
            Color(0xFF1A1F2E), // тёмный низ
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.18),
            blurRadius: 30,
            offset: const Offset(0, 15),
          ),
        ],
        border: Border.all(
          color: Colors.white.withOpacity(0.13),
          width: 1.5,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          Flexible(
            child: _buildRichContent(),
          ),
        ],
      ),
    ),
  );
}
```

#### 1.3.3 Заголовок и Метаданные
```dart
Widget _buildHeader() {
  return Container(
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      border: Border(
        bottom: BorderSide(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
    ),
    child: Row(
      children: [
        // Индикатор сентимента
        _buildSentimentChart(widget.newsDetail.sentiment.sentiment, 28),
        const SizedBox(width: 12),
        // Источник и дата
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.newsDetail.source.toUpperCase(),
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _formatTimeAgo(widget.newsDetail.publishedAt),
                style: const TextStyle(
                  color: Colors.white54,
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ),
        // Кнопка закрытия
        GestureDetector(
          onTap: _closeModal,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.close,
              color: Colors.white70,
              size: 20,
            ),
          ),
        ),
      ],
    ),
  );
}
```

#### 1.3.4 Контент и Анализ
```dart
Widget _buildRichContent() {
  return SingleChildScrollView(
    padding: const EdgeInsets.all(24),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Заголовок
        Text(
          widget.newsDetail.title,
          style: const TextStyle(
            fontSize: 30,
            fontWeight: FontWeight.bold,
            fontFamily: 'SF Pro Display',
            color: Colors.white,
            letterSpacing: 0.2,
            height: 1.18,
          ),
        ),
        const SizedBox(height: 16),
        // Кнопка анализа
        SizedBox(
          width: 180,
          child: ElevatedButton.icon(
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF232A34),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12)
              ),
              padding: const EdgeInsets.symmetric(
                vertical: 12,
                horizontal: 16
              ),
            ),
            icon: const Icon(Icons.analytics_outlined, size: 22),
            label: Text(_isAnalyzing ? 'Анализ...' : 'Анализ новости'),
            onPressed: _isAnalyzing ? null : _analyzeNews,
          ),
        ),
        // Результаты анализа
        if (_analysisResult != null) ...[
          const SizedBox(height: 18),
          _buildAnalysisResult(_analysisResult!),
        ],
      ],
    ),
  );
}
```

#### 1.3.5 Анимации
```dart
void _setupAnimations() {
  _animationController = AnimationController(
    duration: const Duration(milliseconds: 480),
    vsync: this,
  );
  _scaleAnimation = Tween<double>(begin: 0.88, end: 1.0).animate(
    CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.08, 1.0, curve: Curves.easeOutExpo),
    ),
  );
  _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
    CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic
    ),
  );
}
```

### 1.4 Новостные Блоки (UltraGradientNewsCard)

#### 1.4.1 Типы и Размеры Карточек
```dart
enum CardSize { compact, standard, large, tall }

class CardDimensions {
  static const Map<CardSize, double> heights = {
    CardSize.compact: 180,
    CardSize.standard: 240,
    CardSize.large: 320,
    CardSize.tall: 400,
  };

  static const Map<CardSize, double> imageHeights = {
    CardSize.compact: 120,
    CardSize.standard: 160,
    CardSize.large: 240,
    CardSize.tall: 300,
  };
}
```

#### 1.4.2 Структура Карточки
```dart
class UltraGradientNewsCard extends StatefulWidget {
  final NewsItem newsItem;
  final VoidCallback onTap;
  final CardSize size;
  final bool isFeatured;

  const UltraGradientNewsCard({
    Key? key,
    required this.newsItem,
    required this.onTap,
    this.size = CardSize.standard,
    this.isFeatured = false,
  }) : super(key: key);
}
```

#### 1.4.3 Градиентный Дизайн
```dart
Widget _buildGradientBackground() {
  return Container(
    decoration: BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: _getGradientColors(),
      ),
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: 10,
          offset: const Offset(0, 4),
        ),
      ],
    ),
  );
}

List<Color> _getGradientColors() {
  switch (widget.newsItem.sentiment) {
    case SentimentType.positive:
      return [
        const Color(0xFF1A2F38),
        const Color(0xFF2A4A4A),
        const Color(0xFF1A2F38),
      ];
    case SentimentType.negative:
      return [
        const Color(0xFF2A1F1F),
        const Color(0xFF3A2A2A),
        const Color(0xFF2A1F1F),
      ];
    default:
      return [
        const Color(0xFF1A1F2E),
        const Color(0xFF2A2F3E),
        const Color(0xFF1A1F2E),
      ];
  }
}
```

#### 1.4.4 Анимации и Эффекты
```dart
class _UltraGradientNewsCardState extends State<UltraGradientNewsCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnim;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: 200.ms,
    );
    _scaleAnim = Tween<double>(
      begin: 1.0,
      end: 0.97,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
  }

  Widget _buildHoverEffect() {
    return MouseRegion(
      onEnter: (_) {
        setState(() => _isHovered = true);
        _controller.forward();
      },
      onExit: (_) {
        setState(() => _isHovered = false);
        _controller.reverse();
      },
      child: ScaleTransition(
        scale: _scaleAnim,
        child: widget.child,
      ),
    );
  }
}
```

#### 1.4.5 Индикаторы Сентимента
```dart
Widget _buildSentimentIndicator() {
  IconData icon;
  Color color;
  switch (widget.newsItem.sentiment) {
    case SentimentType.positive:
      icon = Icons.trending_up;
      color = const Color(0xFF3ED598);
      break;
    case SentimentType.negative:
      icon = Icons.trending_down;
      color = const Color(0xFFFF4D4F);
      break;
    default:
      icon = Icons.trending_flat;
      color = const Color(0xFF60A5FA);
  }
  
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    decoration: BoxDecoration(
      color: color.withOpacity(0.1),
      borderRadius: BorderRadius.circular(4),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 4),
        Text(
          _getSentimentText(),
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    ),
  );
}
```

### 1.5 Боковая Навигационная Панель

#### 1.5.1 Структура Панели
```dart
class SideNavigationPanel extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const SideNavigationPanel({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 240,
      decoration: BoxDecoration(
        color: const Color(0xFF1A1F2E),
        border: Border(
          right: BorderSide(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          _buildLogo(),
          const SizedBox(height: 32),
          _buildNavigationItems(),
          const Spacer(),
          _buildUserSection(),
        ],
      ),
    );
  }
}
```

#### 1.5.2 Навигационные Элементы
```dart
Widget _buildNavigationItems() {
  return Column(
    children: [
      _buildNavItem(
        icon: Icons.newspaper,
        label: 'Новости',
        index: 0,
      ),
      _buildNavItem(
        icon: Icons.show_chart,
        label: 'Рынки',
        index: 1,
      ),
      _buildNavItem(
        icon: Icons.school,
        label: 'Обучение',
        index: 2,
      ),
      _buildNavItem(
        icon: Icons.bookmark,
        label: 'Сохранённое',
        index: 3,
      ),
      _buildNavItem(
        icon: Icons.person,
        label: 'Профиль',
        index: 4,
      ),
    ],
  );
}

Widget _buildNavItem({
  required IconData icon,
  required String label,
  required int index,
}) {
  final isSelected = currentIndex == index;
  
  return InkWell(
    onTap: () => onTap(index),
    child: Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white.withOpacity(0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: isSelected ? Colors.white : Colors.white70,
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            label,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.white70,
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    ),
  );
}
```

#### 1.5.3 Секция Пользователя
```dart
Widget _buildUserSection() {
  return Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      border: Border(
        top: BorderSide(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
    ),
    child: Row(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundColor: Colors.white.withOpacity(0.1),
          child: const Icon(
            Icons.person,
            color: Colors.white70,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Имя Пользователя',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '<EMAIL>',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.6),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          icon: const Icon(
            Icons.settings,
            color: Colors.white70,
            size: 20,
          ),
          onPressed: () {
            // Обработка нажатия на настройки
          },
        ),
      ],
    ),
  );
}
```

## 2. Серверная Часть и AI Анализ

### 2.1 Промпты для Анализа

#### 2.1.1 Анализ Новостей
```dart
// Промпт для анализа новости
final analysisPrompt = '''
You are a professional crypto market analyst. Analyze the following news and provide insights in JSON format:

{
  "summary": "Brief summary of the news",
  "marketImpact": {
    "strength": "strong/medium/weak",
    "direction": "bullish/bearish/neutral",
    "rationale": "Detailed explanation",
    "affectedAssets": [
      {
        "ticker": "BTC",
        "type": "crypto",
        "impactMagnitude": "high/medium/low"
      }
    ]
  },
  "forecast": {
    "baseScenario": "Most likely outcome",
    "alternativeScenario": "Alternative outcome",
    "technicalLevels": {
      "support": ["level1", "level2"],
      "resistance": ["level1", "level2"]
    }
  },
  "interestingFact": "An interesting fact or historical context"
}
''';
```

#### 2.1.2 Определение Сентимента
```dart
// Промпт для определения сентимента
final sentimentPrompt = '''
You are a sentiment analyzer. Analyze the following text and respond with exactly one word: "positive", "negative", or "neutral".
''';
```

#### 2.1.3 Суммаризация
```dart
// Промпт для суммаризации
final summaryPrompt = '''
You are a professional news summarizer. Create a concise summary of the following text, focusing on key points and financial implications.
''';
```

### 2.2 Серверная Реализация

#### 2.2.1 Обновление Кэша Новостей
```javascript
async function updateNewsCache() {
  for (const article of articles) {
    try {
      // Парсинг статьи
      console.log(`[AUTO] [PARSER] Парсим: ${article.title}`);
      const articleResp = await axios.get(article.url);
      const parsed = unfluff(articleResp.data);
      article.full_text = parsed.text || '';
      
      // Определение тегов
      article.tags = determineTags(article);
      
      // AI анализ
      article.summary = await deepSeekAnalyze(
        article.full_text,
        summaryPrompt,
        150
      );
      
      article.sentiment = await deepSeekAnalyze(
        article.full_text,
        sentimentPrompt,
        10
      );
      
      article.analysis = await deepSeekAnalyze(
        article.full_text,
        analysisPrompt,
        200
      );
      
      // Сокращение текста если нужно
      if ((article.full_text || '').split(' ').length > 100) {
        article.full_text = await aiCondensedText(article.full_text);
      }
    } catch (e) {
      console.error(`[AUTO] [ERROR] Ошибка обработки статьи: ${e.message}`);
    }
  }
}
```

#### 2.2.2 Определение Тегов
```javascript
function determineTags(article) {
  const tags = new Set();
  
  // Базовые теги
  tags.add('crypto');
  tags.add('stocks');
  
  // Анализ контента
  const content = (article.title + ' ' + article.description).toLowerCase();
  
  // Категории
  if (content.includes('bitcoin') || content.includes('btc')) tags.add('bitcoin');
  if (content.includes('ethereum') || content.includes('eth')) tags.add('ethereum');
  if (content.includes('solana') || content.includes('sol')) tags.add('solana');
  
  // Типы новостей
  if (content.includes('whale') || content.includes('large transaction')) tags.add('whales');
  if (content.includes('ai') || content.includes('artificial intelligence')) tags.add('ai');
  if (content.includes('regulation') || content.includes('sec')) tags.add('regulation');
  
  // Важность
  if (content.includes('breaking') || content.includes('urgent')) tags.add('breaking');
  
  return Array.from(tags);
}
```

#### 2.2.3 Интеграция с DeepSeek
```javascript
async function deepSeekAnalyze(text, prompt, maxTokens) {
  try {
    const response = await axios.post('https://api.deepseek.com/v1/chat/completions', {
      model: 'deepseek-chat',
      messages: [
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: text
        }
      ],
      temperature: 0.3,
      max_tokens: maxTokens
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data.choices[0].message.content;
  } catch (e) {
    console.error(`[DEEPSEEK] Ошибка: ${e.message}`);
    return null;
  }
}
```

### 2.3 Обработка Новостей на Клиенте

#### 2.3.1 Получение Новостей
```dart
Future<List<NewsItem>> getAllNews({int pageSize = 10, String category = 'business'}) async {
  try {
    final Map<String, String> queryParams = {
      'pageSize': pageSize.toString(),
      'category': category,
    };
    final Uri uri = Uri.parse(_backendUrl).replace(queryParameters: queryParams);
    final response = await http.get(uri);
    
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      if (data['status'] == 'success' && data['articles'] != null) {
        final List<dynamic> articles = data['articles'];
        return articles.map((item) => NewsItem(
          id: item['url'] ?? '',
          title: item['title'] ?? '',
          description: item['description'] ?? '',
          imageUrl: item['image'] ?? '',
          publishedAt: DateTime.tryParse(item['publishedAt'] ?? '') ?? DateTime.now(),
          source: item['source'] ?? '',
          url: item['url'] ?? '',
          sentiment: _parseSentiment(item['sentiment']),
          tags: (item['tags'] as List?)?.map((e) => e.toString()).toList() ?? [],
          category: _determineCategory(item['title'] ?? '', item['description'] ?? ''),
          importanceLevel: item['importanceLevel'] ?? 1,
          content: item['full_text'] ?? '',
          summary: item['summary'] ?? '',
          analysis: item['analysis'] ?? '',
        )).toList();
      }
    }
    throw Exception('Failed to fetch news');
  } catch (e) {
    print('Error fetching news: $e');
    throw Exception('Failed to fetch news: $e');
  }
}
```

#### 2.3.2 Анализ Новостей
```dart
Future<NewsAnalysisResult?> analyzeNews(NewsItem newsItem) async {
  try {
    final prompt = await _buildPrompt(newsItem);
    
    final response = await http.post(
      Uri.parse(_apiUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
      },
      body: jsonEncode({
        'model': 'deepseek-chat',
        'messages': [
          {'role': 'system', 'content': 'You are a professional crypto market analyst.'},
          {'role': 'user', 'content': prompt},
        ],
        'temperature': 0.7,
        'max_tokens': 2048,
      }),
    );
    
    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(response.body);
      final content = jsonResponse['choices'][0]['message']['content'] as String;
      
      try {
        final jsonString = _extractJsonFromText(content);
        final Map<String, dynamic> jsonResult = jsonDecode(jsonString);
        return NewsAnalysisResult.fromJson(jsonResult);
      } catch (e) {
        print('Error parsing AI response: $e');
        return null;
      }
    }
    return null;
  } catch (e) {
    print('Error analyzing news: $e');
    return null;
  }
}
```

## 3. Анализ Новостей

### 3.1 Компоненты Анализа
1. **Суммаризация**:
   - Краткое содержание
   - Ключевые моменты
   - Финансовые последствия

2. **Анализ Рыночного Влияния**:
   - Направление (позитивное/негативное)
   - Обоснование
   - Ключевые факторы

3. **Прогноз**:
   - Базовый сценарий
   - Альтернативный сценарий
   - Технические уровни

### 3.2 Интеграция с AI
- **DeepSeek API**:
  - Модель: deepseek-chat
  - Температура: 0.3
  - Максимальные токены: 150-200
  - Специализированные промпты для каждого типа анализа

### 3.3 Кэширование Результатов
- Локальное хранение результатов анализа
- Периодическое обновление
- Обработка ошибок и fallback-решения

## 4. Технические Детали

### 4.1 Модели Данных
```dart
class NewsItem {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final DateTime publishedAt;
  final String source;
  final String url;
  final SentimentType sentiment;
  final List<String> tags;
  final NewsCategory category;
  final String content;
  final String summary;
  final String analysis;
}

class NewsAnalysisResult {
  final String summary;
  final MarketImpact marketImpact;
  final Forecast forecast;
  final String interestingFact;
}
```

### 4.2 Зависимости
```yaml
dependencies:
  flutter: ^3.19.0
  http: ^1.1.0
  shared_preferences: ^2.2.2
  crypto: ^3.0.3
  unfluff: ^1.0.0
```

## 5. Безопасность

### 5.1 API Ключи
- Безопасное хранение в .env файле
- Ротация ключей
- Ограничение доступа по IP

### 5.2 Валидация Данных
- Проверка входных данных
- Санитизация HTML
- Ограничение размера запросов

## 6. Мониторинг и Логирование

### 6.1 Метрики
- Время ответа API
- Успешность парсинга
- Качество анализа
- Использование кэша

### 6.2 Логи
- Ошибки парсинга
- Проблемы с API
- Статистика использования
- Аудит доступа 