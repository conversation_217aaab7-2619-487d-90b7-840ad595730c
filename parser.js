const axios = require('axios');
const cheerio = require('cheerio');

/**
 * Парсит публичную web-версию Telegram канала (https://t.me/s/...) и
 * возвращает массив последних текстовых сообщений.
 * @param {string} group Telegram username канала (без @)
 * @param {number} limit Сколько сообщений вернуть (по умолчанию 20)
 * @returns {Promise<string[]>}
 */
async function parseTelegram(group, limit = 20) {
  try {
    const url = `https://t.me/s/${group}`;
    const { data } = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; Discordbot/2.0; +https://discordapp.com)' // некоторым сайтам нужен UA
      },
      timeout: 10000,
    });

    const $ = cheerio.load(data);
    const messages = [];

    // Основной селектор текста сообщений
    $('div.tgme_widget_message_text').each((i, el) => {
      if (i >= limit) return false; // break out of loop
      const text = $(el).text().trim();
      if (text) messages.push(text);
    });

    // Fallback: если ничего не нашлось, пробуем другой селектор (иногда телеграм меняет класс)
    if (messages.length === 0) {
      $('div.tgme_widget_message').each((i, el) => {
        if (i >= limit) return false;
        const text = $(el).find('div.tgme_widget_message_text').text().trim();
        if (text) messages.push(text);
      });
    }

    console.log(`📝 [${group}] Parsed ${messages.length} messages`);

    return messages.slice(0, limit); // гарантируем ограничение
  } catch (err) {
    console.error(`❌ Ошибка при парсинге ${group}:`, err.message);
    return [];
  }
}

module.exports = { parseTelegram };