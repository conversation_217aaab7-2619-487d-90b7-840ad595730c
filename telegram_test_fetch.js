// telegram_test_fetch.js
// Быстрый тест: вывести 10 последних сообщений выбранного чата.
// ----------------------------------------------
// Использует те же переменные окружения, что и основной сервер.
//   TG_API_ID, TG_API_HASH, TG_STRING_SESSION (опц.)
// Запуск:
//   node telegram_test_fetch.js <chat_username_or_id>
// ----------------------------------------------

const { TelegramClient } = require('telegram');
const { StringSession } = require('telegram/sessions');
const input = require('input');
require('dotenv').config();

const apiId = parseInt(process.env.TG_API_ID || '0', 10);
const apiHash = process.env.TG_API_HASH || '';
const stringSession = new StringSession(process.env.TG_STRING_SESSION || '');

if (!apiId || !apiHash) {
  console.error('❌ TG_API_ID и TG_API_HASH должны быть заданы');
  process.exit(1);
}

const chat = process.argv[2];
if (!chat) {
  console.error('Укажите username/id чата: node telegram_test_fetch.js <chat>');
  process.exit(1);
}

(async () => {
  const client = new TelegramClient(stringSession, apiId, apiHash, { connectionRetries: 5 });

  await client.start({
    phoneNumber: async () => await input.text('Введите номер телефона: '),
    password: async () => await input.text('Введите 2FA пароль (если есть): '),
    phoneCode: async () => await input.text('Введите код из Telegram: '),
    onError: (e) => console.error(e),
  });

  console.log(`✅ Получаем 10 сообщений из ${chat}...`);
  const messages = await client.getMessages(chat, { limit: 10 });
  messages.reverse().forEach((msg, i) => {
    console.log(`${i + 1}. ${msg.message}`);
  });

  if (!process.env.TG_STRING_SESSION) {
    console.log('\nℹ️  Сохраните TG_STRING_SESSION для будущих запусков:');
    console.log(client.session.save());
  }

  await client.disconnect();
})(); 