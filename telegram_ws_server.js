// telegram_ws_server.js
// -------------------------------------------------------
// Реализация на Node.js: подключается к Telegram (GramJS),
// слушает указанные группы и ретранслирует сообщения через
// локальный WebSocket-сервер (порт 8765) для Flutter-клиента.
// -------------------------------------------------------
// Установка зависимостей:
//   npm install gramjs ws input
// Первый запуск потребует ввести номер телефона и код.
// После успешной авторизации скрипт выведет строку сессии –
// сохраните её в переменную окружения TG_STRING_SESSION, чтобы
// больше не вводить код.
// -------------------------------------------------------
// env-переменные, которые нужно задать (пример в .env):
//   TG_API_ID=123456
//   TG_API_HASH=abcd1234efgh5678
//   TG_STRING_SESSION="1A2B3C..."   # после первого входа
//   TG_GROUPS=whale_alert_io,WhaleSniper  # через запятую
// -------------------------------------------------------

const { TelegramClient } = require('telegram');
const { StringSession } = require('telegram/sessions');
const input = require('input'); // небольшая util-ка для запроса данных в CLI
const WebSocket = require('ws');
require('dotenv').config();

const apiId = parseInt(process.env.TG_API_ID || '0', 10);
const apiHash = process.env.TG_API_HASH || '';
if (!apiId || !apiHash) {
  console.error('❌ Нужно установить TG_API_ID и TG_API_HASH в переменные окружения');
  process.exit(1);
}

const stringSession = new StringSession(process.env.TG_STRING_SESSION || ""); // пустая = первая авторизация
const groups = (process.env.TG_GROUPS || 'whale_alert_io,WhaleSniper')
  .split(',')
  .map((g) => g.trim())
  .filter(Boolean);

// ----------------------- Telegram клиент -----------------------
const client = new TelegramClient(stringSession, apiId, apiHash, {
  connectionRetries: 5,
});

const MOCK = process.env.MOCK === 'true';

if (MOCK) {
  console.log('⚠️  Работает в режиме MOCK, Telegram не используется');
  setInterval(() => {
    const demo = `[demo_group] Whale moved ${Math.floor(Math.random()*500)} BTC`;
    broadcast(demo);
  }, 1000);
  console.log('🔌 WebSocket сервер слушает на порту 8765');
  return;        // дальше код Telegram не выполняется
}

async function initTelegram() {
  console.log('📲 Подключаемся к Telegram...');
  await client.start({
    phoneNumber: async () => await input.text('Введите номер телефона: '),
    password: async () => await input.text('Введите 2FA пароль (если есть): '),
    phoneCode: async () => await input.text('Введите код из Telegram: '),
    onError: (err) => console.error(err),
  });

  console.log('✅ Telegram клиент запущен');
  // Сохраняем сессионную строку для последующих запусков
  if (!process.env.TG_STRING_SESSION) {
    console.log('ℹ️  Ваша строка сессии (TG_STRING_SESSION):');
    console.log(client.session.save());
  }
}

// ----------------------- WebSocket сервер ----------------------
const wss = new WebSocket.Server({ host: '0.0.0.0', port: 8765 });
const clients = new Set();

wss.on('connection', async (ws) => {
  clients.add(ws);
  console.log('🟢 Flutter подключился');

  // При подключении отдаём последние 20 сообщений каждой группы
  for (const chat of groups) {
    const messages = await client.getMessages(chat, { limit: 20 });
    for (const msg of messages.reverse()) {
      ws.send(`[${chat}] ${msg.message}`);
    }
  }

  ws.on('close', () => {
    clients.delete(ws);
    console.log('🔴 Flutter отключился');
  });
});

function broadcast(text) {
  for (const ws of clients) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(text);
    }
  }
}

// -------------------- Подписка на новые сообщения -------------
async function subscribeGroups() {
  console.log('🔔 Подписываемся на группы:', groups.join(', '));

  client.addEventHandler((update) => {
    const message = update.message;
    if (!message || !message.message) return;

    const chat = update.chat.username || update.chat.title || 'unknown';
    const text = `[${chat}] ${message.message}`;
    broadcast(text);
  }, {
    chats: groups,
  });
}

(async () => {
  try {
    await initTelegram();
    await subscribeGroups();
    console.log('🔌 WebSocket сервер слушает на порту 8765');
  } catch (err) {
    console.error('Ошибка запуска:', err);
    process.exit(1);
  }
})(); 