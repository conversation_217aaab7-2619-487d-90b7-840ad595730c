import 'package:flutter/material.dart';
import 'lib/services/news_service.dart';
import 'lib/models/sentiment_types.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 Тестирование загрузки sentiment из бэкенда...\n');
  
  final newsService = NewsService();
  
  try {
    // Тест 1: Загрузка всех новостей
    print('1. Загрузка всех новостей...');
    final allNews = await newsService.getAllNews(limit: 50);
    print('✅ Загружено ${allNews.length} новостей');
    
    // Тест 2: Проверка sentiment
    print('\n2. Анализ sentiment...');
    final positiveNews = allNews.where((n) => n.sentiment == SentimentType.positive).toList();
    final negativeNews = allNews.where((n) => n.sentiment == SentimentType.negative).toList();
    final neutralNews = allNews.where((n) => n.sentiment == SentimentType.neutral).toList();
    
    print('✅ Positive: ${positiveNews.length}');
    print('✅ Negative: ${negativeNews.length}');
    print('✅ Neutral: ${neutralNews.length}');
    
    // Тест 3: Примеры новостей с sentiment
    print('\n3. Примеры новостей:');
    
    if (positiveNews.isNotEmpty) {
      final positive = positiveNews.first;
      print('📈 POSITIVE: ${positive.title}');
      if (positive.sentimentData != null) {
        print('   Score: ${positive.sentimentData!.score}');
        print('   Confidence: ${positive.sentimentData!.confidence}');
      }
    }
    
    if (negativeNews.isNotEmpty) {
      final negative = negativeNews.first;
      print('📉 NEGATIVE: ${negative.title}');
      if (negative.sentimentData != null) {
        print('   Score: ${negative.sentimentData!.score}');
        print('   Confidence: ${negative.sentimentData!.confidence}');
      }
    }
    
    if (neutralNews.isNotEmpty) {
      final neutral = neutralNews.first;
      print('📊 NEUTRAL: ${neutral.title}');
      if (neutral.sentimentData != null) {
        print('   Score: ${neutral.sentimentData!.score}');
        print('   Confidence: ${neutral.sentimentData!.confidence}');
      }
    }
    
    // Тест 4: Проверка дубликатов
    print('\n4. Проверка дубликатов...');
    final titles = allNews.map((n) => n.title).toList();
    final uniqueTitles = titles.toSet();
    
    if (titles.length == uniqueTitles.length) {
      print('✅ Дубликатов не найдено');
    } else {
      print('⚠️ Найдено ${titles.length - uniqueTitles.length} дубликатов');
    }
    
    print('\n🎉 Все тесты завершены!');
    
  } catch (e) {
    print('❌ Ошибка: $e');
  }
}
