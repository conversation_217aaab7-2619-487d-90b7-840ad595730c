<!DOCTYPE html>
<html>
<head>
    <title>SSE Test</title>
</head>
<body>
    <h1>SSE Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="messages"></div>

    <script>
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        console.log('Starting SSE connection test...');
        
        const eventSource = new EventSource('http://localhost:4000/news/stream');
        
        eventSource.onopen = function(event) {
            console.log('SSE connection opened:', event);
            statusDiv.textContent = 'Connected!';
            statusDiv.style.color = 'green';
        };
        
        eventSource.onmessage = function(event) {
            console.log('SSE message received:', event.data);
            const messageDiv = document.createElement('div');
            messageDiv.textContent = `Message: ${event.data}`;
            messagesDiv.appendChild(messageDiv);
        };
        
        eventSource.onerror = function(event) {
            console.error('SSE error:', event);
            statusDiv.textContent = 'Connection error!';
            statusDiv.style.color = 'red';
        };
    </script>
</body>
</html>
