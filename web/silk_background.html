<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Silk Background</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body, html {
            height: 100%;
            overflow: hidden;
            background: transparent;
        }
        
        #silk-container {
            width: 100vw;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            z-index: -1;
        }
    </style>
</head>
<body>
    <div id="silk-container"></div>
    
    <!-- React and Three.js dependencies -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/three@0.150.0/build/three.min.js"></script>
    <script src="https://unpkg.com/@react-three/fiber@8.13.0/dist/index.umd.js"></script>
    
    <script type="text/babel">
        const { Canvas, useFrame, useThree } = ReactThreeFiber;
        const { forwardRef, useRef, useMemo, useLayoutEffect } = React;
        const { Color } = THREE;

        const hexToNormalizedRGB = (hex) => {
          hex = hex.replace("#", "");
          return [
            parseInt(hex.slice(0, 2), 16) / 255,
            parseInt(hex.slice(2, 4), 16) / 255,
            parseInt(hex.slice(4, 6), 16) / 255,
          ];
        };

        const vertexShader = `
        varying vec2 vUv;
        varying vec3 vPosition;

        void main() {
          vPosition = position;
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
        `;

        const fragmentShader = `
        varying vec2 vUv;
        varying vec3 vPosition;

        uniform float uTime;
        uniform vec3  uColor;
        uniform float uSpeed;
        uniform float uScale;
        uniform float uRotation;
        uniform float uNoiseIntensity;

        const float e = 2.71828182845904523536;

        float noise(vec2 texCoord) {
          float G = e;
          vec2  r = (G * sin(G * texCoord));
          return fract(r.x * r.y * (1.0 + texCoord.x));
        }

        vec2 rotateUvs(vec2 uv, float angle) {
          float c = cos(angle);
          float s = sin(angle);
          mat2  rot = mat2(c, -s, s, c);
          return rot * uv;
        }

        void main() {
          float rnd        = noise(gl_FragCoord.xy);
          vec2  uv         = rotateUvs(vUv * uScale, uRotation);
          vec2  tex        = uv * uScale;
          float tOffset    = uSpeed * uTime;

          tex.y += 0.03 * sin(8.0 * tex.x - tOffset);

          float pattern = 0.6 +
                          0.4 * sin(5.0 * (tex.x + tex.y +
                                           cos(3.0 * tex.x + 5.0 * tex.y) +
                                           0.02 * tOffset) +
                                   sin(20.0 * (tex.x + tex.y - 0.1 * tOffset)));

          vec4 col = vec4(uColor, 1.0) * vec4(pattern) - rnd / 15.0 * uNoiseIntensity;
          col.a = 1.0;
          gl_FragColor = col;
        }
        `;

        const SilkPlane = forwardRef(function SilkPlane({ uniforms }, ref) {
          const { viewport } = useThree();

          useLayoutEffect(() => {
            if (ref.current) {
              ref.current.scale.set(viewport.width, viewport.height, 1);
            }
          }, [ref, viewport]);

          useFrame((_, delta) => {
            ref.current.material.uniforms.uTime.value += 0.1 * delta;
          });

          return React.createElement('mesh', { ref },
            React.createElement('planeGeometry', { args: [1, 1, 1, 1] }),
            React.createElement('shaderMaterial', {
              uniforms,
              vertexShader,
              fragmentShader
            })
          );
        });

        const Silk = ({
          speed = 1.8,
          scale = 0.8,
          color = "#1C1C1C",
          noiseIntensity = 0.5,
          rotation = 0.48,
        }) => {
          const meshRef = useRef();

          const uniforms = useMemo(
            () => ({
              uSpeed: { value: speed },
              uScale: { value: scale },
              uNoiseIntensity: { value: noiseIntensity },
              uColor: { value: new Color(...hexToNormalizedRGB(color)) },
              uRotation: { value: rotation },
              uTime: { value: 0 },
            }),
            [speed, scale, noiseIntensity, color, rotation]
          );

          return React.createElement(Canvas, { dpr: [1, 2], frameloop: 'always' },
            React.createElement(SilkPlane, { ref: meshRef, uniforms })
          );
        };

        // Render the Silk component
        const container = document.getElementById('silk-container');
        const root = ReactDOM.createRoot(container);
        
        // Настройки которые можно менять:
        root.render(React.createElement(Silk, {
          speed: 1.8,        // Скорость анимации
          scale: 0.8,        // Масштаб
          color: "#1C1C1C",  // Цвет
          noiseIntensity: 0.5, // Интенсивность шума
          rotation: 0.48     // Поворот
        }));
    </script>
    
    <!-- Babel for JSX transformation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</body>
</html> 